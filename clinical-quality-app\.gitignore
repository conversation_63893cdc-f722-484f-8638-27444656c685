# See https://docs.github.com/get-started/getting-started-with-git/ignoring-files for more about ignoring files.
# Learn more about .gitignore: https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Compiled output
/dist
/tmp
/out-tsc
/bazel-out
*.class
*.py[cod]

# Node
/node_modules
node_modules/
npm-debug.log
yarn-error.log

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# Miscellaneous
/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings

# System files
.DS_Store
Thumbs.db

# Maven
target/

# Unit test reports
TEST*.xml

# Applications
*.app
*.exe
*.war
*.jar

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Log files
*.log

# Roo AI Assistant
# Exclude all Roo-related files and directories from version control
.roo/
.roomodes
memory-bank/
Docs/

# Angular build outputs
/dist/
/out-tsc/
/.angular/

# IDEs & editors
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln

# Node junk
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# OS-level nonsense
.DS_Store
Thumbs.db

# Roo files
.roo/
.roo*
.roomodes

# Environment & secrets
.env
.env.*
*.key
*.pem
*.crt
*.cert
*.p12
*.jks
*.keystore
*.pfx
firebase.json
service-account.json
secrets.json

# Data & PHI safety
src/assets/fake-charts/
src/assets/**/charts/
*.pdf

# Logs
logs
*.log
