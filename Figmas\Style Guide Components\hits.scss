@import "src/app/tokens.scss";

.hits_929-2688 {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 100%;
}
.frame-913_I929-2688-924-2187 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-end;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}
.head_I929-2688-924-2188 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: 100%;
}
.stack_I929-2688-924-2189 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
}
.text-label_I929-2688-924-2190 {
  color: #17181a;
  @include h-1-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.table_I929-2688-924-2191 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}
.columns_I929-2688-924-2192 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
}
.column_I929-2688-924-2193 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_I929-2688-924-2194 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: 100%;
}
.table-item_I929-2688-924-2195 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_I929-2688-924-2197 {
  color: $variable-collection-text-black;
  @include h-3-text;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2198 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2199 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_I929-2688-924-2200 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2201 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2202 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I929-2688-924-2203 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2204 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2205 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I929-2688-924-2206 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_I929-2688-924-2207 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_I929-2688-924-2208 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
}
.table-item_I929-2688-924-2209 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_I929-2688-924-2211 {
  color: $variable-collection-text-black;
  @include h-3-text;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2212 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2213 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_I929-2688-924-2214 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2215 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2216 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  width: 22px;
}
.text-label_I929-2688-924-2217 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 72px;
}

.table-item_I929-2688-924-2218 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2219 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  width: 22px;
}
.text-label_I929-2688-924-2220 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 72px;
}

.column_I929-2688-924-2221 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_I929-2688-924-2222 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
}
.table-item_I929-2688-924-2223 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_I929-2688-924-2225 {
  color: $variable-collection-text-black;
  @include h-3-text;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2226 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2227 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_I929-2688-924-2228 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2229 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2230 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I929-2688-924-2231 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 16px;
}

.table-item_I929-2688-924-2232 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2233 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I929-2688-924-2234 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_I929-2688-924-2235 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.header-item_I929-2688-924-2236 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  height: 40px;
  width: 100%;
}
.table-item_I929-2688-924-2237 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.text-label_I929-2688-924-2239 {
  color: $variable-collection-text-black;
  @include h-3-text;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2240 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff3f;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2241 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
}
.text-label_I929-2688-924-2242 {
  color: $variable-collection-link;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2243 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2244 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I929-2688-924-2245 {
  color: $variable-collection-link;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2246 {
  padding: 10px 8px 10px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.icon-text_I929-2688-924-2247 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_I929-2688-924-2248 {
  color: $variable-collection-link;
  @include body-text;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_I929-2688-924-2249 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  width: 216px;
}
.header-item_I929-2688-924-2250 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_I929-2688-924-2251 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
  width: 100%;
}
.text-label_I929-2688-924-2252 {
  color: $variable-collection-text-black;
  @include h-3-text;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_I929-2688-924-2254 {
  padding: 2px 0px 2px 0px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.comment-box_I929-2688-929-2649 {
  height: 30px;
  width: 100%;
}

.table-item_I929-2688-924-2258 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
  width: 100%;
}
.comment-box_I929-2688-929-2657 {
  height: 30px;
  width: 100%;
}

.table-item_I929-2688-924-2262 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
  width: 100%;
}
.comment-box_I929-2688-929-2661 {
  height: 30px;
  width: 100%;
}

.column_I929-2688-924-2266 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 60px;
}
.column_I929-2688-924-2267 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  width: 60px;
}
.header-item_I929-2688-924-2268 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: 100%;
}
.table-item_I929-2688-924-2269 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
  width: 100%;
}
.text-label_I929-2688-924-2270 {
  color: $variable-collection-text-black;
  @include h-3-text;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: 54px;
}

.table-item_I929-2688-924-2272 {
  padding: 18px 8px 18px 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 40px;
  width: 100%;
}
.check_I929-2688-924-2273 {
  position: relative;
  width: 16px;
  height: 16px;
}

.table-item_I929-2688-924-2274 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.check_I929-2688-924-2275 {
  position: relative;
  width: 16px;
  height: 16px;
}

.table-item_I929-2688-924-2276 {
  padding: 0px 8px 0px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 40px;
}
.check_I929-2688-924-2277 {
  position: relative;
  width: 16px;
  height: 16px;
}
