<div class="hits-container">
  
  <!-- Header -->
  <div *ngIf="showHeader" class="hits-header">
    <div class="hits-title">{{ title }}</div>
  </div>

  <!-- Hits Table -->
  <div class="hits-table">
    <div class="hits-table-columns">

      <!-- DoS Column -->
      <div class="hits-column dos-column">
        <div class="header-item">
          <div class="table-item">
            <div class="icon-text">
              <div class="label">DoS</div>
            </div>
          </div>
        </div>
        <div *ngFor="let hit of data; trackBy: trackByHitId; let i = index"
             class="table-item"
             [class.highlighted]="hit.id === selectedHitId">
          <div class="icon-text">
            <div class="label">{{ hit.dateOfService }}</div>
          </div>
        </div>
      </div>

      <!-- Sys Column -->
      <div class="hits-column sys-column">
        <div class="header-item">
          <div class="table-item">
            <div class="icon-text">
              <div class="label">Sys</div>
            </div>
          </div>
        </div>
        <div *ngFor="let hit of data; trackBy: trackByHitId; let i = index"
             class="table-item"
             [class.highlighted]="hit.id === selectedHitId">
          <div class="icon-text">
            <div class="label">{{ hit.systolic }}</div>
          </div>
        </div>
      </div>

      <!-- Dias Column -->
      <div class="hits-column dias-column">
        <div class="header-item">
          <div class="table-item">
            <div class="icon-text">
              <div class="label">Dias</div>
            </div>
          </div>
        </div>
        <div *ngFor="let hit of data; trackBy: trackByHitId; let i = index"
             class="table-item"
             [class.highlighted]="hit.id === selectedHitId">
          <div class="icon-text">
            <div class="label">{{ hit.diastolic }}</div>
          </div>
        </div>
      </div>

      <!-- Page Column -->
      <div class="hits-column page-column">
        <div class="header-item">
          <div class="table-item">
            <div class="icon-text">
              <div class="label">Page</div>
            </div>
          </div>
        </div>
        <div *ngFor="let hit of data; trackBy: trackByHitId; let i = index"
             class="table-item"
             [class.highlighted]="hit.id === selectedHitId">
          <div class="icon-text">
            <button type="button"
                    class="page-link"
                    (click)="onPageClick(hit)">
              <div class="label">{{ hit.page }}</div>
            </button>
          </div>
        </div>
      </div>

      <!-- Comment Column -->
      <div class="hits-column comment-column">
        <div class="header-item">
          <div class="table-item">
            <div class="icon-text">
              <div class="label">Comment</div>
            </div>
          </div>
        </div>
        <div *ngFor="let hit of data; trackBy: trackByHitId; let i = index"
             class="table-item"
             [class.highlighted]="hit.id === selectedHitId">
          <app-comment-box
            [ngModel]="hit.comment"
            [placeholder]="'Comment'"
            (ngModelChange)="onCommentChange(hit, $event)"
            [attr.aria-label]="'Comment for hit ' + (i + 1)"
            class="comment-box-component">
          </app-comment-box>
        </div>
      </div>

      <!-- Include Column -->
      <div class="hits-column include-column">
        <div class="header-item">
          <div class="table-item">
            <div class="icon-text">
              <div class="label">Include</div>
            </div>
          </div>
        </div>
        <div *ngFor="let hit of data; trackBy: trackByHitId; let i = index"
             class="table-item"
             [class.highlighted]="hit.id === selectedHitId">
          <div class="include-column-inner">
            <app-checkbox
              class="include-checkbox"
              [ngModel]="hit.include"
              (ngModelChange)="onIncludeChange(hit, $event)"
              [attr.aria-label]="'Include hit ' + (i + 1)">
            </app-checkbox>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
