<div class="component-test-container">
  <h1 class="page-title">Component Test Page</h1>
  <p class="page-description">This page showcases all implemented components with their different states and variations.</p>

  <!-- Buttons Section -->
  <section class="component-section">
    <h2 class="section-title">Buttons</h2>
    <p class="section-description">
      The following demos show the four button states from the Figma style guide: <strong>Inactive</strong>, <strong>Default</strong>, <strong>Hover</strong>, and <strong>Click</strong>.
      Static state demos show each state individually, while interactive demos show natural state transitions on hover and click.
    </p>
    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Primary Button - Inactive</h3>
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>
        <div class="component-demo figma-sized button-demo">
          <app-button
            variant="primary"
            [figmaExact]="true"
            figmaState="inactive"
          >Submit</app-button>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-button variant="primary" figmaState="inactive"&gt;Submit&lt;/app-button&gt;</code></pre>
        </div>
      </div>

      <div class="component-item">
        <h3 class="component-title">Primary Button - Default</h3>
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>
        <div class="component-demo figma-sized button-demo">
          <app-button
            variant="primary"
            [figmaExact]="true"
            figmaState="default"
            (click)="buttonClicked('primary-default')"
          >Submit</app-button>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-button variant="primary" figmaState="default"&gt;Submit&lt;/app-button&gt;</code></pre>
        </div>
      </div>
    </div>

    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Primary Button - Hover</h3>
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>
        <div class="component-demo figma-sized button-demo">
          <app-button
            variant="primary"
            [figmaExact]="true"
            figmaState="hover"
            (click)="buttonClicked('primary-hover')"
          >Submit</app-button>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-button variant="primary" figmaState="hover"&gt;Submit&lt;/app-button&gt;</code></pre>
        </div>
      </div>

      <div class="component-item">
        <h3 class="component-title">Primary Button - Click</h3>
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>
        <div class="component-demo figma-sized button-demo">
          <app-button
            variant="primary"
            [figmaExact]="true"
            figmaState="click"
            (click)="buttonClicked('primary-click')"
          >Submit</app-button>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-button variant="primary" figmaState="click"&gt;Submit&lt;/app-button&gt;</code></pre>
        </div>
      </div>
    </div>

    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Secondary Button - Default</h3>
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>
        <div class="component-demo figma-sized button-demo">
          <app-button
            variant="secondary"
            [figmaExact]="true"
            figmaState="default"
            (click)="buttonClicked('secondary-default')"
          >
            <app-refresh-icon color="default"></app-refresh-icon>Refresh charts
          </app-button>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-button variant="secondary" figmaState="default"&gt;Refresh charts&lt;/app-button&gt;</code></pre>
        </div>
      </div>

      <div class="component-item">
        <h3 class="component-title">Secondary Button - Hover</h3>
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>
        <div class="component-demo figma-sized button-demo">
          <app-button
            variant="secondary"
            [figmaExact]="true"
            figmaState="hover"
            (click)="buttonClicked('secondary-hover')"
          >
            <app-refresh-icon color="hover"></app-refresh-icon>Refresh charts
          </app-button>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-button variant="secondary" figmaState="hover"&gt;Refresh charts&lt;/app-button&gt;</code></pre>
        </div>
      </div>
    </div>

    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Secondary Button - Click</h3>
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "ButtonComponent"</div>
        <div class="component-demo figma-sized button-demo">
          <app-button
            variant="secondary"
            [figmaExact]="true"
            figmaState="click"
            (click)="buttonClicked('secondary-click')"
          >
            <app-refresh-icon color="click"></app-refresh-icon>Refresh charts
          </app-button>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-button variant="secondary" figmaState="click"&gt;Refresh charts&lt;/app-button&gt;</code></pre>
        </div>
      </div>
    </div>

    <!-- Interactive Demo Buttons -->
    <h3 class="subsection-title">Interactive Button Demos</h3>
    <p class="subsection-description">These buttons demonstrate natural state transitions. Hover and click to see the Figma-specified color changes.</p>
    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Interactive Primary Button Demo</h3>
        <div class="component-subtitle">Hover and click to see natural state transitions</div>
        <div class="component-demo figma-sized button-demo">
          <app-button
            variant="primary"
            [figmaExact]="true"
            (click)="buttonClicked('interactive-primary')"
          >Submit</app-button>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-button variant="primary" [figmaExact]="true"&gt;Submit&lt;/app-button&gt;</code></pre>
        </div>
      </div>

      <div class="component-item">
        <h3 class="component-title">Interactive Secondary Button Demo</h3>
        <div class="component-subtitle">Hover and click to see natural state transitions</div>
        <div class="component-demo figma-sized button-demo">
          <app-button
            variant="secondary"
            [figmaExact]="true"
            (click)="buttonClicked('interactive-secondary')"
          >
            <app-refresh-icon></app-refresh-icon>Refresh charts
          </app-button>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-button variant="secondary" [figmaExact]="true"&gt;Refresh charts&lt;/app-button&gt;</code></pre>
        </div>
      </div>
    </div>
  </section>

  <!-- Form Controls Section -->
  <section class="component-section">
    <h2 class="section-title">Form Controls</h2>
    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Checkbox</h3>
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "CheckboxComponent"</div>
        <div class="component-demo figma-sized checkbox-demo">
          <app-checkbox
            label="Checkbox"
            [(ngModel)]="isChecked"
          ></app-checkbox>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-checkbox label="Checkbox"&gt;&lt;/app-checkbox&gt;</code></pre>
        </div>
      </div>
    </div>

    <!-- New Form Controls Section -->
    <h3 class="subsection-title">Advanced Form Controls</h3>

    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Dropdown (Single Select)</h3>
        <div class="component-demo figma-sized form-demo">
          <app-dropdown
            label="Reasoning"
            placeholder="Select reasoning"
            [options]="reasoningOptions"
            [(ngModel)]="selectedValue"
            (selectionChange)="onDropdownChange($event)"
          ></app-dropdown>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-dropdown label="Reasoning" [options]="options"&gt;&lt;/app-dropdown&gt;</code></pre>
        </div>
      </div>

      <div class="component-item">
        <h3 class="component-title">Dropdown (Multi Select)</h3>
        <div class="component-demo figma-sized form-demo">
          <app-dropdown
            label="Multiple Reasoning"
            placeholder="Select multiple"
            [options]="reasoningOptions"
            [multiSelect]="true"
            [(ngModel)]="selectedMultipleValues"
            (selectionChange)="onMultiDropdownChange($event)"
          ></app-dropdown>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-dropdown [multiSelect]="true" [options]="options"&gt;&lt;/app-dropdown&gt;</code></pre>
        </div>
      </div>
    </div>

    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Calendar</h3>
        <div class="component-demo figma-sized form-demo">
          <app-calendar
            label="Date of Service"
            placeholder="Date of Service"
            [(ngModel)]="selectedDate"
            (dateChange)="onDateChange($event)"
          ></app-calendar>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-calendar label="Date of Service"&gt;&lt;/app-calendar&gt;</code></pre>
        </div>
      </div>

      <div class="component-item">
        <h3 class="component-title">Calendar (Required)</h3>
        <div class="component-demo figma-sized form-demo">
          <app-calendar
            label="Required Date"
            placeholder="Select date"
            [required]="true"
            [(ngModel)]="selectedDate"
            (dateChange)="onDateChange($event)"
          ></app-calendar>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-calendar [required]="true"&gt;&lt;/app-calendar&gt;</code></pre>
        </div>
      </div>
    </div>



    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Results</h3>
        <div class="component-demo figma-sized results-demo">
          <app-results
            title="Results"
            [(ngModel)]="resultsData"
            (dataChange)="onResultsDataChange($event)"
            (tabChange)="onResultsTabChange($event)"
          ></app-results>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-results title="Results"&gt;&lt;/app-results&gt;</code></pre>
        </div>
      </div>

      <div class="component-item">
        <h3 class="component-title">Results (Exclusions Tab)</h3>
        <div class="component-demo figma-sized results-demo">
          <app-results
            title="Findings"
            [(ngModel)]="exclusionsData"
          ></app-results>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-results title="Findings" [ngModel]="exclusionsData"&gt;&lt;/app-results&gt;</code></pre>
        </div>
      </div>
    </div>
  </section>



  <!-- Tables Section -->
  <section class="component-section">
    <h2 class="section-title">Tables</h2>
    <div class="component-row full-width">
      <div class="component-item full-width">
        <h3 class="component-title">Assigned Table (Dashboard Table)</h3>
        <div class="component-demo table-demo">
          <app-assigned-table [charts]="sampleCharts"></app-assigned-table>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-assigned-table [charts]="charts"&gt;&lt;/app-assigned-table&gt;</code></pre>
        </div>
      </div>
    </div>
  </section>

  <!-- Phase 2 Components Section -->
  <section class="component-section">
    <h2 class="section-title">Phase 2 Components</h2>

    <!-- Notes Component -->
    <div class="component-row">
      <div class="component-item">
        <h3 class="component-title">Notes</h3>
        <div class="component-subtitle">Style Guide: "Notes" | Implementation: "NotesComponent"</div>
        <div class="component-demo figma-sized form-demo">
          <app-notes
            label="Notes"
            placeholder="Notes"
            [(ngModel)]="notesText"
            (notesChange)="onNotesChange($event)"
          ></app-notes>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-notes label="Notes" [(ngModel)]="notesText"&gt;&lt;/app-notes&gt;</code></pre>
        </div>
      </div>

      <div class="component-item">
        <h3 class="component-title">Notes (Limited)</h3>
        <div class="component-subtitle">Style Guide: "Notes" | Implementation: "NotesComponent"</div>
        <div class="component-demo figma-sized form-demo">
          <app-notes
            label="Notes"
            placeholder="Notes"
            [maxLength]="100"
            [(ngModel)]="notesText"
          ></app-notes>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-notes [maxLength]="100"&gt;&lt;/app-notes&gt;</code></pre>
        </div>
      </div>
    </div>

    <!-- Demographics Component -->
    <div class="component-row demographics-row full-width">
      <div class="component-item full-width">
        <h3 class="component-title">Demographics</h3>
        <div class="component-demo figma-sized demographics-demo">
          <app-demographics
            [data]="demographicsData"
            (backClick)="onDemographicsBackClick()"
          ></app-demographics>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-demographics [data]="demographicsData"&gt;&lt;/app-demographics&gt;</code></pre>
        </div>
      </div>
    </div>

    <!-- Hits Component -->
    <div class="component-row full-width">
      <div class="component-item full-width">
        <h3 class="component-title">Hits</h3>
        <div class="component-subtitle">Style Guide: "Hits" | Implementation: "HitsComponent"</div>
        <div class="component-demo figma-sized hits-demo">
          <app-hits
            title="Hits"
            [data]="hitsData"
            (dataChange)="onHitsDataChange($event)"
            (pageClick)="onHitsPageClick($event)"
            (commentChange)="onHitsCommentChange($event)"
            (includeChange)="onHitsIncludeChange($event)"
          ></app-hits>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-hits title="Hits" [data]="hitsData"&gt;&lt;/app-hits&gt;</code></pre>
        </div>
      </div>
    </div>

    <!-- Menu Component -->
    <div class="component-row full-width">
      <div class="component-item full-width">
        <h3 class="component-title">Menu</h3>
        <div class="component-demo figma-sized menu-demo">
          <app-menu
            logoSrc="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024"
            logoAlt="Stellarus Logo"
            [user]="userProfile"
            [menuItems]="menuItems"
            (logoClick)="onLogoClick()"
            (userClick)="onUserClick()"
            (dropdownToggle)="onDropdownToggle($event)"
            (menuItemClick)="onMenuItemClick($event)"
          ></app-menu>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-menu [user]="userProfile" [menuItems]="menuItems"&gt;&lt;/app-menu&gt;</code></pre>
        </div>
      </div>
    </div>

    <!-- Icons Component - Temporarily disabled due to SVG binding issues -->
    <!--
    <div class="component-row full-width">
      <div class="component-item full-width">
        <h3 class="component-title">Icons</h3>
        <div class="component-subtitle">Style Guide: "Buttons, selectors, and icons" | Implementation: "IconComponent"</div>
        <div class="component-demo">
          <div class="icons-grid">
            <div *ngFor="let iconName of iconNames" class="icon-demo-item">
              <app-icon [name]="iconName" size="md" color="primary" [clickable]="true"></app-icon>
              <span class="icon-label">{{ iconName }}</span>
            </div>
          </div>
        </div>
        <div class="component-code">
          <pre><code>&lt;app-icon name="refresh" size="md" color="primary" [clickable]="true"&gt;&lt;/app-icon&gt;</code></pre>
        </div>
      </div>
    </div>
    -->


  </section>
</div>