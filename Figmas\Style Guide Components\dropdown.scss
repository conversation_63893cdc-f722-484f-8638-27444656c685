@import "src/app/tokens.scss";

.dropdown_506-9753 {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  background: #ffffff;
  width: 1615px;
  height: 2420px;
}
.rectangle-314_506-9757 {
  top: 737px;
  left: 189px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-333_506-9758 {
  top: 1059px;
  left: 189px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-335_649-5211 {
  top: 261px;
  left: 189px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-spacing-_501-14380 {
  top: 707px;
  left: 64px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.text-styling-_501-14381 {
  top: 1022px;
  left: 71px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urban<PERSON>;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.text-variants-_649-5099 {
  top: 1568px;
  left: 71px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 92px;
  height: 16px;
}

.text-exclusions-_649-5213 {
  top: 1618px;
  left: 72px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 109px;
  height: 16px;
}

.text-none-found-_649-5214 {
  top: 1618px;
  left: 589px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 130px;
  height: 16px;
}

.text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state-_307-12563 {
  top: 178px;
  left: 71px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  width: 1040px;
  height: 16px;
}

.text-sorting--sorting-for-all-items-in-the-dropdown-is-alphabetical-_488-12548 {
  top: 216px;
  left: 71px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  width: 1040px;
  height: 16px;
}

.text-usage--select-reasoning-for-a-finding-_307-12564 {
  top: 134px;
  left: 72px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  text-wrap: wrap;
  width: 1478px;
  height: 22px;
}

.text-dropdown_307-12565 {
  top: 63px;
  left: 71px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 36px;
  font-family: Urbane;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
  width: 198px;
  height: 43px;
}

.text-default--no-reasoning-selected_307-12578 {
  top: 308px;
  left: 666px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 229px;
  height: 4px;
}

.text-clicked--top-level-clicked_307-12580 {
  top: 379px;
  left: 666px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 174px;
  height: 4px;
}

.text-items-in-list-are-multiselect--and-the-user-can-select-multiple-at-the-same-time_488-12551 {
  top: 423px;
  left: 666px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 315px;
  height: 24px;
}

.text-selected--item-selected-from-dropdown_307-12582 {
  top: 492px;
  left: 666px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 302px;
  height: 4px;
}

.text-entered--item-selected-from-dropdown--label-changes-to-match-selected-item_307-12584 {
  top: 605px;
  left: 666px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 315px;
  height: 24px;
}

.text-corner-radius--10-border-style--solid-border-color--gray-2_506-9751 {
  top: 1068px;
  left: 848px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 121px;
  height: 60px;
}

.text-border-style--solid-border-color--gray-3_938-15889 {
  top: 1212px;
  left: 644px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 121px;
  height: 40px;
}

.text-border-style--solid-border-color--gray-3_938-15891 {
  top: 1325px;
  left: 644px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 121px;
  height: 40px;
}

.vector-9_307-12579 {
  top: 318px;
  left: 625px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-10_307-12581 {
  top: 389px;
  left: 625px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-13_488-12552 {
  top: 433px;
  left: 625px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-11_307-12583 {
  top: 502px;
  left: 625px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-12_307-12585 {
  top: 615px;
  left: 625px;
  position: absolute;
  display: flex;
  display: flex;
}

.dropdown_649-5178 {
  padding: 4px;
  top: 598px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
}
.row_649-5179 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_649-5180 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-right_649-5181 {
  position: relative;
  width: 14px;
  height: 8px;
}

.dropdown_649-5182 {
  padding: 4px;
  top: 478px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-3;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
}
.row_649-5183 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_649-5184 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-down_649-5185 {
  position: relative;
  width: 14px;
  height: 8px;
}

.div_649-5186 {
  position: relative;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  height: 8px;
  width: 100%;
}
.rectangle-1_649-5187 {
  top: 4px;
  left: -8px;
  position: absolute;
  display: flex;
  display: flex;
}

.row_649-5188 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5189 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-selection_649-5190 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.dropdown_649-5191 {
  padding: 4px;
  top: 368px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-3;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
}
.row_649-5192 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_649-5193 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-down_649-5194 {
  position: relative;
  width: 14px;
  height: 8px;
}

.div_649-5195 {
  position: relative;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  height: 8px;
  width: 100%;
}
.rectangle-1_649-5196 {
  top: 4px;
  left: -8px;
  position: absolute;
  display: flex;
  display: flex;
}

.row_649-5197 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5198 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-selection_649-5199 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.dropdown_649-5200 {
  padding: 4px;
  top: 286px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
  height: 48px;
}
.row_649-5201 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_649-5202 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-right_649-5203 {
  position: relative;
  width: 14px;
  height: 8px;
}

.dropdown-variant8_501-16775 {
  padding: 4px;
  top: 754px;
  left: 206px;
  position: absolute;
  display: flex;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: #d9e1e7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
  height: 48px;
}
.row_501-16776 {
  padding: 8px 12px 8px 12px;
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_501-16777 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-stroke_501-16778 {
  position: relative;
  box-sizing: border-box;
  display: flex;
}
.vector_501-16779 {
  top: 1px;
  left: 1px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-318_501-16845 {
  top: -6px;
  left: -4px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-320_501-16847 {
  top: 4px;
  left: 4px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-321_501-16848 {
  top: 0px;
  left: 395px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-319_501-16846 {
  top: 754px;
  left: 613px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-320_505-833 {
  top: 880px;
  left: 523px;
  position: absolute;
  display: flex;
  display: flex;
}

.dropdown-variant8_501-16808 {
  padding: 4px;
  top: 1076px;
  left: 207px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
}
.row_501-16809 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_501-16810 {
  color: $variable-collection-gray-3;
  @include input-text-text;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-stroke_501-16811 {
  position: relative;
  box-sizing: border-box;
  display: flex;
}
.vector_501-16812 {
  top: 1px;
  left: 1px;
  position: absolute;
  display: flex;
  display: flex;
}

.dropdown-variant7_501-16780 {
  padding: 4px;
  top: 836px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: #d9e1e7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
}
.row_501-16781 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_501-16782 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-stroke_501-16783 {
  position: relative;
  box-sizing: border-box;
  display: flex;
}
.vector_501-16784 {
  top: 1px;
  left: 1px;
  position: absolute;
  display: flex;
  display: flex;
}

.div_501-16785 {
  position: relative;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  height: 8px;
  width: 100%;
}
.rectangle-1_501-16786 {
  top: 4px;
  left: -8px;
  position: absolute;
  display: flex;
  display: flex;
}

.row_501-16787 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_501-16788 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-selection_501-16789 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.rectangle-322_505-860 {
  top: 836px;
  left: 210px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-332_505-866 {
  top: 880px;
  left: 238px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-331_505-865 {
  top: 836px;
  left: 595px;
  position: absolute;
  display: flex;
  display: flex;
}

.dropdown-variant7_501-16813 {
  padding: 4px;
  top: 1158px;
  left: 207px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-3;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
}
.row_501-16814 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_501-16815 {
  color: #000000;
  @include input-text-text;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-stroke_501-16816 {
  position: relative;
  box-sizing: border-box;
  display: flex;
}
.vector_501-16817 {
  top: 1px;
  left: 1px;
  position: absolute;
  display: flex;
  display: flex;
}

.div_501-16818 {
  position: relative;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  height: 8px;
  width: 100%;
}
.rectangle-1_501-16819 {
  top: 4px;
  left: -8px;
  position: absolute;
  display: flex;
  display: flex;
}

.row_501-16820 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_501-16821 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-selection_501-16822 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.dropdown-variant6_501-16823 {
  padding: 4px;
  top: 1284px;
  left: 207px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-3;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
}
.row_501-16824 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_501-16825 {
  color: #000000;
  @include input-text-text;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-stroke_501-16826 {
  position: relative;
  box-sizing: border-box;
  display: flex;
}
.vector_501-16827 {
  top: 1px;
  left: 1px;
  position: absolute;
  display: flex;
  display: flex;
}

.div_501-16828 {
  position: relative;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  height: 8px;
  width: 100%;
}
.rectangle-1_501-16829 {
  top: 4px;
  left: -8px;
  position: absolute;
  display: flex;
  display: flex;
}

.row_501-16830 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_501-16831 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-selection_501-16832 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.dropdown-variant5_501-16833 {
  padding: 4px;
  top: 1404px;
  left: 207px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 410px;
}
.row_501-16834 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_501-16835 {
  color: #000000;
  @include input-text-text;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-stroke_501-16836 {
  position: relative;
  box-sizing: border-box;
  display: flex;
}
.vector_501-16837 {
  top: 1px;
  left: 1px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-314_501-16841 {
  top: 758px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-321_505-834 {
  top: 884px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-326_505-839 {
  top: 840px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-317_501-16844 {
  top: 802px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-322_505-835 {
  top: 924px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-327_505-840 {
  top: 880px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-315_501-16842 {
  top: 766px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-323_505-836 {
  top: 892px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-328_505-841 {
  top: 848px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-316_501-16843 {
  top: 798px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-324_505-837 {
  top: 920px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-329_505-842 {
  top: 876px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-843 {
  top: 884px;
  left: 185px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-852 {
  top: 759px;
  left: 185px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-856 {
  top: 822px;
  left: 210px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-867 {
  top: 946px;
  left: 210px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-871 {
  top: 946px;
  left: 238px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-848 {
  top: 912px;
  left: 185px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-846 {
  top: 880px;
  left: 185px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-853 {
  top: 755px;
  left: 185px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-858 {
  top: 822px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-868 {
  top: 946px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_505-849 {
  top: 921px;
  left: 185px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-8px_505-844 {
  top: 889px;
  left: 163px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 20px;
  height: 4px;
}

.text-8px_505-854 {
  top: 764px;
  left: 163px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 20px;
  height: 4px;
}

.text-12px_505-857 {
  top: 827px;
  left: 215px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 22px;
  height: 4px;
}

.text-12px_505-869 {
  top: 951px;
  left: 215px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 22px;
  height: 4px;
}

.text-12px_505-872 {
  top: 951px;
  left: 243px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 22px;
  height: 4px;
}

.text-8px_505-850 {
  top: 915px;
  left: 163px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 20px;
  height: 4px;
}

.text-4px_505-847 {
  top: 878px;
  left: 163px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 20px;
  height: 4px;
}

.text-4px_505-855 {
  top: 754px;
  left: 163px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 20px;
  height: 4px;
}

.text-4px_505-859 {
  top: 827px;
  left: 189px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 20px;
  height: 4px;
}

.text-4px_505-870 {
  top: 951px;
  left: 189px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 20px;
  height: 4px;
}

.text-4px_505-851 {
  top: 927px;
  left: 163px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 20px;
  height: 4px;
}

.rectangle-319_505-862 {
  top: 836px;
  left: 206px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-330_505-864 {
  top: 836px;
  left: 611px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-font--input-text-color--gray-3_506-8911 {
  top: 1084px;
  left: 85px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 90px;
  height: 24px;
}

.text-font--input-text-color--text-black_506-8914 {
  top: 1168px;
  left: 85px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 99px;
  height: 24px;
}

.text-component--icon-down-color--text-black--when-active-_506-8924 {
  top: 1168px;
  left: 644px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 186px;
  height: 24px;
}

.text-component--icon-right-color--gray-3_506-8926 {
  top: 1086px;
  left: 644px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 140px;
  height: 24px;
}

.text-font--input-text-color--text-black-component--check_506-8918 {
  top: 1210px;
  left: 68px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 116px;
  height: 44px;
}

.text-component--check-color--text-black--when-active-_506-8920 {
  top: 1340px;
  left: 64px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 124px;
  height: 44px;
}

.text-font--input-text-color--text-black_506-8916 {
  top: 1294px;
  left: 85px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 99px;
  height: 24px;
}

.text-font--input-text-color--text-black_506-8922 {
  top: 1413px;
  left: 85px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 99px;
  height: 24px;
}

.vector-28_506-8912 {
  top: 1100px;
  left: 189px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-29_506-8915 {
  top: 1184px;
  left: 189px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-34_506-8925 {
  top: 1180px;
  left: 612px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-35_506-8927 {
  top: 1098px;
  left: 612px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-31_506-8919 {
  top: 1226px;
  left: 189px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-32_506-8921 {
  top: 1349px;
  left: 189px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-30_506-8917 {
  top: 1310px;
  left: 189px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-33_506-8923 {
  top: 1429px;
  left: 189px;
  position: absolute;
  display: flex;
  display: flex;
}

.dropdown-exclusion_649-5299 {
  top: 1662px;
  left: 72px;
  position: absolute;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 5px;
  border-color: #9747ff;
  border-style: dashed;
  border-width: 1px;
}
.property-1-entered_649-5298 {
  padding: 4px;
  top: 628px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.row_649-5290 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-reasoning_649-5291 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-right_649-5292 {
  position: relative;
  width: 14px;
  height: 8px;
}

.property-1-default_649-5296 {
  padding: 4px;
  top: 20px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.row_649-5286 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_649-5287 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-right_649-5288 {
  position: relative;
  width: 14px;
  height: 8px;
}

.property-1-active_649-5297 {
  padding: 4px;
  top: 88px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-3;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.row_649-5226 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_649-5227 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-stroke_649-5228 {
  position: relative;
  box-sizing: border-box;
  display: flex;
}
.vector_649-5229 {
  top: 1px;
  left: 1px;
  position: absolute;
  display: flex;
  display: flex;
}

.div_649-5230 {
  position: relative;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  height: 8px;
  width: 100%;
}
.rectangle-1_649-5231 {
  top: 4px;
  left: -8px;
  position: absolute;
  display: flex;
  display: flex;
}

.row_649-5232 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5233 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-acute-inpatient-and-ed-visit_649-5234 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5235 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5236 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-end-stage-renal-disease_649-5237 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5238 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5239 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-frailty--member-81--years-as-of-12-31-of-the-my_649-5240 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5241 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5242 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-lidocaine-and-epinephrine-given-to-patient_649-5243 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5244 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5245 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-medicare-member-in-an-institutional-snp--i-snp-_649-5246 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5247 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5248 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-medicare-member-living-in-long-term-care_649-5249 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5250 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5251 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-member-66-80-years-as-of-12-31-of-the-my_649-5252 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5253 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5254 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-member-died-during-the-my_649-5255 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5256 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5257 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-member-in-hospice-anytime-during-the-my_649-5258 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5259 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5260 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-non-acute-inpatient-admission_649-5261 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5262 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5263 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-palliative-care_649-5264 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5265 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5266 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-pregnancy_649-5267 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5268 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5269 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-other_649-5270 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.dropdown-none_649-5451 {
  top: 1662px;
  left: 569px;
  position: absolute;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 5px;
  border-color: #9747ff;
  border-style: dashed;
  border-width: 1px;
}
.property-1-entered_649-5450 {
  padding: 4px;
  top: 598px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.row_649-5367 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-reasoning_649-5368 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-right_649-5369 {
  position: relative;
  width: 14px;
  height: 8px;
}

.property-1-default_649-5448 {
  padding: 4px;
  top: 20px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.row_649-5371 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_649-5372 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-right_649-5373 {
  position: relative;
  width: 14px;
  height: 8px;
}

.property-1-clicked_649-5449 {
  padding: 4px;
  top: 86px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-3;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.row_649-5311 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-select_649-5312 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-stroke_649-5313 {
  position: relative;
  box-sizing: border-box;
  display: flex;
}
.vector_649-5314 {
  top: 1px;
  left: 1px;
  position: absolute;
  display: flex;
  display: flex;
}

.div_649-5315 {
  position: relative;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  height: 8px;
  width: 100%;
}
.rectangle-1_649-5316 {
  top: 4px;
  left: -8px;
  position: absolute;
  display: flex;
  display: flex;
}

.row_649-5317 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5318 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-blood-pressure-values-do-not-match_649-5319 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5320 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5321 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-bmi-not-found_649-5322 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5323 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5324 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-chart-summary-not-found_649-5325 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5326 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5327 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-dates-do-not-match_649-5328 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5329 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5330 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-documentation-does-not-match-entry_649-5331 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5332 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5333 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-insufficient-documentation_649-5334 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5335 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5336 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-insufficient-patient-identifiers_649-5337 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5338 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5339 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-lab-value-not-entered_649-5340 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5341 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5342 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-no-documentation_649-5343 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5344 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5345 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-out-of-timeframe-for-measurement-period_649-5346 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5347 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5348 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-patient-name-dob-does-not-match-records_649-5349 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.row_649-5350 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.check_649-5351 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-other_649-5352 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}
