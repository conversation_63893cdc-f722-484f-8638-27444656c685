<div class="calendar-container" 
     [class.disabled]="disabled"
     [class.has-error]="errorMessage"
     #calendarContainer>
  
  <!-- Label -->
  <label *ngIf="label" [for]="id" class="calendar-label">
    {{ label }}
    <span *ngIf="required" class="required-indicator">*</span>
  </label>

  <!-- Date Input -->
  <div class="date-input-container"
       [class.focused]="isCalendarOpen"
       [class.disabled]="disabled"
       [class.has-value]="selectedDate"
       [class.has-error]="errorMessage">
    
    <div class="date-input-content">
      <input 
        type="text"
        class="date-input"
        [id]="id"
        [name]="name"
        [placeholder]="placeholder"
        [value]="displayValue"
        [disabled]="disabled"
        [required]="required"
        [attr.min]="minDate"
        [attr.max]="maxDate"
        [attr.aria-expanded]="isCalendarOpen"
        [attr.aria-haspopup]="true"
        [attr.role]="'combobox'"
        (input)="onInputChange($event)"
        (focus)="onInputFocus()"
        (blur)="onInputBlur()"
        #dateInput>
      
      <button type="button" 
              class="calendar-icon-button"
              [disabled]="disabled"
              [attr.aria-label]="'Open calendar'"
              (click)="toggleCalendar()">
        <svg class="calendar-icon" width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="0.75" y="2.75" width="14.5" height="14.5" rx="1.25" stroke="currentColor" stroke-width="1.5"/>
          <path d="M4 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          <path d="M12 1V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          <path d="M1 7H15" stroke="currentColor" stroke-width="1.5"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- Calendar Popup -->
  <div class="calendar-popup" 
       *ngIf="isCalendarOpen"
       [attr.role]="'dialog'"
       [attr.aria-label]="'Calendar'">
    
    <!-- Calendar Header -->
    <div class="calendar-header">
      <button type="button" 
              class="nav-button"
              [attr.aria-label]="'Previous month'"
              (click)="previousMonth()">
        <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 1L2 6L7 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      
      <span class="month-year">
        {{ monthNames[currentMonth] }} {{ currentYear }}
      </span>
      
      <button type="button" 
              class="nav-button"
              [attr.aria-label]="'Next month'"
              (click)="nextMonth()">
        <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 1L6 6L1 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <!-- Days of Week Header -->
    <div class="days-header">
      <div class="day-header">Su</div>
      <div class="day-header">Mo</div>
      <div class="day-header">Tu</div>
      <div class="day-header">We</div>
      <div class="day-header">Th</div>
      <div class="day-header">Fr</div>
      <div class="day-header">Sa</div>
    </div>

    <!-- Calendar Days -->
    <div class="calendar-days">
      <button type="button"
              *ngFor="let day of calendarDays; trackBy: trackByDay"
              class="calendar-day"
              [class.selected]="isSelectedDay(day)"
              [class.today]="isToday(day)"
              [class.empty]="!day"
              [disabled]="!day"
              [attr.aria-label]="day ? 'Select ' + day : null"
              (click)="selectDate(day!)">
        {{ day || '' }}
      </button>
    </div>
  </div>

  <!-- Error message -->
  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>
</div>
