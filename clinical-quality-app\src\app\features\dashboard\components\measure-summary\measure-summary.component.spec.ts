import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MeasureSummaryComponent } from './measure-summary.component';

describe('MeasureSummaryComponent', () => {
  let component: MeasureSummaryComponent;
  let fixture: ComponentFixture<MeasureSummaryComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MeasureSummaryComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MeasureSummaryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
