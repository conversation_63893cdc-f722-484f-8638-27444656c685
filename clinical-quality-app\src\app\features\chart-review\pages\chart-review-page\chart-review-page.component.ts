import { Component, OnInit, inject, ViewChild, PLATFORM_ID } from '@angular/core'; // Import inject, ViewChild, PLATFORM_ID
import { ActivatedRoute, RouterModule, Router } from '@angular/router'; // Import RouterModule and Router
import { CommonModule, isPlatformBrowser } from '@angular/common'; // Import isPlatformBrowser
import { FormsModule } from '@angular/forms';
import { PdfViewerComponent } from '@features/chart-review/components/pdf-viewer/pdf-viewer.component';
import { MenuComponent, UserProfile, MenuItem } from '@shared/components/menu/menu.component';
import { HitsComponent, HitData } from '@shared/components/hits/hits.component';
import { ResultsComponent, ResultsData } from '@shared/components/form-controls/results/results.component';
import { ButtonComponent } from '@shared/components/buttons/button.component';
import { DemographicsComponent, DemographicsData } from '@shared/components/demographics/demographics.component';
import { HttpClient } from '@angular/common/http';
import { PdfService } from '@features/chart-review/services/pdf.service'; // Import PdfService

interface Finding {
  id: string;
  type: string;
  text: string;
  confidence: number;
  pageNumber: number;
  validated: boolean;
  isValid?: boolean;
}

@Component({
  selector: 'app-chart-review-page',
  standalone: true,
  imports: [
    CommonModule,
    PdfViewerComponent,
    FormsModule,
    MenuComponent,
    HitsComponent,
    ResultsComponent,
    ButtonComponent,
    DemographicsComponent,
    RouterModule
  ],
  templateUrl: './chart-review-page.component.html',
  styleUrl: './chart-review-page.component.scss'
})
export class ChartReviewPageComponent implements OnInit {
  @ViewChild(PdfViewerComponent) pdfViewerComponent!: PdfViewerComponent;

  chartId: string = '';
  selectedFinding: Finding | null = null;

  // Demographics data for the patient info header (will be updated with route data)
  demographicsData: DemographicsData = {
    measureTitle: 'Controlling Blood Pressure (CBP)',
    measureSubtitle: 'Measure',
    memberId: '',
    memberName: '',
    dateOfBirth: '',
    gender: '',
    lob: '',
    providerName: '',
    npi: ''
  };

  // Navigation data
  userProfile: UserProfile = {
    name: 'Jane Chu',
    avatar: ''
  };

  menuItems: MenuItem[] = [
    { label: 'Dashboard', route: '/dashboard', icon: '🏠' },
    { label: 'Profile', route: '/profile', icon: '👤' },
    { label: 'Settings', route: '/settings', icon: '⚙️' },
    { label: 'Help', route: '/help', icon: '❓' },
    { label: 'Logout', action: () => this.logout(), icon: '🚪' }
  ];

  // Hits data for the hits component
  hitsData: HitData[] = [
    {
      id: 'hit-1',
      dateOfService: '07/22/24',
      systolic: 136,
      diastolic: 82,
      page: 2,
      comment: '',
      include: false
    },
    {
      id: 'hit-2',
      dateOfService: '07/22/24',
      systolic: 140,
      diastolic: 82,
      page: 2,
      comment: '',
      include: false
    },
    {
      id: 'hit-3',
      dateOfService: '05/22/24',
      systolic: 150,
      diastolic: 90,
      page: 7,
      comment: '',
      include: false
    }
  ];

  // Results data for the results component
  resultsData: ResultsData = {
    category: 'inclusions',
    telehealth: false,
    sys: '',
    dias: '',
    dateOfService: '',
    notes: ''
  };


  // zoom property removed as it's no longer needed
  // TODO: Determine how selectedFinding should be set now that the sidebar is removed.
  // It might come from interactions with the PDF viewer or another source.

  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private pdfService = inject(PdfService); // Inject PdfService
  private http = inject(HttpClient); // Inject HttpClient for loading default PDF
  private platformId = inject(PLATFORM_ID); // Inject platform ID for browser detection

  // Remove the constructor if only used for DI before

  ngOnInit(): void {
    // Get chart ID from route parameters
    this.chartId = this.route.snapshot.paramMap.get('id') || '';

    if (this.chartId) {
      console.log(`[ChartReviewPage] Attempting to load chart with ID: ${this.chartId}`);
      // Construct the path to the PDF within the application's assets folder.
      // This is how local files bundled with the app are accessed.
      const pdfUrl = `assets/charts/${this.chartId}.pdf`;

      // Use pdfService.loadPdf(url) for loading PDFs from the assets folder.
      // HttpClient (used by loadPdf) can fetch these local asset files.
      this.pdfService.loadPdf(pdfUrl).subscribe({
        next: (pdfDoc) => {
          console.log(`[ChartReviewPage] PDF loaded successfully from asset path: ${pdfUrl}, Pages: ${pdfDoc.numPages}`);
          // PdfViewerComponent should automatically react to the PdfService's BehaviorSubject update.
        },
        error: (err) => {
          console.error(`[ChartReviewPage] Error loading PDF from asset path (${pdfUrl}):`, err);
          // TODO: Display a user-friendly error message in the UI.
          // For example, set an error message property and bind it in the template.
        }
      });
    } else {
      console.warn('[ChartReviewPage] No chart ID found in route parameters. Cannot load PDF.');
      // TODO: Optionally, display a message to the user that no chart ID was provided.
    }

    // Update demographics data with member information
    this.updateDemographicsData();

    console.log('[ChartReviewPage] Demographics data initialized:', this.demographicsData);

    // Load PDF based on member ID from route
    this.loadPdfForMember();

    // TODO: Initialize selectedFinding based on the new selection mechanism if needed.
    // this.selectedFinding = ...;
  }

  /**
   * Handles the file selection event from an input element.
   * This method is for manual file uploads by the user, not for loading charts by ID.
   * Commenting out for now as the primary task is to load by chartId.
   */
  /*
  onFileSelected(event: Event): void {
    console.log('[ChartReviewPage] onFileSelected method called.');
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      console.log(`[ChartReviewPage] File selected: ${file.name}`);
      // loadPdfFromFile is used when a user explicitly selects a file using a file input.
      this.pdfService.loadPdfFromFile(file).subscribe({
        next: (pdfDoc) => {
          console.log(`[ChartReviewPage] PDF loaded successfully by service (from file input): ${pdfDoc.numPages} pages`);
        },
        error: (err) => {
          console.error('[ChartReviewPage] Error loading PDF via service (from file input):', err);
        }
      });
    } else {
      console.log('[ChartReviewPage] No file selected via file input.');
    }
  }
  */

  // Zoom methods removed as requested

  /**
   * Navigates the PDF viewer to a specific page.
   * @param pageNumber The page number to navigate to.
   */
  public goToPdfPage(pageNumber: number): void {
    if (this.pdfViewerComponent) {
      console.log(`[ChartReviewPage] Navigating to PDF page: ${pageNumber}`);
      this.pdfViewerComponent.currentPage = pageNumber;
    } else {
      console.warn('[ChartReviewPage] goToPdfPage called, but pdfViewerComponent is not available.');
    }
  }

  // Navigation event handlers
  onLogoClick(): void {
    console.log('Logo clicked');
    this.router.navigate(['/dashboard']);
  }

  onUserClick(): void {
    console.log('User clicked');
  }

  onDropdownToggle(isOpen: boolean): void {
    console.log('Dropdown toggled:', isOpen);
  }

  onMenuItemClick(item: MenuItem): void {
    console.log('Menu item clicked:', item);
    if (item.route) {
      this.router.navigate([item.route]);
    } else if (item.action) {
      item.action();
    }
  }

  logout(): void {
    console.log('Logout clicked');
    this.router.navigate(['/']);
  }

  // Demographics component event handler
  onDemographicsBackClick(): void {
    console.log('Demographics back button clicked');
    this.router.navigate(['/dashboard']);
  }

  // Update demographics data based on member ID
  private updateDemographicsData(): void {
    const memberId = this.chartId;
    console.log(`[ChartReviewPage] Updating demographics for member ID: ${memberId}`);

    // Update member ID
    this.demographicsData.memberId = memberId || 'Unknown';

    // Set member-specific data based on member ID
    if (memberId === '55820474') {
      this.demographicsData.memberName = 'John Dey';
      this.demographicsData.dateOfBirth = '01/05/1972';
      this.demographicsData.gender = 'M';
      this.demographicsData.lob = 'PPO';
      this.demographicsData.providerName = 'Nicolas Dejong PA';
      this.demographicsData.npi = '882716229';
    } else {
      // Default data for other member IDs
      this.demographicsData.memberName = 'Sample Patient';
      this.demographicsData.dateOfBirth = '01/01/1970';
      this.demographicsData.gender = 'U';
      this.demographicsData.lob = 'HMO';
      this.demographicsData.providerName = 'Dr. Sample Provider';
      this.demographicsData.npi = '123456789';
    }

    console.log('[ChartReviewPage] Demographics updated:', this.demographicsData);
  }

  // Load PDF based on member ID from route parameters
  private loadPdfForMember(): void {
    const memberId = this.chartId; // chartId is set from route parameters
    console.log(`[ChartReviewPage] Loading PDF for member ID: ${memberId}`);

    if (!memberId) {
      console.warn('[ChartReviewPage] No member ID available, loading default PDF');
      this.loadFallbackPdf();
      return;
    }

    // Try to load the specific PDF for this member ID
    const pdfPath = `assets/charts/${memberId}.pdf`;
    console.log(`[ChartReviewPage] Attempting to load PDF from: ${pdfPath}`);

    this.http.get(pdfPath, { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log(`[ChartReviewPage] PDF for member ${memberId} loaded successfully`);
          this.convertBlobToDataUrlAndLoad(blob);
        },
        error: (error) => {
          console.error(`[ChartReviewPage] Error loading PDF for member ${memberId}:`, error);
          console.log('[ChartReviewPage] Falling back to default PDF...');
          this.loadFallbackPdf();
        }
      });
  }

  // Load fallback PDF when member-specific PDF is not available
  private loadFallbackPdf(): void {
    console.log('[ChartReviewPage] Loading fallback PDF...');

    // Load the CBP redacted usability PDF as fallback
    this.http.get('assets/charts/CBP_Redacted_Usability.pdf', { responseType: 'blob' })
      .subscribe({
        next: (blob) => {
          console.log('[ChartReviewPage] Fallback PDF loaded successfully');
          this.convertBlobToDataUrlAndLoad(blob);
        },
        error: (error) => {
          console.error('[ChartReviewPage] Error loading fallback PDF:', error);
          console.log('[ChartReviewPage] Trying final fallback PDF...');

          // Try the other PDF as final fallback
          this.http.get('assets/charts/55820474.pdf', { responseType: 'blob' })
            .subscribe({
              next: (blob) => {
                console.log('[ChartReviewPage] Final fallback PDF loaded successfully');
                this.convertBlobToDataUrlAndLoad(blob);
              },
              error: (altError) => {
                console.error('[ChartReviewPage] Error loading final fallback PDF:', altError);
                console.error('[ChartReviewPage] No PDFs available to load');
              }
            });
        }
      });
  }

  // Helper method to convert blob to data URL and load into PDF service
  private convertBlobToDataUrlAndLoad(blob: Blob): void {
    // Check if we're in a browser environment before using FileReader
    if (!isPlatformBrowser(this.platformId)) {
      console.log('[ChartReviewPage] Not in browser environment, skipping FileReader operation');
      return;
    }

    // FileReader is only available in browser environment
    if (typeof FileReader === 'undefined') {
      console.error('[ChartReviewPage] FileReader is not available in this environment');
      return;
    }

    const reader = new FileReader();
    reader.onload = () => {
      const dataUrl = reader.result as string;
      console.log('[ChartReviewPage] PDF converted to base64, loading into viewer');

      // Use the PDF service to load the PDF
      this.pdfService.loadPdfFromDataUrl(dataUrl).subscribe({
        next: (pdfDoc) => {
          console.log(`[ChartReviewPage] PDF loaded successfully: ${pdfDoc.numPages} pages`);
        },
        error: (error) => {
          console.error('[ChartReviewPage] Error loading PDF via service:', error);
        }
      });
    };
    reader.readAsDataURL(blob);
  }

  // Hits component event handlers
  onHitsDataChange(data: HitData[]): void {
    console.log('Hits data changed:', data);
    this.hitsData = data;
  }

  onHitsPageClick(event: { hit: HitData, page: number }): void {
    console.log('Hits page clicked:', event);
    this.goToPdfPage(event.page);
  }

  onHitsCommentChange(event: { hit: HitData, comment: string }): void {
    console.log('Hits comment changed:', event);
    const hitIndex = this.hitsData.findIndex(hit => hit.id === event.hit.id);
    if (hitIndex !== -1) {
      this.hitsData[hitIndex].comment = event.comment;
    }
  }

  onHitsIncludeChange(event: { hit: HitData, include: boolean }): void {
    console.log('Hits include changed:', event);
    const hitIndex = this.hitsData.findIndex(hit => hit.id === event.hit.id);
    if (hitIndex !== -1) {
      this.hitsData[hitIndex].include = event.include;
    }
  }

  // Results component event handlers
  onResultsDataChange(data: ResultsData): void {
    console.log('Results data changed:', data);
    this.resultsData = data;
  }

  // Complete review action
  onCompleteReview(): void {
    console.log('Complete review clicked');
    console.log('Results data:', this.resultsData);
    console.log('Hits data:', this.hitsData);
    // Add logic to save/submit the review
  }
}
