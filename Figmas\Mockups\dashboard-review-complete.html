<div data-layer="Dashboard - review completed" class="DashboardReviewCompleted" style="width: 1440px; height: 900px; background: var(--background-gray, #F6F6F6); flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
  <div data-layer="screen" class="Screen" style="width: 1440px; height: 900px; background: var(--light-background, #F9FBFC); flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
    <div data-layer="menu" class="Menu" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
      <div data-layer="Topbar" class="Topbar" style="width: 1440px; height: 80px; background: var(--white, white); border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
        <div data-layer="menu" class="Menu" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: center; display: inline-flex">
          <div data-layer="box" class="Box" style="flex: 1 1 0; align-self: stretch; padding-left: 30px; padding-right: 30px; padding-top: 12px; padding-bottom: 12px; justify-content: flex-start; align-items: center; display: flex">
            <div data-layer="frame" class="Frame" style="flex: 1 1 0; height: 46px; justify-content: space-between; align-items: center; display: flex">
              <div data-layer="Frame 840" class="Frame840" style="flex: 1 1 0; justify-content: flex-start; align-items: center; gap: 20px; display: flex">
                <div data-layer="logo" class="Logo" style="width: 240px; padding-left: 20px; padding-right: 20px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                  <img data-layer="stellarus-logo" class="StellarusLogo" style="width: 150px; height: 37.40px" src="data:image/png;base64,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" />
                </div>
              </div>
              <div data-layer="Frame 839" class="Frame839" style="justify-content: flex-end; align-items: center; gap: 16px; display: flex">
                <div data-layer="Menu Item" class="MenuItem" style="padding-left: 20px; padding-right: 20px; padding-top: 12px; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: flex">
                  <div data-layer="Frame 838" class="Frame838" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                    <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
                    <div data-layer="base" class="Base" style="padding: 8px; background: rgba(56, 112, 184, 0.20); overflow: hidden; border-radius: 120px; justify-content: center; align-items: center; gap: 8px; display: flex">
                      <div data-svg-wrapper data-layer="icon_user" data-property-1="Profile_Circle" class="IconUser" style="position: relative">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10.0999 10.65C10.0416 10.6416 9.9666 10.6416 9.89993 10.65C8.43327 10.6 7.2666 9.39998 7.2666 7.92498C7.2666 6.41665 8.48327 5.19165 9.99993 5.19165C11.5083 5.19165 12.7333 6.41665 12.7333 7.92498C12.7249 9.39998 11.5666 10.6 10.0999 10.65Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M15.6166 16.15C14.1333 17.5084 12.1666 18.3334 9.99997 18.3334C7.8333 18.3334 5.86663 17.5084 4.3833 16.15C4.46663 15.3667 4.96663 14.6 5.8583 14C8.14163 12.4834 11.875 12.4834 14.1416 14C15.0333 14.6 15.5333 15.3667 15.6166 16.15Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M9.99984 18.3334C14.6022 18.3334 18.3332 14.6024 18.3332 10.0001C18.3332 5.3977 14.6022 1.66674 9.99984 1.66674C5.39746 1.66674 1.6665 5.3977 1.6665 10.0001C1.6665 14.6024 5.39746 18.3334 9.99984 18.3334Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </div>
                    </div>
                    <div data-svg-wrapper data-layer="icon_arrow_down" class="IconArrowDown" style="position: relative">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M16 9.99992L12 13.9999L8 9.99992" stroke="var(--light-primary, #809FB8)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div data-layer="content" class="Content" style="align-self: stretch; flex: 1 1 0; padding-top: 20px; padding-bottom: 20px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: flex">
      <div data-layer="content" class="Content" style="align-self: stretch; padding-left: 30px; padding-right: 30px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: flex">
        <div data-layer="Dashboard" class="Dashboard" style="color: var(--text-black, #17181A); font-size: 24px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Dashboard</div>
        <div data-layer="assigned-table" class="AssignedTable" style="width: 1380px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: flex">
          <div data-layer="assigned-table" class="AssignedTable" style="width: 1380px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: flex">
            <div data-layer="Table" class="Table" style="align-self: stretch; padding: 20px; background: var(--white, white); border-radius: 8px; outline: 1px var(--light-borders, #F1F5F7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
              <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 20px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
                <div data-layer="stack" class="Stack" style="flex: 1 1 0; justify-content: flex-start; align-items: center; gap: 10px; display: flex">
                  <div data-layer="label" class="Label" style="color: var(--text-black, #17181A); font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Assigned charts</div>
                  <div data-layer="right corner" class="RightCorner" style="flex: 1 1 0; align-self: stretch"></div>
                </div>
                <div data-layer="Button" class="Button" style="border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                  <div data-layer="_Button base" class="ButtonBase" style="padding-left: 14px; padding-right: 14px; padding-top: 8px; padding-bottom: 8px; background: white; overflow: hidden; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 8px; display: flex">
                    <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.99984 18.3333C14.6022 18.3333 18.3332 14.6023 18.3332 9.99993C18.3332 5.39756 14.6022 1.6666 9.99984 1.6666C5.39746 1.6666 1.6665 5.39756 1.6665 9.99993C1.6665 14.6023 5.39746 18.3333 9.99984 18.3333Z" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M6.6748 12.0917C6.8248 12.3417 7.00813 12.5751 7.21647 12.7834C8.7498 14.3167 11.2415 14.3167 12.7831 12.7834C13.4081 12.1584 13.7665 11.3667 13.8831 10.5583" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M6.1167 9.44175C6.23337 8.62508 6.59171 7.84171 7.21671 7.21671C8.75004 5.68338 11.2417 5.68338 12.7834 7.21671C13 7.43338 13.175 7.66673 13.325 7.90839" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M6.5166 14.3165V12.0915H8.74159" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M13.4833 5.6832V7.90818H11.2583" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                    <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Refresh charts</div>
                  </div>
                </div>
              </div>
              <div data-layer="table" class="Table" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                <div data-layer="columns" class="Columns" style="flex: 1 1 0; justify-content: flex-start; align-items: flex-start; display: flex">
                  <div data-layer="column" class="Column" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                      <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                        <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member ID</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">302274401</div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">7729471914</div>
                    </div>
                  </div>
                  <div data-layer="column" class="Column" style="width: 200px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                      <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Name</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Alma Anders</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Joanne Smith</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="column" class="Column" style="width: 155px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                      <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DOB</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12/15/1953</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">06/30/1951</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="column" class="Column" style="width: 100px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                      <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                        <div data-layer="Label" class="Label" style="width: 27px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">LOB</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="column" class="Column" style="width: 93px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                      <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Measure</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="column" class="Column" style="width: 140px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                      <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review 1</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Susana Cu</div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Susana Cu</div>
                    </div>
                  </div>
                  <div data-layer="column" class="Column" style="width: 140px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                      <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review 2</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
                    </div>
                  </div>
                  <div data-layer="column" class="Column" style="width: 170px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                      <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Assigned</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
                    </div>
                  </div>
                  <div data-layer="column" class="Column" style="width: 118px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                      <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: #384455; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Status</div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="sumbit-button" data-property-1="Inactive" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                        <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #BFD0EE; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                          <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                        </div>
                      </div>
                    </div>
                    <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                      <div data-layer="sumbit-button" data-property-1="Inactive" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                        <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #BFD0EE; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                          <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>