<div data-layer="Comment box" class="CommentBox" style="width: 1242px; height: 770px; position: relative; background: white; overflow: hidden">
  <div data-layer="Behavior: Scrolls with page. Interactive in default state, but not in inactive state. Allows copy/paste." class="BehaviorScrollsWithPageInteractiveInDefaultStateButNotInInactiveStateAllowsCopyPaste" style="width: 1039.94px; height: 16px; left: 111px; top: 188.12px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Behavior: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Scrolls with page. Interactive in default state, but not in inactive state. Allows copy/paste.</span></div>
  <div data-layer="Usage: Comment on the tool’s findings in order to make notes for the overreader to reference." class="UsageCommentOnTheToolSFindingsInOrderToMakeNotesForTheOverreaderToReference" style="width: 1478.31px; left: 111.83px; top: 144.12px; position: absolute"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Usage: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; word-wrap: break-word">Comment on the tool’s findings in order to make notes for the overreader to reference.</span></div>
  <div data-layer="Comment box" class="CommentBox" style="left: 111px; top: 91px; position: absolute; color: black; font-size: 24px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Comment box</div>
  <div data-layer="Default: no comment entered" class="DefaultNoCommentEntered" style="left: 517.80px; top: 290.79px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Default: no comment entered</div>
  <div data-layer="Active: user clicks on comment box and/or is typing" class="ActiveUserClicksOnCommentBoxAndOrIsTyping" style="left: 517.80px; top: 339.79px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Active: user clicks on comment box and/or is typing</div>
  <div data-layer="Entered: comment entered, user is no longer typing" class="EnteredCommentEnteredUserIsNoLongerTyping" style="left: 517.80px; top: 388.79px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Entered: comment entered, user is no longer typing</div>
  <div data-svg-wrapper data-layer="Vector 5" class="Vector5" style="left: 477.01px; top: 290.79px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.00585938 0.989502H34.1885" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 7" class="Vector7" style="left: 477.01px; top: 339.79px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.00585938 0.989502H34.1885" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 6" class="Vector6" style="left: 477.01px; top: 388.79px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.00585938 0.989502H34.1885" stroke="black"/>
    </svg>
  </div>
  <div data-layer="Comment-box" class="CommentBox" style="width: 256px; height: 170px; left: 231.71px; top: 255.79px; position: absolute; overflow: hidden; border-radius: 5px; border: 1px #9747FF solid">
    <div data-layer="Property 1=Default" class="Property1Default" style="width: 216px; height: 30px; padding: 4px; left: 20px; top: 20px; position: absolute; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
      <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
        <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
      </div>
    </div>
    <div data-layer="Property 1=Active" class="Property1Active" style="width: 216px; height: 30px; padding: 4px; left: 20px; top: 70px; position: absolute; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-3, #547996) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
      <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
        <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--text-black, #17181A); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
      </div>
    </div>
    <div data-layer="Property 1=Entered" class="Property1Entered" style="width: 216px; height: 30px; padding: 4px; left: 20px; top: 120px; position: absolute; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
      <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
        <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--text-black, #17181A); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
      </div>
    </div>
  </div>
  <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="width: 216px; height: 30px; padding: 4px; left: 248.17px; top: 515.14px; position: absolute; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
    <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
      <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
    </div>
  </div>
  <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="width: 216px; height: 30px; padding: 4px; left: 252.17px; top: 670.61px; position: absolute; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
    <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
      <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
    </div>
  </div>
  <div data-layer="Spacing:" class="Spacing" style="width: 83px; height: 15.69px; left: 190.21px; top: 477.45px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Spacing:</div>
  <div data-svg-wrapper data-layer="Rectangle 259" class="Rectangle259" style="left: 248.17px; top: 515.14px; position: absolute">
    <svg width="17" height="31" viewBox="0 0 17 31" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.173828" y="0.341064" width="16" height="30" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 262" class="Rectangle262" style="left: 448.17px; top: 515.14px; position: absolute">
    <svg width="17" height="31" viewBox="0 0 17 31" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.173828" y="0.341064" width="16" height="30" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-layer="Rectangle 260" class="Rectangle260" style="width: 9px; height: 216px; left: 248.17px; top: 524.14px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 261" class="Rectangle261" style="width: 9px; height: 216px; left: 248.17px; top: 545.14px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 248.61px; top: 547.98px; position: absolute">
    <svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.61328 0.186035L0.613281 8.46629L16.6133 8.46629L16.6133 0.186034M8.61329 8.54448L8.61329 19.9666" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 226.36px; top: 515.14px; position: absolute">
    <svg width="21" height="11" viewBox="0 0 21 11" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.1399 1.34106L11.8596 1.34106L11.8596 10.3411L20.1399 10.3411M11.7814 5.84107L0.359375 5.84107" stroke="black"/>
    </svg>
  </div>
  <div data-layer="16px" class="Px" style="left: 245.11px; top: 575.62px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">16px</div>
  <div data-layer="9px" class="Px" style="left: 199.96px; top: 519.64px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">9px</div>
  <div data-layer="Styling:" class="Styling" style="width: 83px; height: 15.69px; left: 201.17px; top: 624.44px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Styling:</div>
  <div data-layer="Font: Urbane light Font-size: 10px Color: gray-3" class="FontUrbaneLightFontSize10pxColorGray3" style="left: 119px; top: 670.61px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: Urbane light<br/>Font-size: 10px<br/>Color: gray-3</div>
  <div data-svg-wrapper data-layer="Vector 1" class="Vector1" style="left: 231.04px; top: 685.61px; position: absolute">
    <svg width="28" height="2" viewBox="0 0 28 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M27.6738 0.814208L0.0449219 0.814209" stroke="black"/>
    </svg>
  </div>
  <div data-layer="corner-radius: 10 border-style: solid  border-color: gray-2" class="CornerRadius10BorderStyleSolidBorderColorGray2" style="left: 480.65px; top: 657.61px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">corner-radius: 10<br/>border-style: solid  border-color: gray-2</div>
</div>