<div class="none-found-tab-content" [formGroup]="formGroup">

  <!-- Form Fields Section - exact Figma frame-916 -->
  <div class="frame-916">

    <!-- Reasoning Dropdown - exact Figma frame-942 -->
    <div class="frame-942">
      <span class="text-reasoning">Reasoning</span>
      <app-dropdown
        placeholder="Select"
        [options]="noneFoundReasoningOptions"
        [multiSelect]="true"
        formControlName="reasoning"
        [disabled]="disabled"
        class="dropdown-none"
        (selectionChange)="onFormFieldChange()">
      </app-dropdown>
    </div>

    <!-- Notes Field - exact Figma frame-943 -->
    <div class="frame-943">
      <span class="text-notes">Notes</span>
      <app-notes
        label=""
        placeholder="Notes"
        formControlName="notes"
        [disabled]="disabled"
        class="notes"
        (notesChange)="onFormFieldChange()">
      </app-notes>
    </div>

  </div>
</div>
