import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonComponent } from '../buttons/button.component';
import { CheckboxComponent } from '../form-controls/checkbox/checkbox.component';
import { DropdownComponent, DropdownOption } from '../form-controls/dropdown/dropdown.component';
import { CalendarComponent } from '../form-controls/calendar/calendar.component';
import { ResultsComponent, ResultsData } from '../form-controls/results/results.component';

import { AssignedTableComponent } from '../../../features/dashboard/components/assigned-table/assigned-table.component';
import { NotesComponent } from '../notes/notes.component';
import { DemographicsComponent, DemographicsData } from '../demographics/demographics.component';
import { HitsComponent, HitData } from '../hits/hits.component';
import { MenuComponent, UserProfile, MenuItem } from '../menu/menu.component';
// import { IconComponent, IconName } from '../icons/icon.component'; // Temporarily disabled
import { RefreshIconComponent } from '../icons/refresh-icon.component';

@Component({
  selector: 'app-component-test',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonComponent,
    CheckboxComponent,
    DropdownComponent,
    CalendarComponent,
    ResultsComponent,

    AssignedTableComponent,
    NotesComponent,
    DemographicsComponent,
    HitsComponent,
    MenuComponent,
    // IconComponent, // Temporarily disabled
    RefreshIconComponent
  ],
  templateUrl: './component-test.component.html',
  styleUrl: './component-test.component.scss'
})
export class ComponentTestComponent {
  // Button test properties
  buttonClicked(type: string): void {
    console.log(`${type} button clicked`);
  }

  // Form control test properties
  isChecked = false;
  selectedValue: any = null;
  selectedMultipleValues: any[] = [];
  selectedDate = '';
  notesText = '';
  resultsData: ResultsData = {
    category: 'inclusions',
    telehealth: false,
    sys: '',
    dias: '',
    dateOfService: '',
    notes: ''
  };

  exclusionsData: ResultsData = {
    category: 'exclusions',
    telehealth: true,
    sys: '',
    dias: '',
    dateOfService: '',
    notes: 'Sample exclusion note'
  };

  // Demographics data
  demographicsData: DemographicsData = {
    measureTitle: 'Controlling Blood Pressure (CBP)',
    measureSubtitle: 'Measure',
    memberId: '55820474',
    memberName: 'John Dey',
    dateOfBirth: '01/05/1972',
    gender: 'M',
    lob: 'MAHMO',
    providerName: 'Nicolas Dejong PA',
    npi: '882716229'
  };

  // Hits data
  hitsData: HitData[] = [
    {
      id: 'hit-1',
      dateOfService: '07/21/24',
      systolic: 136,
      diastolic: 82,
      page: 2,
      comment: '',
      include: false
    },
    {
      id: 'hit-2',
      dateOfService: '07/21/24',
      systolic: 140,
      diastolic: 82,
      page: 2,
      comment: '',
      include: false
    },
    {
      id: 'hit-3',
      dateOfService: '05/21/24',
      systolic: 150,
      diastolic: 90,
      page: 7,
      comment: '',
      include: false
    }
  ];

  // Dropdown options
  reasoningOptions: DropdownOption[] = [
    { value: 'acute-inpatient', label: 'Acute inpatient and ED visit' },
    { value: 'end-stage-renal', label: 'End-stage renal disease' },
    { value: 'frailty', label: 'Frailty: Member 81+ years as of 12/31 of the MY' },
    { value: 'lidocaine', label: 'Lidocaine and Epinephrine given to patient' },
    { value: 'medicare-isnp', label: 'Medicare member in an Institutional SNP (I-SNP)' },
    { value: 'medicare-ltc', label: 'Medicare member living in long-term care' },
    { value: 'member-66-80', label: 'Member 66-80 years as of 12/31 of the MY' },
    { value: 'member-died', label: 'Member died during the MY' },
    { value: 'hospice', label: 'Member in hospice anytime during the MY' },
    { value: 'non-acute', label: 'Non-acute inpatient admission' },
    { value: 'palliative', label: 'Palliative Care' },
    { value: 'pregnancy', label: 'Pregnancy' },
    { value: 'other', label: 'Other' }
  ];

  // Navigation data
  userProfile: UserProfile = {
    name: 'Jane Chu',
    avatar: ''
  };

  menuItems: MenuItem[] = [
    { label: 'Profile', route: '/profile', icon: '👤' },
    { label: 'Settings', route: '/settings', icon: '⚙️' },
    { label: 'Help', route: '/help', icon: '❓' },
    { label: 'Logout', action: () => this.logout(), icon: '🚪' }
  ];

  // Icon names for testing
  // iconNames: IconName[] = ['refresh', 'back', 'user', 'arrow-down', 'arrow-up', 'arrow-left', 'arrow-right', 'check', 'close', 'search', 'filter', 'sort']; // Temporarily disabled

  onCheckboxChange(checked: boolean): void {
    this.isChecked = checked;
  }

  onDropdownChange(value: any): void {
    this.selectedValue = value;
    console.log('Dropdown selection changed:', value);
  }

  onMultiDropdownChange(values: any[]): void {
    this.selectedMultipleValues = values;
    console.log('Multi-select dropdown changed:', values);
  }

  onDateChange(date: string): void {
    this.selectedDate = date;
    console.log('Date changed:', date);
  }



  onNotesChange(notes: string): void {
    this.notesText = notes;
    console.log('Notes changed:', notes);
  }

  onResultsDataChange(data: ResultsData): void {
    this.resultsData = data;
    console.log('Results data changed:', data);
  }

  onResultsTabChange(tab: string): void {
    console.log('Results tab changed:', tab);
  }

  onDemographicsBackClick(): void {
    console.log('Demographics back button clicked');
  }

  onHitsDataChange(data: HitData[]): void {
    this.hitsData = data;
    console.log('Hits data changed:', data);
  }

  onHitsPageClick(event: { hit: HitData, page: number }): void {
    console.log('Hits page clicked:', event);
  }

  onHitsCommentChange(event: { hit: HitData, comment: string }): void {
    console.log('Hits comment changed:', event);
  }

  onHitsIncludeChange(event: { hit: HitData, include: boolean }): void {
    console.log('Hits include changed:', event);
  }

  // Sample data for assigned table
  sampleCharts = [
    {
      memberId: '55820474',
      name: 'John Dey',
      dob: '01/05/1972',
      lob: 'MA HMO',
      measure: 'CBP',
      review1: 'Jane Chu',
      review2: '-',
      assigned: '04/15/25 1:30pm',
      status: 'Review' as const
    },
    {
      memberId: '302274401',
      name: 'Alma Anders',
      dob: '12/15/1953',
      lob: 'MA HMO',
      measure: 'CBP',
      review1: 'Jane Chu',
      review2: '-',
      assigned: '04/15/25 1:30pm',
      status: 'Review' as const
    },
    {
      memberId: '7729471914',
      name: 'Joanne Smith',
      dob: '06/30/1951',
      lob: 'MA HMO',
      measure: 'CBP',
      review1: 'Jane Chu',
      review2: '-',
      assigned: '04/15/25 1:30pm',
      status: 'Review' as const
    }
  ];

  // Navigation event handlers
  onLogoClick(): void {
    console.log('Logo clicked');
  }

  onUserClick(): void {
    console.log('User clicked');
  }

  onDropdownToggle(isOpen: boolean): void {
    console.log('Dropdown toggled:', isOpen);
  }

  onMenuItemClick(item: MenuItem): void {
    console.log('Menu item clicked:', item);
  }

  logout(): void {
    console.log('Logout clicked');
    // Add logout logic here
  }
}