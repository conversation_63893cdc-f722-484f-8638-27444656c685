<div class="pdf-test-container">
  <div class="test-controls">
    <h2>PDF Viewer Test Component</h2>
    
    <!-- File Input -->
    <div class="file-input-container">
      <label for="pdf-test-file-input" class="file-input-label">
        <span>Load PDF from local file</span>
      </label>
      <input
        type="file"
        id="pdf-test-file-input"
        accept=".pdf"
        (change)="onFileSelected($event)"
        class="file-input"
      />
    </div>
    
    <!-- Configuration Controls -->
    <div class="config-controls">
      <div class="control-group">
        <label>Range Requests:</label>
        <button (click)="toggleRangeRequests()">
          {{ enableRangeRequests ? 'Enabled' : 'Disabled' }}
        </button>
      </div>
      
      <div class="control-group">
        <label>Fit to Page:</label>
        <button (click)="toggleFitToPage()">
          {{ fitToPage ? 'Enabled' : 'Disabled' }}
        </button>
      </div>
      
      <div class="control-group">
        <label>Scroll Mode:</label>
        <select #scrollSelect (change)="changeScrollMode(+scrollSelect.value)">
          <option [value]="0" [selected]="scrollMode === 0">Vertical</option>
          <option [value]="1" [selected]="scrollMode === 1">Horizontal</option>
          <option [value]="2" [selected]="scrollMode === 2">Wrapped</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>Spread Mode:</label>
        <select #spreadSelect (change)="changeSpreadMode(+spreadSelect.value)">
          <option [value]="0" [selected]="spreadMode === 0">None</option>
          <option [value]="1" [selected]="spreadMode === 1">Odd</option>
          <option [value]="2" [selected]="spreadMode === 2">Even</option>
        </select>
      </div>
      
      <!-- Removed outer zoom control as it conflicts with the PDF viewer's internal zoom -->
    </div>
    
    <!-- Debug Info (Hidden with *ngIf but kept for future reference) -->
    <div class="debug-info" *ngIf="false">
      <h3>Debug Information</h3>
      <div><strong>Browser environment:</strong> {{ isBrowser ? 'Browser' : 'Server' }}</div>
      <div><strong>PDF Source:</strong> {{ pdfBase64Source ? 'Available' : 'Not Available' }}</div>
      <div><strong>Current File:</strong> {{ currentFileName || 'None' }}</div>
      <div><strong>Total Pages:</strong> {{ totalPages }}</div>
      <div><strong>Current Page:</strong> {{ currentPage }}</div>
      <!-- Removed outer zoom display as it conflicts with the PDF viewer's internal zoom -->
      <div><strong>Range Requests:</strong> {{ enableRangeRequests ? 'Enabled' : 'Disabled' }}</div>
      <div><strong>Scroll Mode:</strong> {{ scrollMode === 0 ? 'Vertical' : scrollMode === 1 ? 'Horizontal' : 'Wrapped' }}</div>
      <div><strong>Spread Mode:</strong> {{ spreadMode === 0 ? 'None' : spreadMode === 1 ? 'Odd' : 'Even' }}</div>
      <div><strong>Fit to Page:</strong> {{ fitToPage ? 'Enabled' : 'Disabled' }}</div>
    </div>
  </div>
  
  <!-- PDF Viewer -->
  <div class="pdf-viewer-wrapper">
    <ngx-extended-pdf-viewer *ngIf="isBrowser && pdfBase64Source"
      [src]="pdfBase64Source"
      [(page)]="currentPage"
      [zoom]="zoom"
      [defaultZoom]="1.0"
      [autoScale]="'page-fit'"
      (zoomChange)="onZoomChange($event)"
      (pdfLoaded)="onPdfLoaded($event)"
      (pageChange)="onPageChange($event)"
      [height]="viewerHeight"
      [language]="'en-US'"
      [showToolbar]="true"
      [showSidebarButton]="true"
      [showFindButton]="true"
      [showPagingButtons]="true"
      [showZoomButtons]="true"
      [showPresentationModeButton]="true"
      [showOpenFileButton]="true"
      [showPrintButton]="true"
      [showDownloadButton]="true"
      [showBookmodeButton]="'always-in-secondary-menu'"
      [showSecondaryToolbarButton]="true"
      [showRotateButton]="true"
      [showHandToolButton]="true"
      [showScrollingButtons]="'always-in-secondary-menu'"
      [showSpreadButton]="true"
      [showPropertiesButton]="true"
      [scrollMode]="scrollMode"
      [spread]="spreadMode === 0 ? 'off' : spreadMode === 1 ? 'odd' : 'even'"
      [disableForms]="!renderInteractiveForms"
      [minZoom]="minZoom"
      [maxZoom]="maxZoom"
      (error)="onPdfViewerError($event)"
    ></ngx-extended-pdf-viewer>
    
    <!-- Message when no PDF is loaded -->
    <div *ngIf="isBrowser && !pdfBase64Source" class="no-pdf-message">
      <p>Please select a PDF file to view.</p>
    </div>
    
    <!-- Message when not in browser -->
    <div *ngIf="!isBrowser" class="ssr-placeholder">
      PDF Viewer is not available during server-side rendering.
    </div>
  </div>
</div>