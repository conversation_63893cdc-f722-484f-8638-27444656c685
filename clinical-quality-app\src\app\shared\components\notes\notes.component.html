<div class="notes-container"
     [class.disabled]="disabled"
     [class.has-error]="errorMessage">

  <!-- Label (optional, for external use) -->
  <label *ngIf="label"
         class="notes-label"
         [for]="id">
    {{ label }}
    <span *ngIf="required" class="required-indicator">*</span>
  </label>

  <!-- Simple Notes Text Area (Figma design) -->
  <div class="notes-input-wrapper">
    <textarea
      class="notes-textarea"
      [id]="id"
      [name]="name"
      [placeholder]="placeholder"
      [value]="value"
      [disabled]="disabled"
      [required]="required"
      [attr.maxlength]="maxLength || null"
      [attr.aria-describedby]="showCharacterCount ? id + '-char-count' : null"
      [class.focused]="isFocused"
      [class.has-value]="value && value.length > 0"
      (input)="onInput($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"></textarea>

    <!-- Character Counter (bottom-right overlay) -->
    <div *ngIf="showCharacterCount"
         class="character-counter"
         [class.near-limit]="isNearLimit"
         [class.over-limit]="isOverLimit"
         [id]="id + '-char-count'">
      {{ characterCount }}
    </div>
  </div>

  <!-- Error Message -->
  <div *ngIf="errorMessage"
       class="error-message"
       [id]="id + '-error'"
       role="alert">
    {{ errorMessage }}
  </div>
</div>
