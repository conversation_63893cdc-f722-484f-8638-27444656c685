<div class="dropdown-container"
     [class.disabled]="disabled"
     [class.has-error]="errorMessage"
     #dropdownContainer>

  <!-- Label -->
  <label *ngIf="label" [for]="id" class="dropdown-label">
    {{ label }}
    <span *ngIf="required" class="required-indicator">*</span>
  </label>

  <!-- Dropdown Trigger -->
  <div class="dropdown-trigger"
       [class.open]="isOpen"
       [class.disabled]="disabled"
       [class.has-selection]="(multiSelect && selectedValues.length > 0) || (!multiSelect && selectedValue !== null && selectedValue !== undefined)"
       [attr.tabindex]="disabled ? -1 : 0"
       [attr.aria-expanded]="isOpen"
       [attr.aria-haspopup]="true"
       [attr.role]="'combobox'"
       [attr.aria-labelledby]="label ? id + '-label' : null"
       (click)="toggleDropdown()"
       (keydown)="onKeyDown($event)">

    <div class="dropdown-content">
      <span class="dropdown-text"
            [class.placeholder]="(multiSelect && selectedValues.length === 0) || (!multiSelect && (selectedValue === null || selectedValue === undefined))"
            [class.has-value]="(multiSelect && selectedValues.length > 0) || (!multiSelect && selectedValue !== null && selectedValue !== undefined)">
        {{ getDisplayText() }}
      </span>

      <span class="dropdown-icon" [class.rotated]="isOpen">
        <svg width="14" height="8" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 1L7 7L13 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </span>
    </div>
  </div>

  <!-- Dropdown Options -->
  <div class="dropdown-options"
       *ngIf="isOpen"
       [attr.role]="'listbox'"
       [attr.aria-multiselectable]="multiSelect">

    <!-- Separator line -->
    <div class="dropdown-separator"></div>

    <!-- Options list -->
    <div class="dropdown-option"
         *ngFor="let option of options; trackBy: trackByValue"
         [class.selected]="isSelected(option)"
         [class.disabled]="option.disabled"
         [attr.role]="'option'"
         [attr.aria-selected]="isSelected(option)"
         (click)="selectOption(option)">

      <!-- Checkbox for multi-select -->
      <div *ngIf="multiSelect" class="option-checkbox">
        <input type="checkbox"
               [checked]="isSelected(option)"
               [disabled]="option.disabled"
               readonly
               tabindex="-1">
      </div>

      <!-- Option text -->
      <span class="option-text">{{ option.label }}</span>
    </div>
  </div>

  <!-- Error message -->
  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>
</div>
