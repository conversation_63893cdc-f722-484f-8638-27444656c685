@use 'variables' as variables;
@use 'mixins' as mix;

.hits-container {
  padding: 20px; // Exact Figma padding from hits.scss line 4
  background: #ffffff; // Exact Figma background from hits.scss line 16
  border-radius: 8px; // Exact Figma border radius from hits.scss line 12
  border: 1px solid #f1f5f7; // Exact Figma border from hits.scss lines 13-15
  display: flex; // Exact Figma display from hits.scss line 5
  flex-direction: column; // Exact Figma flex-direction from hits.scss line 6
  justify-content: flex-start; // Exact Figma justify from hits.scss line 7
  align-items: flex-start; // Exact Figma align from hits.scss line 9
  gap: 0px; // Exact Figma gap from hits.scss line 10
  width: 100%; // Exact Figma width from hits.scss line 17
  box-sizing: border-box;
}

.hits-header {
  padding: 0px 0px 12px 0px; // Exact Figma padding from hits.scss line 30
  display: flex; // Exact Figma display from hits.scss line 31
  flex-direction: row; // Exact Figma flex-direction from hits.scss line 32
  justify-content: flex-start; // Exact Figma justify from hits.scss line 33
  align-items: center; // Exact Figma align from hits.scss line 35
  gap: 20px; // Exact Figma gap from hits.scss line 36
  width: 100%; // Exact Figma width from hits.scss line 38
  box-sizing: border-box;
}

.hits-title {
  color: #17181A;
  font-family: Urbane;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 32px; /* 160% */
}

.hits-table {
  display: flex; // Exact Figma display from hits.scss line 58
  flex-direction: row; // Exact Figma flex-direction from hits.scss line 59
  justify-content: flex-start; // Exact Figma justify from hits.scss line 60
  align-items: flex-start; // Exact Figma align from hits.scss line 62
  gap: 0px; // Exact Figma gap from hits.scss line 63
  width: 100%; // Exact Figma width from hits.scss line 65
  box-sizing: border-box;
}

.hits-table-columns {
  display: flex; // Exact Figma display from hits.scss line 68
  flex-direction: row; // Exact Figma flex-direction from hits.scss line 69
  justify-content: flex-start; // Exact Figma justify from hits.scss line 70
  align-items: flex-start; // Exact Figma align from hits.scss line 72
  gap: 0px; // Exact Figma gap from hits.scss line 73
  width: 100%; // Exact Figma width from hits.scss line 75
  box-sizing: border-box;
}

.hits-row {
  display: flex;
  align-self: stretch;

  &.highlighted {
    background: #D9E1E7; // Designer-specified blue color for selected row
  }

  &:not(.highlighted) {
    background: variables.$white;
  }
}

.hits-column {
  flex-direction: column; // Exact Figma flex-direction
  justify-content: flex-start; // Exact Figma justify
  align-items: flex-start; // Exact Figma align
  display: inline-flex; // Exact Figma display
  box-sizing: border-box;

  // Set consistent widths for all columns
  &.dos-column {
    width: 80px; // Fixed width for DoS column
  }

  &.sys-column {
    width: 60px; // Fixed width for Sys column
  }

  &.dias-column {
    width: 60px; // Fixed width for Dias column
  }

  &.page-column {
    width: 60px; // Fixed width for Page column
  }

  &.comment-column {
    width: 216px; // Exact Figma width
    flex-direction: column; // Exact Figma flex-direction
    justify-content: flex-start; // Exact Figma justify
    align-items: center; // Exact Figma align
    display: inline-flex; // Exact Figma display
  }

  &.include-column {
    width: 60px; // Exact Figma width
    flex-direction: column; // Exact Figma flex-direction
    justify-content: flex-start; // Exact Figma justify
    align-items: center; // Center align for include column
    display: inline-flex; // Exact Figma display

    .include-column-inner {
      width: 60px; // Exact Figma width
      flex-direction: column; // Exact Figma flex-direction
      justify-content: center; // Exact Figma justify
      align-items: center; // Exact Figma align
      display: flex; // Exact Figma display
    }
  }
}

.header-item {
  //padding: 0px 8px 0px 8px; // Exact Figma padding from hits.scss line 87
  display: flex; // Exact Figma display from hits.scss line 88
  flex-direction: column; // Exact Figma flex-direction from hits.scss line 89
  justify-content: flex-start; // Exact Figma justify from hits.scss line 90
  align-items: flex-start; // Exact Figma align from hits.scss line 92
  gap: 10px; // Exact Figma gap from hits.scss line 93
  border-bottom: 1px solid #f1f5f7; // Exact Figma border from hits.scss lines 95-97
  height: 40px; // Exact Figma height from hits.scss line 98
  width: 100%; // Exact Figma width from hits.scss line 99
  box-sizing: border-box;

  .table-item {
    display: flex; // Exact Figma display from hits.scss line 102
    flex-direction: row; // Exact Figma flex-direction from hits.scss line 103
    justify-content: flex-start; // Exact Figma justify from hits.scss line 104
    align-items: center; // Exact Figma align from hits.scss line 106
    gap: 12px; // Exact Figma gap from hits.scss line 107
    height: 40px; // Exact Figma height from hits.scss line 109
    box-sizing: border-box;

    .label {
      color: #17181A; // Exact Figma color (text-black)
      font-size: 12px; // Exact Figma font size
      font-family: Urbane; // Exact Figma font family
      font-weight: 500; // Exact Figma font weight
      line-height: 20px; // Exact Figma line height
      word-wrap: break-word; // Exact Figma word wrap
    }
  }
}

// Specific header alignment for different columns
.comment-column .header-item {
  align-items: center; // Center align for comment column header

  .table-item {
    justify-content: center; // Center the header text in comment column
    width: 100%;
  }
}

.include-column .header-item {
  align-items: center; // Center align for include column header

  .table-item {
    justify-content: center; // Center the header text in include column
    width: 100%;
  }
}

.table-item {
  height: 40px; // Exact Figma height from hits.scss line 129
  display: flex; // Exact Figma display from hits.scss line 121
  flex-direction: row; // Exact Figma flex-direction from hits.scss line 122
  justify-content: flex-start; // Exact Figma justify from hits.scss line 123
  align-items: center; // Exact Figma align from hits.scss line 125
  gap: 12px; // Exact Figma gap from hits.scss line 126
  box-sizing: border-box;
  width: 100%; // Exact Figma width from hits.scss line 130

  // Default padding for most table items
  padding: 10px 8px 10px 8px; // Exact Figma padding from hits.scss line 120

  &.highlighted {
    background: #D9E1E7; // Designer-specified blue color for selected row
  }

  &:not(.highlighted) {
    background: #ffffff; // Exact Figma background from hits.scss line 128
  }

  // Special styling for comment column items
  .comment-column & {
    padding: 2px 0px 2px 0px; // Exact Figma padding from hits.scss line 660
    justify-content: center; // Exact Figma justify from hits.scss line 663
  }

  .icon-text {
    justify-content: flex-start; // Exact Figma justify
    align-items: center; // Exact Figma align
    gap: 12px; // Exact Figma gap
    display: flex; // Exact Figma display
    width: 100%; // Ensure content takes full width

    .label {
      text-box-trim: trim-both; // Exact Figma text-box-trim
      text-box-edge: cap alphabetic; // Exact Figma text-box-edge
      color: #17181A; // Exact Figma color (text-black)
      font-size: 12px; // Exact Figma font size
      font-family: Urbane; // Exact Figma font family
      font-weight: 300; // Exact Figma font weight
      line-height: 16px; // Exact Figma line height
      word-wrap: break-word; // Exact Figma word wrap
    }
  }
}

.dos-column .cell-content,
.sys-column .cell-content,
.dias-column .cell-content {
  color: variables.$text-black;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  line-height: 16px;
}

.page-link {
  background: none;
  border: none;
  color: variables.$link;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  text-decoration: underline;
  line-height: 16px;
  cursor: pointer;
  padding: 0;

  &:hover {
    opacity: 0.8;
  }

  &:focus {
    outline: 2px solid variables.$primary-blue;
    outline-offset: 2px;
  }
}

// Comment box component styling within hits
.comment-box-component {
  height: 30px; // Exact Figma height from hits.scss line 673
  width: 100%; // Exact Figma width from hits.scss line 674
}

.checkbox-wrapper {
  width: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.include-checkbox {
  @include mix.checkbox;
  width: 16px;
  height: 16px;
  margin: 0;
}

.checkbox-label {
  display: none; // Hidden but accessible for screen readers
}

// Responsive design
@include mix.for-phone-only {
  .hits-container {
    padding: 16px;
  }

  .hits-table {
    overflow-x: auto;
  }

  .hits-column {
    min-width: 60px;
    padding: 0; // Remove padding to maintain alignment

    &.dos-column {
      width: 70px; // Slightly smaller on mobile
    }

    &.sys-column {
      width: 50px; // Slightly smaller on mobile
    }

    &.dias-column {
      width: 50px; // Slightly smaller on mobile
    }

    &.page-column {
      width: 50px; // Slightly smaller on mobile
    }

    &.comment-column {
      width: 180px;
    }
  }

  // Comment input styling removed - handled by CommentBoxComponent
}

@include mix.for-tablet-portrait-up {
  .hits-container {
    padding: 20px;
  }
}
