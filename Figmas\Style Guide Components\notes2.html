<div class="notes_506-14219">
  <div class="rectangle-325_506-14296">
    <svg
      width="449"
      height="464"
      viewBox="0 0 449 464"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.837891" width="447.887" height="464" fill="#F2F2F2" />
    </svg>
  </div>
  <div class="rectangle-315_506-14220">
    <svg
      width="444"
      height="119"
      viewBox="0 0 444 119"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.798828"
        y="0.650635"
        width="442.926"
        height="117.464"
        fill="#F2F2F2"
      />
    </svg>
  </div>
  <div class="rectangle-328_506-14312">
    <svg
      width="444"
      height="118"
      viewBox="0 0 444 118"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.798828"
        y="0.213623"
        width="442.926"
        height="117.464"
        fill="#F2F2F2"
      />
    </svg>
  </div>
  <div class="rectangle-329_506-14331">
    <svg
      width="444"
      height="118"
      viewBox="0 0 444 118"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.798828"
        y="0.422363"
        width="442.926"
        height="117.464"
        fill="#F2F2F2"
      />
    </svg>
  </div>
  <div class="table_506-14300">
    <div class="text-area-field_506-14301">
      <div class="input-field_506-14302">
        <div class="text_506-14303">
          <span class="text-input-text_506-14304">Notes</span>
        </div>
        <div class="lead-icon---text_506-14305">
          <div class="text_506-14306">
            <span class="text-input-text_506-14307">0/200</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="table_506-14313">
    <div class="text-area-field_506-14314">
      <div class="input-field_506-14315">
        <div class="text_506-14316">
          <span class="text-input-text_506-14317">Notes</span>
        </div>
        <div class="lead-icon---text_506-14318">
          <div class="text_506-14319"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="table_506-14332">
    <div class="text-area-field_506-14333">
      <div class="input-field_506-14334">
        <div class="text_506-14335">
          <span class="text-input-text_506-14336"
            >Active text added to notes</span
          >
        </div>
        <div class="lead-icon---text_506-14337">
          <div class="text_506-14338"></div>
        </div>
      </div>
    </div>
  </div>
  <span
    class="text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state--allows-copy-paste-_506-14223"
    >Behavior: Scrolls with page. Interactive in both default and entered state.
    Allows copy/paste.</span
  >
  <span
    class="text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state--allows-copy-paste-_506-14224"
    >Behavior: Scrolls with page. Interactive in both default and entered state.
    Allows copy/paste.</span
  >
  <span
    class="text-entry--date-entry-is-manual--done-by-typing-or-copy-paste-_506-14225"
    >Entry: Date entry is manual, done by typing or copy/paste.</span
  >
  <span
    class="text-usage--text-entry-for-notes-related-to-chart-review-_506-14226"
    >Usage: Text entry for notes related to chart review.</span
  >
  <span class="text-notes_506-14227">Notes</span>
  <span class="text-default--no-text-entered_506-14229"
    >Default: no text entered</span
  >
  <div class="vector-40_506-14232">
    <svg
      width="35"
      height="2"
      viewBox="0 0 35 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.275391 1H34.458" stroke="black" />
    </svg>
  </div>
  <span class="text-spacing-_506-14260">Spacing:</span>
  <span class="text-styling-_506-14261">Styling:</span>
  <div class="rectangle-322_506-14262">
    <svg
      width="410"
      height="9"
      viewBox="0 0 410 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        y="8.38257"
        width="8"
        height="409.957"
        transform="rotate(-90 0 8.38257)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-326_506-14308">
    <svg
      width="410"
      height="9"
      viewBox="0 0 410 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        y="8.38257"
        width="8"
        height="409.957"
        transform="rotate(-90 0 8.38257)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="vector_506-14267">
    <svg
      width="21"
      height="10"
      viewBox="0 0 21 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.6184 1.38257L12.3382 1.38257L12.3382 9.38257L20.6184 9.38257M12.26 5.38257L0.837891 5.38257"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_506-14310">
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.999999 0.635009L1 6.04542L13 6.04542L13 0.635009M7 6.09651L7.00001 13.5598"
        stroke="black"
      />
    </svg>
  </div>
  <span class="text-8px_506-14268">8px</span>
  <span class="text-12px_506-14311">12px</span>
  <div class="rectangle-318_506-14277">
    <svg
      width="12"
      height="89"
      viewBox="0 0 12 89"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        y="0.382568"
        width="12"
        height="88"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-327_506-14309">
    <svg
      width="12"
      height="89"
      viewBox="0 0 12 89"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        y="0.382568"
        width="12"
        height="88"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <span
    class="text-corner-radius--8-border-style--solid-border-color--gray-1_506-14278"
    >corner-radius: 8 border-style: solidborder-color: gray-1</span
  >
  <span class="text-font--input-text-color--gray-3_506-14279"
    >Font: Input text Color: gray-3</span
  >
  <span
    class="text-font--input-text-color--text-black--when-typing-or-typed-_506-14341"
    >Font: Input text Color: text black (when typing or typed)</span
  >
  <div class="vector-36_506-14283">
    <svg
      width="25"
      height="2"
      viewBox="0 0 25 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.275391 1.25757L24.6768 1.25757" stroke="black" />
    </svg>
  </div>
  <div class="vector-47_506-14343">
    <svg
      width="65"
      height="2"
      viewBox="0 0 65 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.769531 1.46631L64.6768 1.46631" stroke="black" />
    </svg>
  </div>
  <div class="notes_506-14507">
    <div class="property-1-active_506-14506">
      <div class="text-area-field_506-14499">
        <div class="input-field_506-14500">
          <div class="text_506-14501">
            <span class="text-input-text_506-14502">BP under 140/90 in MY</span>
          </div>
          <div class="lead-icon---text_506-14503">
            <div class="text_506-14504"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="property-1-exclusions-active_749-9276">
      <div class="text-area-field_749-9277">
        <div class="input-field_749-9278">
          <div class="text_749-9279">
            <span class="text-input-text_749-9280"
              >Member in hospice during MY</span
            >
          </div>
          <div class="lead-icon---text_749-9281">
            <div class="text_749-9282"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="property-1-default_506-14505">
      <div class="text-area-field_506-14288">
        <div class="input-field_506-14289">
          <div class="text_506-14290">
            <span class="text-input-text_506-14291">Notes</span>
          </div>
        </div>
      </div>
    </div>
    <div class="property-1-exclusions-default_749-9283">
      <div class="text-area-field_749-9284">
        <div class="input-field_749-9285">
          <div class="text_749-9286">
            <span class="text-input-text_749-9287">Notes</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
