<div class="results-v2_929-2981">
  <div class="rectangle-325_929-2982">
    <svg
      width="557"
      height="394"
      viewBox="0 0 557 394"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.951172"
        y="0.541992"
        width="556.035"
        height="392.867"
        fill="#F2F2F2"
      />
    </svg>
  </div>
  <div class="rectangle-343_1102-539">
    <svg
      width="557"
      height="394"
      viewBox="0 0 557 394"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.951172"
        y="0.541992"
        width="556.035"
        height="392.867"
        fill="#F2F2F2"
      />
    </svg>
  </div>
  <div class="notes_929-7609">
    <div class="head_929-7610">
      <div class="frame-929_929-7611">
        <div class="stack_929-7612">
          <span class="text-label_929-7613">Results</span>
          <div class="right-corner_929-7614">
            <span class="text-8px_929-3175">8px</span>
            <span class="text-16px_929-3176">16px</span>
            <span class="text-8px_929-3177">8px</span>
          </div>
        </div>
        <div class="menu_929-7615">
          <div class="second-menu_929-7616">
            <div class="bradcrumb_929-7617">
              <div class="box_929-7618">
                <div class="menu_929-7619">
                  <div class="menu-item_929-7620">
                    <div class="menu-item_929-7621">
                      <div class="frame-831_929-7622">
                        <span class="text-label_929-7624">Inclusions</span>
                      </div>
                    </div>
                  </div>
                  <div class="menu-item-_929-7626">
                    <div class="menu-item_929-7627">
                      <div class="frame-831_929-7628">
                        <span class="text-label_929-7630">Exclusions</span>
                      </div>
                    </div>
                    <div class="menu-item_929-7632">
                      <div class="frame-831_929-7633">
                        <span class="text-label_929-7635">None found</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="frame-916_929-7637">
      <div class="frame-942_929-7638">
        <app-check [property1]="'default'" class="check_929-7639"></app-check>
        <span class="text-teleheath_929-7640">Teleheath</span>
      </div>
      <div class="frame-936_929-7641">
        <div class="frame-939_929-7642">
          <div class="frame-940_929-7643">
            <span class="text-sys_929-7644">Sys</span>
            <div class="dropdown-inclusion_929-7645">
              <div class="row_929-7646">
                <span class="text-value_929-7647">Value</span>
              </div>
            </div>
          </div>
          <div class="frame-941_929-7648">
            <span class="text-dias_929-7649">Dias</span>
            <div class="dropdown-inclusion_929-7650">
              <div class="row_929-7651">
                <span class="text-value_929-7652">Value</span>
              </div>
            </div>
          </div>
          <div class="frame-937_929-7653">
            <span class="text-date-of-service_929-7654">Date of Service</span>
            <app-calendar
              [property1]="'Default'"
              class="calendar_929-7655"
            ></app-calendar>
          </div>
        </div>
      </div>
      <div class="frame-938_929-7656">
        <span class="text-notes_929-7657">Notes</span>
        <app-notes [property1]="'Default'" class="notes_929-7658"></app-notes>
        <div class="vector_1102-540">
          <svg
            width="6"
            height="21"
            viewBox="0 0 6 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.35351 0.555908L1.35352 8.83616L5.35352 8.83616L5.35351 0.555907M3.35352 8.91435L3.35352 20.3364"
              stroke="black"
            />
          </svg>
        </div>
        <div class="vector_1102-542">
          <svg
            width="6"
            height="21"
            viewBox="0 0 6 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.45703 0.555908L1.45703 8.83616L5.45703 8.83616L5.45703 0.555907M3.45703 8.91435L3.45703 20.3364"
              stroke="black"
            />
          </svg>
        </div>
        <span class="text-4px_1102-541">4px</span>
        <span class="text-4px_1102-543">4px</span>
      </div>
    </div>
  </div>
  <div class="rectangle-337_929-2983">
    <svg
      width="554"
      height="408"
      viewBox="0 0 554 408"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.380859" width="553.986" height="407.499" fill="#F2F2F2" />
    </svg>
  </div>
  <div class="notes_929-3537">
    <div class="head_929-3538">
      <div class="frame-929_929-3539">
        <div class="stack_929-3540">
          <span class="text-label_929-3541">Results</span>
          <div class="right-corner_929-3542"></div>
        </div>
        <div class="menu_929-3543">
          <div class="second-menu_929-3544">
            <div class="bradcrumb_929-3545">
              <div class="box_929-3546">
                <div class="menu_929-3547">
                  <div class="menu-item_929-3548">
                    <div class="menu-item_929-3549">
                      <div class="frame-831_929-3550">
                        <span class="text-label_929-3552">Inclusions</span>
                      </div>
                    </div>
                  </div>
                  <div class="menu-item-_929-3554">
                    <div class="menu-item_929-3555">
                      <div class="frame-831_929-3556">
                        <span class="text-label_929-3558">Exclusions</span>
                      </div>
                    </div>
                    <div class="menu-item_929-3560">
                      <div class="frame-831_929-3561">
                        <span class="text-label_929-3563">None found</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="frame-916_929-3565">
      <div class="frame-942_929-3566">
        <app-check [property1]="'default'" class="check_929-3567"></app-check>
        <span class="text-teleheath_929-3568">Teleheath</span>
      </div>
      <div class="frame-936_929-3569">
        <div class="frame-939_929-3570">
          <div class="frame-940_929-3571">
            <span class="text-sys_929-3572">Sys</span>
            <div class="dropdown-inclusion_929-3573">
              <div class="row_929-3574">
                <span class="text-value_929-3575">Value</span>
              </div>
            </div>
          </div>
          <div class="frame-941_929-3576">
            <span class="text-dias_929-3577">Dias</span>
            <div class="dropdown-inclusion_929-3578">
              <div class="row_929-3579">
                <span class="text-value_929-3580">Value</span>
              </div>
            </div>
          </div>
          <div class="frame-937_929-3581">
            <span class="text-date-of-service_929-3582">Date of Service</span>
            <app-calendar
              [property1]="'Default'"
              class="calendar_929-3583"
            ></app-calendar>
          </div>
        </div>
      </div>
      <div class="frame-938_929-3584">
        <span class="text-notes_929-3585">Notes</span>
        <app-notes [property1]="'Default'" class="notes_929-3586"></app-notes>
      </div>
    </div>
  </div>
  <div class="rectangle-315_929-2984">
    <svg
      width="558"
      height="1173"
      viewBox="0 0 558 1173"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.0722656"
        y="0.935791"
        width="557.689"
        height="1171.66"
        fill="#F2F2F2"
      />
    </svg>
  </div>
  <span class="text-results-container-v2_929-3178">Results Container V2</span>
  <span
    class="text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state--allows-copy-paste-_929-3179"
    >Behavior: Scrolls with page. Interactive in both default and entered state.
    Allows copy/paste.</span
  >
  <span
    class="text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state--allows-copy-paste-_929-3180"
    >Behavior: Scrolls with page. Interactive in both default and entered state.
    Allows copy/paste.</span
  >
  <span
    class="text-entry--has-a-dropdown--reasoning---date-entry--date-of-service---and-text-entry--notes-_929-3181"
    >Entry: Has a dropdown (Reasoning), date entry (Date of Service), and text
    entry (Notes)</span
  >
  <span
    class="text-usage--enter-findings-after-finding-them-in-the-chart-and-validating-them--there-are-three-versions--inclusions--exclusions--and-none-found-_929-3182"
    >Usage: Enter findings after finding them in the chart and validating them.
    There are three versions: Inclusions, Exclusions, and None found.</span
  >
  <span class="text-default--inactive--nothing-entered_929-3183"
    >Default: inactive, nothing entered</span
  >
  <span
    class="text-text-entry--sys--dias--and-dos-auto-populate-when-a-measure-is-included-and-allow-for-copy-paste_929-3531"
    >Text-entry: Sys, Dias, and DoS auto-populate when a measure is included and
    allow for copy/paste</span
  >
  <span
    class="text-inclusions--reasoning--date-of-service--and-notes-shown_929-3184"
    >Inclusions: Reasoning, Date of Service, and Notes shown</span
  >
  <span class="text-categories-are-in-tabs_929-3185"
    >Categories are in tabs</span
  >
  <span
    class="text-exclusions--reasoning--date-of-service--and-notes-shown_929-3186"
    >Exclusions: Reasoning, Date of Service, and Notes shown</span
  >
  <span class="text-component--dropdown-exclusion_929-3533"
    >Component: Dropdown-exclusion</span
  >
  <span
    class="text-all-fields-have-labels-above-font--urbane-light-font-size--10px-font-color--gray3-line-spacing--20px_929-3597"
    >All fields have labels above Font: Urbane light font-size: 10px font-color:
    gray3 line-spacing: 20px</span
  >
  <span class="text-component--dropdown-none_929-3535"
    >Component: Dropdown-none</span
  >
  <span class="text-none-found--reasoning--notes-shown_929-3187"
    >None found: Reasoning, Notes shown</span
  >
  <span class="text-second-menu--stroke-bottom-size--1px-color--gray-1_929-3189"
    >Second menu: stroke bottom Size: 1px Color: gray-1</span
  >
  <span
    class="text-menu-item--active--font--h2-color--primary-blue-stroke--bottom--1px--primary-blue_929-3190"
    >Menu item (active) Font: H2 Color: primary-blue Stroke: bottom, 1px,
    primary-blue</span
  >
  <span
    class="text-menu-item--inactive--font--h2-color--gray-3-stroke--none_929-3191"
    >Menu item (inactive) Font: H2 Color: gray-3 Stroke: none</span
  >
  <div class="vector-40_929-3194">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.898438 1.20337H35.0811" stroke="black" />
    </svg>
  </div>
  <div class="vector-42_929-3195">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.898438 0.703369H35.0811" stroke="black" />
    </svg>
  </div>
  <div class="vector-45_929-3196">
    <svg
      width="35"
      height="2"
      viewBox="0 0 35 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.597656 0.703369H34.7803" stroke="black" />
    </svg>
  </div>
  <div class="vector-43_929-3197">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.931641 0.703369H35.1143" stroke="black" />
    </svg>
  </div>
  <div class="vector-53_929-3534">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.931641 1.20337H35.1143" stroke="black" />
    </svg>
  </div>
  <div class="vector-55_929-3598">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.980469 1.20337H35.1631" stroke="black" />
    </svg>
  </div>
  <div class="vector-54_929-3536">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.931641 1.31519H35.1143" stroke="black" />
    </svg>
  </div>
  <div class="vector-52_929-3532">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.931641 0.792236H35.1143" stroke="black" />
    </svg>
  </div>
  <div class="vector-44_929-3198">
    <svg
      width="35"
      height="2"
      viewBox="0 0 35 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.798828 0.703369H34.9814" stroke="black" />
    </svg>
  </div>
  <div class="vector-49_929-3200">
    <svg
      width="35"
      height="2"
      viewBox="0 0 35 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.826172 1.38086H35.0088" stroke="black" />
    </svg>
  </div>
  <div class="vector-50_929-3201">
    <svg
      width="47"
      height="2"
      viewBox="0 0 47 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.3125 0.880859L46.9668 0.880855" stroke="black" />
    </svg>
  </div>
  <div class="vector-51_929-3202">
    <svg
      width="2"
      height="97"
      viewBox="0 0 2 97"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.994141 96.9414L0.994136 0.591798" stroke="black" />
    </svg>
  </div>
  <span class="text-spacing-_929-3205">Spacing:</span>
  <span class="text-styling-_929-3206">Styling:</span>
  <div class="vector_929-3207">
    <svg
      width="22"
      height="21"
      viewBox="0 0 22 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.4082 0.770263L1.4082 9.05052L21.4082 9.05052L21.4082 0.770262M11.4082 9.12871L11.4082 20.5508"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_929-3208">
    <svg
      width="21"
      height="22"
      viewBox="0 0 21 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.3098 0.770508L12.0296 0.770508L12.0296 20.7705H20.3098M11.9514 10.7705L0.529297 10.7705"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_929-3210">
    <svg
      width="42"
      height="14"
      viewBox="0 0 42 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M41.9141 1.47559L24.7471 1.47559L24.7471 13.4756L41.9141 13.4756M24.585 7.47559L0.904297 7.47559"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_929-3211">
    <svg
      width="42"
      height="6"
      viewBox="0 0 42 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M41.9141 0.770508L24.7471 0.770508V4.77051L41.9141 4.77051M24.585 2.77051L0.904297 2.77051"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_929-7671">
    <svg
      width="42"
      height="6"
      viewBox="0 0 42 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M41.9141 0.767578L24.7471 0.767578V4.76758L41.9141 4.76758M24.585 2.76758L0.904297 2.76758"
        stroke="black"
      />
    </svg>
  </div>
  <span class="text-20px_929-3212">20px</span>
  <span class="text-20px_929-3213">20px</span>
  <span class="text-12px_929-3215">12px</span>
  <span class="text-4px_929-3216">4px</span>
  <span class="text-4px_929-7672">4px</span>
  <div class="rectangle-293_929-3217">
    <svg
      width="518"
      height="21"
      viewBox="0 0 518 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.46875"
        y="20.873"
        width="20"
        height="517"
        transform="rotate(-90 0.46875 20.873)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-326_929-3218">
    <svg
      width="518"
      height="21"
      viewBox="0 0 518 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.46875"
        y="20.4756"
        width="20"
        height="517"
        transform="rotate(-90 0.46875 20.4756)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-295_929-3219">
    <svg
      width="21"
      height="360"
      viewBox="0 0 21 360"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="20.4688"
        y="359.476"
        width="20"
        height="358.706"
        transform="rotate(180 20.4688 359.476)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-327_929-3220">
    <svg
      width="21"
      height="366"
      viewBox="0 0 21 366"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="20.4688"
        y="365.476"
        width="20"
        height="364.603"
        transform="rotate(180 20.4688 365.476)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-297_929-3221">
    <svg
      width="478"
      height="13"
      viewBox="0 0 478 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.236328"
        y="12.7705"
        width="12"
        height="477.232"
        transform="rotate(-90 0.236328 12.7705)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-334_929-3222">
    <svg
      width="478"
      height="5"
      viewBox="0 0 478 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.236328"
        y="4.47559"
        width="4"
        height="477.232"
        transform="rotate(-90 0.236328 4.47559)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-341_1102-537">
    <svg
      width="5"
      height="49"
      viewBox="0 0 5 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.46875"
        y="0.433105"
        width="4"
        height="48.0425"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-342_1102-538">
    <svg
      width="5"
      height="49"
      viewBox="0 0 5 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="4.46875"
        y="48.4756"
        width="4"
        height="48.0425"
        transform="rotate(-180 4.46875 48.4756)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-340_929-7673">
    <svg
      width="478"
      height="5"
      viewBox="0 0 478 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.236328"
        y="4.47266"
        width="4"
        height="477.232"
        transform="rotate(-90 0.236328 4.47266)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-339_929-7670">
    <svg
      width="478"
      height="13"
      viewBox="0 0 478 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.236328"
        y="12.623"
        width="12"
        height="477.232"
        transform="rotate(-90 0.236328 12.623)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-338_929-7669">
    <svg
      width="478"
      height="13"
      viewBox="0 0 478 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.236328"
        y="12.4756"
        width="12"
        height="477.232"
        transform="rotate(-90 0.236328 12.4756)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-336_929-3223">
    <svg
      width="478"
      height="13"
      viewBox="0 0 478 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.236328"
        y="12.4756"
        width="12"
        height="477.232"
        transform="rotate(-90 0.236328 12.4756)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-300_929-3225">
    <svg
      width="9"
      height="47"
      viewBox="0 0 9 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.236328"
        y="0.770508"
        width="8"
        height="45.7051"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-328_929-3226">
    <svg
      width="9"
      height="47"
      viewBox="0 0 9 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.9375"
        y="0.770508"
        width="8"
        height="45.7051"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-331_929-3227">
    <svg
      width="9"
      height="47"
      viewBox="0 0 9 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.9375"
        y="0.770508"
        width="8"
        height="45.7051"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-330_929-3228">
    <svg
      width="17"
      height="47"
      viewBox="0 0 17 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.9375"
        y="0.770508"
        width="16"
        height="45.7051"
        fill="#12AFF0"
        fill-opacity="0.4"
      />
    </svg>
  </div>
  <div class="rectangle-332_929-3229">
    <svg
      width="17"
      height="47"
      viewBox="0 0 17 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.9375"
        y="0.770508"
        width="16"
        height="45.7051"
        fill="#12AFF0"
        fill-opacity="0.4"
      />
    </svg>
  </div>
  <div class="rectangle-329_929-3230">
    <svg
      width="9"
      height="47"
      viewBox="0 0 9 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.9375"
        y="0.770508"
        width="8"
        height="45.7051"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-333_929-3231">
    <svg
      width="9"
      height="47"
      viewBox="0 0 9 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.9375"
        y="0.770508"
        width="8"
        height="45.7051"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <span
    class="text-corner-radius--8-border-style--solid-border-color--gray-1_929-3232"
    >corner-radius: 8 border-style: solidborder-color: gray-1</span
  >
  <div class="notes_924-2278">
    <div class="head_924-2279">
      <div class="frame-929_924-2280">
        <div class="stack_924-2281">
          <span class="text-label_924-2282">Results</span>
          <div class="right-corner_924-2283"></div>
        </div>
        <div class="menu_924-2284">
          <div class="second-menu_924-2285">
            <div class="bradcrumb_924-2286">
              <div class="box_924-2287">
                <div class="menu_924-2288">
                  <div class="menu-item_924-2289">
                    <div class="menu-item_924-2290">
                      <div class="frame-831_924-2291">
                        <span class="text-label_924-2293">Inclusions</span>
                      </div>
                    </div>
                  </div>
                  <div class="menu-item-_924-2295">
                    <div class="menu-item_924-2296">
                      <div class="frame-831_924-2297">
                        <span class="text-label_924-2299">Exclusions</span>
                      </div>
                    </div>
                    <div class="menu-item_924-2301">
                      <div class="frame-831_924-2302">
                        <span class="text-label_924-2304">None found</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="frame-916_924-2306">
      <div class="frame-942_924-2307">
        <app-check [property1]="'default'" class="check_924-2308"></app-check>
        <span class="text-teleheath_924-2309">Teleheath</span>
      </div>
      <div class="frame-936_924-2310">
        <div class="frame-939_924-2311">
          <div class="frame-940_924-2312">
            <span class="text-sys_924-2313">Sys</span>
            <div class="dropdown-inclusion_924-2314">
              <div class="row_924-2315">
                <span class="text-value_924-2316">Value</span>
              </div>
            </div>
          </div>
          <div class="frame-941_924-2317">
            <span class="text-dias_924-2318">Dias</span>
            <div class="dropdown-inclusion_924-2319">
              <div class="row_924-2320">
                <span class="text-value_924-2321">Value</span>
              </div>
            </div>
          </div>
          <div class="frame-937_924-2322">
            <span class="text-date-of-service_924-2323">Date of Service</span>
            <app-calendar
              [property1]="'Default'"
              class="calendar_924-2324"
            ></app-calendar>
          </div>
        </div>
      </div>
      <div class="frame-938_924-2325">
        <span class="text-notes_924-2326">Notes</span>
        <app-notes [property1]="'Default'" class="notes_924-2327"></app-notes>
      </div>
    </div>
  </div>
  <div class="notes_929-3472">
    <div class="head_929-3473">
      <div class="frame-929_929-3474">
        <div class="stack_929-3475">
          <span class="text-label_929-3476">Results</span>
          <div class="right-corner_929-3477"></div>
        </div>
        <div class="menu_929-3478">
          <div class="second-menu_929-3479">
            <div class="bradcrumb_929-3480">
              <div class="box_929-3481">
                <div class="menu_929-3482">
                  <div class="menu-item_929-3483">
                    <div class="menu-item_929-3484">
                      <div class="frame-831_929-3485">
                        <span class="text-label_929-3487">Inclusions</span>
                      </div>
                    </div>
                  </div>
                  <div class="menu-item-_929-3489">
                    <div class="menu-item_929-3490">
                      <div class="frame-831_929-3491">
                        <span class="text-label_929-3493">Exclusions</span>
                      </div>
                    </div>
                  </div>
                  <div class="menu-item_929-3495">
                    <div class="menu-item_929-3496">
                      <div class="frame-831_929-3497">
                        <span class="text-label_929-3499">None found</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="frame-916_929-3501">
      <div class="frame-939_929-3502">
        <span class="text-reasoning_929-3503">Reasoning</span>
        <app-dropdown-exclusion
          [property1]="'Default'"
          class="dropdown-exclusion_929-3504"
        ></app-dropdown-exclusion>
      </div>
      <div class="frame-940_929-3505">
        <span class="text-date-of-service_929-3506">Date of service</span>
        <app-calendar
          [property1]="'Default'"
          class="calendar_929-3507"
        ></app-calendar>
      </div>
      <div class="frame-941_929-3508">
        <span class="text-notes_929-3509">Notes</span>
        <app-notes
          [property1]="'exclusions-default'"
          class="notes_929-3510"
        ></app-notes>
      </div>
    </div>
  </div>
  <div class="notes_929-3424">
    <div class="head_929-3425">
      <div class="frame-929_929-3426">
        <div class="stack_929-3427">
          <span class="text-label_929-3428">Results</span>
          <div class="right-corner_929-3429"></div>
        </div>
        <div class="menu_929-3430">
          <div class="second-menu_929-3431">
            <div class="bradcrumb_929-3432">
              <div class="box_929-3433">
                <div class="menu_929-3434">
                  <div class="menu-item_929-3435">
                    <div class="menu-item_929-3436">
                      <div class="frame-831_929-3437">
                        <span class="text-label_929-3439">Inclusions</span>
                      </div>
                    </div>
                  </div>
                  <div class="menu-item-_929-3441">
                    <div class="menu-item_929-3442">
                      <div class="frame-831_929-3443">
                        <span class="text-label_929-3445">Exclusions</span>
                      </div>
                    </div>
                  </div>
                  <div class="menu-item_929-3447">
                    <div class="menu-item_929-3448">
                      <div class="frame-831_929-3449">
                        <span class="text-label_929-3451">None found</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="frame-916_929-3453">
      <div class="frame-942_929-3454">
        <span class="text-reasoning_929-3455">Reasoning</span>
        <app-dropdown-none
          [property1]="'default'"
          class="dropdown-none_929-3456"
        ></app-dropdown-none>
      </div>
      <div class="frame-943_929-3457">
        <span class="text-notes_929-3458">Notes</span>
        <app-notes [property1]="'Default'" class="notes_929-3459"></app-notes>
      </div>
    </div>
  </div>
</div>
