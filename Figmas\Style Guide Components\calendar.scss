@import "src/app/tokens.scss";

.calendar_912-4317 {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 48px;
  width: 100%;
}
.row_I912-4317-193-3549 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-date-of-service_I912-4317-193-3550 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.icon-interface_I912-4317-193-3562 {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
}
.vector_I912-4317-193-3563 {
  top: 2px;
  left: 4px;
  position: absolute;
  display: flex;
  display: flex;
}
