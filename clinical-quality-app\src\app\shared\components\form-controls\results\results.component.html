<div class="results-container"
     [class.disabled]="disabled"
     [class.has-error]="errorMessage">

  <!-- Results Header -->
  <div class="results-header">
    <div class="results-title-section">
      <h1 class="results-title">{{ title }}</h1>

      <!-- Tab Navigation -->
      <div class="tab-navigation" role="tablist">
        <button
          *ngFor="let tab of tabs"
          type="button"
          class="tab-button"
          [class.active]="tab.active"
          [disabled]="disabled"
          [attr.role]="'tab'"
          [attr.aria-selected]="tab.active"
          [attr.aria-controls]="id + '-' + tab.id + '-panel'"
          [attr.id]="id + '-' + tab.id + '-tab'"
          (click)="selectTab(tab.id)">
          {{ tab.label }}
        </button>
      </div>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">

    <!-- Inclusions Tab -->
    <app-inclusions-tab
      *ngIf="activeTab === 'inclusions'"
      [formGroup]="resultsForm"
      [disabled]="disabled"
      [id]="id + '-inclusions'"
      (fieldChange)="onFormFieldChange()">
    </app-inclusions-tab>

    <!-- Exclusions Tab -->
    <app-exclusions-tab
      *ngIf="activeTab === 'exclusions'"
      [formGroup]="resultsForm"
      [disabled]="disabled"
      [id]="id + '-exclusions'"
      (fieldChange)="onFormFieldChange()">
    </app-exclusions-tab>

    <!-- None Found Tab -->
    <app-none-found-tab
      *ngIf="activeTab === 'none-found'"
      [formGroup]="resultsForm"
      [disabled]="disabled"
      [id]="id + '-none-found'"
      (fieldChange)="onFormFieldChange()">
    </app-none-found-tab>

  </div>

  <!-- Error message -->
  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>
</div>
