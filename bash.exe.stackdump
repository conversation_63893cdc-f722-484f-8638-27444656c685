Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FEBA
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210285FF9, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBB0  0002100690B4 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBE90  00021006A49D (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8E60B0000 ntdll.dll
7FF8E4420000 KERNEL32.DLL
7FF8E3C40000 KERNELBASE.dll
7FF8E5770000 USER32.dll
7FF8E3F40000 win32u.dll
7FF8E4860000 GDI32.dll
7FF8E3FC0000 gdi32full.dll
7FF8E3880000 msvcp_win.dll
000210040000 msys-2.0.dll
7FF8E39E0000 ucrtbase.dll
7FF8E5E70000 advapi32.dll
7FF8E4A50000 msvcrt.dll
7FF8E49B0000 sechost.dll
7FF8E4140000 RPCRT4.dll
7FF8E39B0000 bcrypt.dll
7FF8E2FF0000 CRYPTBASE.DLL
7FF8E3920000 bcryptPrimitives.dll
7FF8E4B60000 IMM32.DLL
7FF8E3400000 hlcnuser.dll
000002630000 umppc19706.dll
