# Decision Log

This file records architectural and implementation decisions using a list format.
2025-04-21 14:33:00 - Log of updates made.

* [2025-04-22 14:21:16] - Implementation of PDF viewer with advanced search functionality
* [2025-04-22 15:05:00] - Secure chart loading approach for handling PHI data
* [2025-04-22 15:13:00] - UI integration approach for incorporating design files
* [2025-04-23 17:40:00] - SCSS modernization and import structure

## Decision - PDF Viewer Implementation

* Use ngx-extended-pdf-viewer as the PDF.js wrapper for the PDF viewer component
      
## Rationale

* ngx-extended-pdf-viewer provides comprehensive features including built-in search functionality
* It has good Angular integration and active maintenance
* It supports text layer for text selection and search, which is critical for our advanced search requirements
* While it has a larger bundle size, the benefits of built-in functionality outweigh the size concerns

## Implementation Details

* Implement custom search service to extend the built-in search functionality
* Use progressive loading and caching strategies for large PDFs (100+ pages)
* Integrate with storage service for saving search history and results
* Implement UI for advanced search options (case-sensitive, whole word)

## Decision - Secure Chart Loading

* Implement a multi-layered approach for handling PHI data in the browser
* Use local file input for testing with real charts containing PHI
* Store charts in IndexedDB for persistence between sessions

## Rationale

* PHI data must remain secure and never be transmitted to external servers
* Local browser storage provides a secure environment for testing with real data
* This approach enables realistic testing while maintaining PHI security

## Implementation Details

* Create assets/sample-charts/ directory with sanitized PDFs for development
* Implement file input component for local chart selection
* Use URL.createObjectURL() to create temporary, local URLs for PDFs
* Implement IndexedDB storage for chart persistence
* Add clear data option for security

## Decision - UI Integration

* Extract design system from provided HTML/CSS files and implement in Angular
* Use Angular Material as the foundation but customize to match the design
* Convert inline styles and styled-components to Angular component styles

## Rationale

* The provided design files represent a complete UI design system
* Angular Material provides a solid foundation for UI components
* Custom styling can be applied to match the exact design specifications

## Implementation Details

* Create custom Angular Material theme based on design colors
* Convert styled-components to SCSS variables and mixins
* Implement shared components (header, buttons, form controls)
* Create dashboard and chart review layouts using Angular Flex Layout
* Add the Urbane font family to the project
* Ensure responsive behavior for different screen sizes

## Decision - SCSS Modernization and Import Structure

* Update Angular Material theme files to use modern @use syntax
* Set up SCSS import aliases in angular.json
* Simplify component SCSS imports with proper namespacing

## Rationale

* Modern Sass best practices recommend @use over deprecated @import
* Proper namespacing prevents variable name collisions
* Path aliases simplify imports and improve maintainability
* Consistent approach across the codebase improves developer experience

## Implementation Details

* Changed @import 'variables'; to @use 'variables' as vars; in _theme.scss
* Updated all variable references to use the namespace (e.g., vars.$primary-blue)
* Made similar updates in _mixins.scss
* Added stylePreprocessorOptions with includePaths for src/styles in angular.json
* Updated component SCSS files to use direct imports with proper namespacing