# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-04-21 14:33:00 - Log of updates made.

*

## Coding Patterns

* **Repository Pattern**: Abstract repository interfaces for each data entity with specific implementations (IndexedDB in Phase 1, API-based for PostgreSQL in Phase 2)
* **Service Layer**: Services that use repositories through dependency injection, keeping business logic independent of storage mechanism
* **UUID Generation**: UUID v4 for all entity IDs to ensure compatibility with PostgreSQL
* **Timestamps**: Include `createdAt` and `updatedAt` fields in all entities with consistent date formatting

## Architectural Patterns

* **Module Structure**: Core Module (services), Shared Module (UI components), Feature Modules (Dashboard, Chart Review)
* **Data Access Abstraction**: Storage module with provider configuration that can be switched between implementations
* **PostgreSQL-Ready Design**: Data models and schema designed to match future PostgreSQL schema
* **Dependency Injection**: Used for swapping storage implementations without changing business logic

## Testing Patterns

* **Unit Testing**: Jasmine/Karma for component and service testing
* **Integration Testing**: For workflow validation
* **Performance Testing**: Especially for large document handling