# Secure Chart Loading Implementation Plan

## Overview

This document outlines the implementation plan for securely loading and handling medical charts with Protected Health Information (PHI) in the Clinical Quality UI application. The approach ensures that PHI remains secure by keeping all data local to the browser and never transmitting it to external servers.

## Implementation Approaches

### 1. Local Assets Folder with Dummy/Sanitized PDFs

- Create an `assets/sample-charts/` directory in the Angular project
- Place sanitized/dummy PDFs (with similar structure but no real PHI) for development
- Configure the PDF service to load from this location during development

```typescript
// In pdf.service.ts
loadSamplePdf(sampleName: string): Observable<any> {
  const url = `assets/sample-charts/${sampleName}.pdf`;
  return this.loadPdf(url);
}
```

### 2. File Input for Testing with Real Charts

- Add a file input component that allows testers to locally select PDFs from their machine
- Files selected this way never leave the user's browser (never uploaded to any server)
- Implementation in chart-review-page:

```html
<!-- In chart-review-page.component.html -->
<div class="file-input-container">
  <input type="file" accept="application/pdf" (change)="onFileSelected($event)" />
  <button mat-raised-button color="primary" (click)="triggerFileInput()">
    <mat-icon>upload_file</mat-icon> Load Chart
  </button>
</div>
```

```typescript
// In chart-review-page.component.ts
@ViewChild('fileInput') fileInput: ElementRef;

triggerFileInput(): void {
  this.fileInput.nativeElement.click();
}

onFileSelected(event: any): void {
  const file = event.target.files[0];
  if (file) {
    // Create a local URL for the file
    this.pdfUrl = URL.createObjectURL(file);
    
    // Optionally store file metadata
    this.storageService.saveChartMetadata({
      id: uuidv4(),
      filename: file.name,
      size: file.size,
      lastModified: new Date(file.lastModified),
      localUrl: this.pdfUrl
    });
  }
}
```

### 3. IndexedDB Storage for Persistence

- Allow saving uploaded PDFs to IndexedDB for persistence between sessions
- Implement storage service methods for PDF storage and retrieval

```typescript
// In storage.service.ts
saveChartFile(chartId: string, file: File): Observable<string> {
  return from(this.db.then(async db => {
    const fileData = await file.arrayBuffer();
    await db.put('chart_files', { chartId, data: fileData });
    return chartId;
  }));
}

getChartFile(chartId: string): Observable<Blob> {
  return from(this.db.then(async db => {
    const fileData = await db.get('chart_files', chartId);
    if (!fileData) {
      throw new Error('Chart file not found');
    }
    return new Blob([fileData.data], { type: 'application/pdf' });
  }));
}
```

## Integration with Existing Components

### Update PDF Viewer Component

Modify the PDF viewer component to handle both sample PDFs and user-uploaded PDFs:

```typescript
// In pdf-viewer.component.ts
@Input() pdfUrl: string;
@Input() pdfBlob: Blob;

ngOnInit(): void {
  if (this.pdfUrl) {
    this.loadPdfFromUrl();
  } else if (this.pdfBlob) {
    this.loadPdfFromBlob();
  }
}

loadPdfFromUrl(): void {
  this.pdfService.loadPdf(this.pdfUrl).subscribe(
    (pdf) => {
      this.pdfDocument = pdf;
      this.totalPages = pdf.numPages;
    },
    (error) => console.error('Error loading PDF:', error)
  );
}

loadPdfFromBlob(): void {
  this.pdfService.loadPdfFromBlob(this.pdfBlob).subscribe(
    (pdf) => {
      this.pdfDocument = pdf;
      this.totalPages = pdf.numPages;
    },
    (error) => console.error('Error loading PDF:', error)
  );
}
```

### Update Chart Review Page

Enhance the chart review page to support multiple chart loading methods:

```typescript
// In chart-review-page.component.ts
chartId: string;
pdfUrl: string;
pdfBlob: Blob;
isLoading = false;

ngOnInit(): void {
  this.chartId = this.route.snapshot.paramMap.get('id') || '';
  
  if (this.chartId) {
    // Load from IndexedDB if chart ID is provided
    this.loadChartFromStorage(this.chartId);
  } else {
    // Default to sample PDF for development
    this.pdfUrl = 'assets/sample-charts/sample-chart.pdf';
  }
}

loadChartFromStorage(chartId: string): void {
  this.isLoading = true;
  this.storageService.getChartFile(chartId).subscribe(
    (blob) => {
      this.pdfBlob = blob;
      this.pdfUrl = URL.createObjectURL(blob);
      this.isLoading = false;
    },
    (error) => {
      console.error('Error loading chart:', error);
      this.isLoading = false;
      // Fall back to sample PDF
      this.pdfUrl = 'assets/sample-charts/sample-chart.pdf';
    }
  );
}
```

## Security Considerations

1. **No Server Transmission**: All chart data remains in the browser and is never transmitted to any server
2. **Local Storage Only**: Charts are stored only in the browser's IndexedDB
3. **URL.createObjectURL**: Creates temporary, local URLs that are only accessible within the browser
4. **Revocation**: Object URLs should be revoked when no longer needed to free memory
5. **Clear Data Option**: Provide users with the ability to clear all stored data

## Implementation Steps

1. Create the `assets/sample-charts/` directory and add sanitized sample PDFs
2. Update the PDF service to support loading from both URLs and Blobs
3. Enhance the chart review page with file input for local chart selection
4. Implement IndexedDB storage for chart files
5. Add UI controls for managing stored charts
6. Implement security measures (URL revocation, clear data option)
7. Test with both sample and real charts (ensuring PHI remains local)

## Conclusion

This implementation plan provides a secure approach to handling medical charts with PHI in the Clinical Quality UI application. By keeping all data local to the browser and never transmitting it to external servers, we ensure that PHI remains secure while still enabling a realistic user experience for testing and development.