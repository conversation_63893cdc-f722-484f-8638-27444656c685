# Product Context
      
This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-04-21 14:32:00 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

* The Clinical Quality UI is an Angular-based application designed to facilitate the review and validation of AI-generated findings in medical charts. The project follows a phased approach, with Phase 1 focusing on an MVP with local storage (IndexedDB) that is designed to be PostgreSQL-ready for future migration.

## Key Features

* **Dashboard View**: Display list of charts/members requiring review with status indicators, filtering, sorting, and basic search functionality.
* **Chart Review View**: PDF viewer integration with advanced Ctrl+F search functionality across all pages, split-screen layout with AI findings, highlighting, comment/annotation capabilities, and validation controls.
* **Advanced Search**: Text search across all PDF pages, optimized for large PDFs, with highlighting of matches and navigation between results.
* **Annotation System**: Highlighting functionality for relevant sections, comment capabilities, and validation controls for AI findings.
* **Save Annotated Charts**: Save user annotations and highlights locally, persist comments and validation decisions, and export annotated charts as PDFs with embedded annotations.

## Overall Architecture

* **Frontend**: Angular 17, Angular Material, NgRx (lightweight), SCSS with Material theming, Jasmine/Karma for testing
* **Data Storage (Phase 1)**: Browser's IndexedDB for structured data, LocalStorage for small configuration data, File system API for PDF storage
* **Architecture Pattern**: Repository pattern with abstract interfaces for data entities, designed for easy migration to PostgreSQL in Phase 2
* **Module Structure**: Core Module (services), Shared Module (UI components), Feature Modules (Dashboard, Chart Review)