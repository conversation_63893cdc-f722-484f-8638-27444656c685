import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CheckboxComponent } from '../form-controls/checkbox/checkbox.component';
import { CommentBoxComponent } from '../form-controls/comment-box/comment-box.component';

export interface HitData {
  id: string;
  dateOfService: string;
  systolic: number;
  diastolic: number;
  page: number;
  comment: string;
  include: boolean;
}

@Component({
  selector: 'app-hits',
  standalone: true,
  imports: [CommonModule, FormsModule, CheckboxComponent, CommentBoxComponent],
  templateUrl: './hits.component.html',
  styleUrls: ['./hits.component.scss']
})
export class HitsComponent implements OnInit {
  @Input() title: string = 'Hits';
  @Input() data: HitData[] = [];
  @Input() showHeader: boolean = true;
  
  // Track the currently selected hit (the one user clicked to navigate to)
  selectedHitId: string | null = null;

  @Output() dataChange = new EventEmitter<HitData[]>();
  @Output() pageClick = new EventEmitter<{ hit: HitData, page: number }>();
  @Output() commentChange = new EventEmitter<{ hit: HitData, comment: string }>();
  @Output() includeChange = new EventEmitter<{ hit: HitData, include: boolean }>();

  ngOnInit(): void {
    if (this.data.length === 0) {
      this.data = this.getDefaultData();
    }

    // No hit should be selected by default
    this.selectedHitId = null;
  }

  onCommentChange(hit: HitData, comment: string): void {
    hit.comment = comment;
    this.commentChange.emit({ hit, comment });
    this.dataChange.emit(this.data);
  }

  onIncludeChange(hit: HitData, include: boolean): void {
    hit.include = include;
    this.includeChange.emit({ hit, include });
    this.dataChange.emit(this.data);
  }

  onPageClick(hit: HitData): void {
    // Set the selected hit when user clicks on a page link
    this.selectedHitId = hit.id;
    this.pageClick.emit({ hit, page: hit.page });
  }

  trackByHitId(_index: number, hit: HitData): string {
    return hit.id;
  }

  private getDefaultData(): HitData[] {
    return [
      {
        id: 'hit-1',
        dateOfService: '07/21/24',
        systolic: 136,
        diastolic: 82,
        page: 2,
        comment: '',
        include: false
      },
      {
        id: 'hit-2',
        dateOfService: '07/21/24',
        systolic: 140,
        diastolic: 82,
        page: 2,
        comment: '',
        include: false
      },
      {
        id: 'hit-3',
        dateOfService: '05/21/24',
        systolic: 150,
        diastolic: 90,
        page: 7,
        comment: '',
        include: false
      }
    ];
  }
}
