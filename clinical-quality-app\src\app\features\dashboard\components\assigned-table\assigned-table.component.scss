@use 'variables' as variables;
@use 'mixins' as mix;

.assigned-table-container {
  width: max-content; // Let table size itself based on content
  background: variables.$white;
  border-radius: 8px;
  border: none; // Remove container border
  overflow: hidden;
  min-width: 1240px; // Total of all column widths (140+180+120+100+120+140+140+160+140)


}

.assigned-table-header {
  display: flex;
  background: variables.$white;
  border-bottom: 1px solid variables.$gray-1 !important; // Keep the header separator line
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
}

.assigned-table-header-cell {
  padding: 16px 12px;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 500;
  color: variables.$text-black;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border: none !important;
  border-right: none !important;
  border-left: none !important;
  border-top: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: variables.$white !important;
}

.assigned-table-body {
  width: 100%;
}

.assigned-table-row {
  display: flex;
  background: variables.$white;
  border: none !important;
  border-bottom: none !important;
  border-top: none !important;
  outline: none !important;
  box-shadow: none !important;

  &:hover {
    background: variables.$gray-1;
  }
}

.assigned-table-cell {
  padding: 16px 12px;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: variables.$text-black;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border: none !important;
  border-right: none !important;
  border-left: none !important;
  border-top: none !important;
  border-bottom: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: variables.$white !important;
}

// Status badge styles removed - now using ButtonComponent with Figma specifications

.no-charts-message {
  padding: 40px 20px;
  text-align: center;
  font-size: 14px;
  font-family: 'Urbane', sans-serif;
  color: variables.$gray-3;
  font-style: italic;
  background: variables.$white;
}

// Responsive behavior
@include mix.for-tablet-landscape-up {
  .assigned-table-container {
    min-width: auto;
  }
}

@include mix.for-phone-only {
  .assigned-table-container {
    min-width: auto;
    overflow-x: auto;
  }

  .assigned-table-header-cell,
  .assigned-table-cell {
    padding: 12px 8px;
    font-size: 11px;
    min-width: 80px;
  }

  .status-badge {
    padding: 6px 12px;
    font-size: 11px;
    min-width: 70px;
  }
}
