<div data-layer="Assigned table" class="AssignedTable" style="width: 1807px; height: 2081px; position: relative; background: white; overflow: hidden">
  <div data-layer="Rectangle 245" class="Rectangle245" style="width: 1512px; height: 426.45px; left: 142.31px; top: 756.40px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="assigned-table" class="AssignedTable" style="width: 1380px; left: 203.99px; top: 791.35px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: inline-flex">
    <div data-layer="Table" class="Table" style="align-self: stretch; padding: 20px; background: var(--white, white); border-radius: 8px; outline: 1px var(--light-borders, #F1F5F7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
      <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 20px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="flex: 1 1 0; justify-content: flex-start; align-items: center; gap: 10px; display: flex">
          <div data-layer="label" class="Label" style="color: var(--text-black, #17181A); font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Assigned charts</div>
          <div data-layer="right corner" class="RightCorner" style="flex: 1 1 0; align-self: stretch"></div>
        </div>
        <div data-layer="Button" class="Button" style="border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="_Button base" class="ButtonBase" style="padding-left: 14px; padding-right: 14px; padding-top: 8px; padding-bottom: 8px; background: white; overflow: hidden; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 8px; display: flex">
            <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
              <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10.9886 18.6784C15.591 18.6784 19.3219 14.9474 19.3219 10.3451C19.3219 5.74268 15.591 2.01172 10.9886 2.01172C6.38623 2.01172 2.65527 5.74268 2.65527 10.3451C2.65527 14.9474 6.38623 18.6784 10.9886 18.6784Z" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7.66309 12.4369C7.81309 12.6869 7.99641 12.9203 8.20475 13.1286C9.73808 14.6619 12.2297 14.6619 13.7714 13.1286C14.3964 12.5036 14.7547 11.7119 14.8714 10.9036" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7.10449 9.7869C7.22116 8.97024 7.5795 8.18687 8.2045 7.56187C9.73783 6.02853 12.2295 6.02853 13.7712 7.56187C13.9878 7.77853 14.1628 8.01188 14.3128 8.25355" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7.50488 14.6618V12.4368H9.72987" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14.4711 6.02832V8.25331H12.2461" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Refresh charts</div>
          </div>
        </div>
      </div>
      <div data-layer="table" class="Table" style="align-self: stretch; position: relative; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div data-layer="columns" class="Columns" style="flex: 1 1 0; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="column" class="Column" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member ID</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">55820474</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">302274401</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">7729471914</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 200px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Name</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">John Dey</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Alma Anders</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Joanne Smith</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 155px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DOB</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">01/05/1972</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12/15/1953</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">06/30/1951</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 100px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="width: 27px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">LOB</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 93px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Measure</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 140px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review 1</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 140px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review 2</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 170px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Assigned</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 118px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: #384455; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Status</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="sumbit-button" data-property-1="Default" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: var(--primary-blue, #3870B8); overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                  <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                </div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="sumbit-button" data-property-1="Inactive" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #BFD0EE; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                  <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                </div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="sumbit-button" data-property-1="Inactive" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #BFD0EE; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                  <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div data-layer="12px" class="Px" style="left: -9.05px; top: 63.29px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">12px</div>
      </div>
    </div>
    <div data-layer="Rectangle 266" class="Rectangle266" style="width: 24px; height: 224px; left: 20px; top: 100px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(18, 175, 240, 0.10)"></div>
    <div data-layer="Rectangle 268" class="Rectangle268" style="width: 12px; height: 68px; left: 32px; top: 144px; position: absolute; transform: rotate(-180deg); transform-origin: top left; background: rgba(18, 175, 240, 0.10)"></div>
    <div data-layer="Rectangle 269" class="Rectangle269" style="width: 12px; height: 68px; left: 244px; top: 144px; position: absolute; transform: rotate(-180deg); transform-origin: top left; background: rgba(18, 175, 240, 0.10)"></div>
    <div data-layer="Rectangle 267" class="Rectangle267" style="width: 24px; height: 224px; left: 20px; top: 144px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(18, 175, 240, 0.10)"></div>
  </div>
  <div data-layer="20px" class="Px" style="left: 164.53px; top: 999.71px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Inter; font-weight: 400; line-height: 20px; word-wrap: break-word">20px</div>
  <div data-layer="22px" class="Px" style="left: 171.99px; top: 845.35px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Inter; font-weight: 400; line-height: 20px; word-wrap: break-word">22px</div>
  <div data-layer="22px" class="Px" style="left: 171.99px; top: 792.35px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Inter; font-weight: 400; line-height: 20px; word-wrap: break-word">22px</div>
  <div data-layer="22px" class="Px" style="left: 171.99px; top: 1138.35px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Inter; font-weight: 400; line-height: 20px; word-wrap: break-word">22px</div>
  <div data-layer="20px" class="Px" style="left: 203.99px; top: 1162.85px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Inter; font-weight: 400; line-height: 20px; word-wrap: break-word">20px</div>
  <div data-layer="Assigned table" class="AssignedTable" style="left: 70px; top: 77px; position: absolute; color: black; font-size: 36px; font-family: Urbane; font-weight: 600; line-height: 54px; word-wrap: break-word">Assigned table</div>
  <div data-layer="Rectangle 275" class="Rectangle275" style="width: 1512px; height: 414.13px; left: 148px; top: 233.82px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Usage: The assigned table is used to display charts that the reviewer has not completed review 1 on. It is stacked on the top of the dashboard page, above the main table." class="UsageTheAssignedTableIsUsedToDisplayChartsThatTheReviewerHasNotCompletedReview1OnItIsStackedOnTheTopOfTheDashboardPageAboveTheMainTable" style="width: 1509.10px; left: 72.90px; top: 142.03px; position: absolute"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Usage: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; word-wrap: break-word">The assigned table is used to display charts that the reviewer has not completed review 1 on. It is stacked on the top of the dashboard page, above the main table. </span></div>
  <div data-layer="Default: The assigned table’s default state includes assigned charts, in the same format as the main table, with the review button in an active state for the 10 page chart, and an inactive state for the other charts." class="DefaultTheAssignedTableSDefaultStateIncludesAssignedChartsInTheSameFormatAsTheMainTableWithTheReviewButtonInAnActiveStateForThe10PageChartAndAnInactiveStateForTheOtherCharts" style="width: 1498.03px; left: 156.28px; top: 1202.85px; position: absolute"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Default:</span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; word-wrap: break-word"> The assigned table’s default state includes assigned charts, in the same format as the main table, with the review button in an active state for the 10 page chart, and an inactive state for the other charts.</span></div>
  <div data-layer="Spacing: Column spacing is flexible, but the table should fill the page and no text should be cut off." class="SpacingColumnSpacingIsFlexibleButTheTableShouldFillThePageAndNoTextShouldBeCutOff" style="left: 156.28px; top: 1261.47px; position: absolute"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Spacing:</span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; word-wrap: break-word"> Column spacing is flexible, but the table should fill the page and no text should be cut off.</span></div>
  <div data-layer="Rectangle 248" class="Rectangle248" style="width: 19.80px; height: 371.51px; left: 1563.98px; top: 791.35px; position: absolute; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 252" class="Rectangle252" style="width: 20px; height: 368px; left: 203.98px; top: 791.35px; position: absolute; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 249" class="Rectangle249" style="width: 22px; height: 1380.13px; left: 203.64px; top: 813.35px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 277" class="Rectangle277" style="width: 22px; height: 1380.13px; left: 203.64px; top: 1159.35px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 251" class="Rectangle251" style="width: 21.63px; height: 1340.20px; left: 223.78px; top: 866.98px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 268" class="Rectangle268" style="width: 1508.60px; height: 415.44px; left: 112.72px; top: 1589.04px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="assigned-table" class="AssignedTable" style="width: 1380px; left: 205.53px; top: 1612.76px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: inline-flex">
    <div data-layer="Table" class="Table" style="align-self: stretch; padding: 20px; background: var(--white, white); border-radius: 8px; outline: 1px var(--light-borders, #F1F5F7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
      <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 20px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="flex: 1 1 0; justify-content: flex-start; align-items: center; gap: 10px; display: flex">
          <div data-layer="label" class="Label" style="color: var(--text-black, #17181A); font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Assigned charts</div>
          <div data-layer="right corner" class="RightCorner" style="flex: 1 1 0; align-self: stretch"></div>
        </div>
        <div data-layer="Button" class="Button" style="border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="_Button base" class="ButtonBase" style="padding-left: 14px; padding-right: 14px; padding-top: 8px; padding-bottom: 8px; background: white; overflow: hidden; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 8px; display: flex">
            <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
              <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10.5286 19.0915C15.131 19.0915 18.862 15.3605 18.862 10.7581C18.862 6.15577 15.131 2.4248 10.5286 2.4248C5.92627 2.4248 2.19531 6.15577 2.19531 10.7581C2.19531 15.3605 5.92627 19.0915 10.5286 19.0915Z" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7.20312 12.8498C7.35313 13.0998 7.53645 13.3331 7.74479 13.5414C9.27812 15.0748 11.7698 15.0748 13.3114 13.5414C13.9364 12.9164 14.2948 12.1247 14.4115 11.3164" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M6.64453 10.2002C6.7612 9.38357 7.11954 8.6002 7.74454 7.9752C9.27787 6.44186 11.7695 6.44186 13.3112 7.9752C13.5279 8.19186 13.7029 8.42521 13.8529 8.66688" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7.04492 15.0746V12.8496H9.26991" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14.0111 6.44141V8.66639H11.7861" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Refresh charts</div>
          </div>
        </div>
      </div>
      <div data-layer="table" class="Table" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div data-layer="columns" class="Columns" style="flex: 1 1 0; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="column" class="Column" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member ID</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">55820474</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">302274401</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">7729471914</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 200px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Name</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">John Dey</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Alma Anders</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Joanne Smith</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 155px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DOB</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">01/05/1972</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12/15/1953</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">06/30/1951</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 100px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="width: 27px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">LOB</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 93px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Measure</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 140px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review 1</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 140px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review 2</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 170px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Assigned</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 118px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--gray-1, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: #384455; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Status</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="sumbit-button" data-property-1="Default" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: var(--primary-blue, #3870B8); overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                  <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                </div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="sumbit-button" data-property-1="Inactive" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #BFD0EE; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                  <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                </div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="sumbit-button" data-property-1="Inactive" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #BFD0EE; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                  <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="header item" class="HeaderItem" style="width: 224px; padding-left: 12px; padding-right: 12px; left: 189.35px; top: 1383.21px; position: absolute; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex">
    <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
      <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member ID</div>
    </div>
    <div data-layer="Rectangle 268" class="Rectangle268" style="width: 12px; height: 68px; left: 12px; top: 68px; position: absolute; transform: rotate(-180deg); transform-origin: top left; background: rgba(18, 175, 240, 0.10)"></div>
    <div data-layer="Rectangle 269" class="Rectangle269" style="width: 12px; height: 68px; left: 224px; top: 68px; position: absolute; transform: rotate(-180deg); transform-origin: top left; background: rgba(18, 175, 240, 0.10)"></div>
    <div data-layer="Rectangle 266" class="Rectangle266" style="width: 24px; height: 224px; left: 0px; top: 24px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(18, 175, 240, 0.10)"></div>
    <div data-layer="Rectangle 267" class="Rectangle267" style="width: 24px; height: 224px; left: 0px; top: 68px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(18, 175, 240, 0.10)"></div>
  </div>
  <div data-layer="font: H1 line-height: 160%" class="FontH1LineHeight160" style="left: 197.02px; top: 1565.09px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">font: H1<br/>line-height: 160%</div>
  <div data-layer="font: H3 font-size: 12px line-height: 20px color: text-black" class="FontH3FontSize12pxLineHeight20pxColorTextBlack" style="width: 109.53px; left: 67.49px; top: 1700.76px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">font: H3<br/>font-size: 12px<br/>line-height: 20px<br/>color: text-black</div>
  <div data-layer="All table items are left aligned" class="AllTableItemsAreLeftAligned" style="width: 109.53px; left: 1629.97px; top: 1836.76px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">All table items are left aligned</div>
  <div data-layer="header-item stroke bottom: 1px, gray 1" class="HeaderItemStrokeBottom1pxGray1" style="width: 109.53px; left: 1629.97px; top: 1736.76px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">header-item stroke bottom: 1px, gray 1</div>
  <div data-layer="font: Body font-size: 12px line-height: 20px color: text-black" class="FontBodyFontSize12pxLineHeight20pxColorTextBlack" style="width: 109.53px; left: 67.49px; top: 1834.19px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">font: Body<br/>font-size: 12px<br/>line-height: 20px<br/>color: text-black</div>
  <div data-layer="Header/table item:" class="HeaderTableItem" style="left: 196.33px; top: 1343.21px; position: absolute; color: black; font-size: 16px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Header/table item:</div>
  <div data-layer="24px" class="Px" style="left: 191.28px; top: 911.35px; position: absolute; justify-content: center; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">24px</div>
  <div data-layer="Line 3" class="Line3" style="width: 22.36px; height: 0px; left: 246.67px; top: 1606.14px; position: absolute; transform: rotate(90deg); transform-origin: top left; outline: 1px black solid; outline-offset: -0.50px"></div>
  <div data-layer="Line 4" class="Line4" style="width: 43.18px; height: 0px; left: 182.34px; top: 1724.76px; position: absolute; outline: 1px black solid; outline-offset: -0.50px"></div>
  <div data-layer="Line 6" class="Line6" style="width: 43.18px; height: 0px; left: 1578.13px; top: 1855.76px; position: absolute; outline: 1px black solid; outline-offset: -0.50px"></div>
  <div data-layer="Line 7" class="Line7" style="width: 43.18px; height: 0px; left: 1578.13px; top: 1755.76px; position: absolute; outline: 1px black solid; outline-offset: -0.50px"></div>
  <div data-layer="Line 5" class="Line5" style="width: 43.18px; height: 0px; left: 182.34px; top: 1858.19px; position: absolute; outline: 1px black solid; outline-offset: -0.50px"></div>
  <div data-layer="Spacing (primary):" class="SpacingPrimary" style="left: 71.90px; top: 703.20px; position: absolute; color: black; font-size: 24px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Spacing (primary):</div>
  <div data-layer="Styling:" class="Styling" style="left: 70px; top: 1533.44px; position: absolute; color: black; font-size: 24px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Styling:</div>
  <div data-layer="corner-radius: 8" class="CornerRadius8" style="left: 1629.97px; top: 1617.76px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">corner-radius: 8</div>
  <div data-layer="assigned-table" class="AssignedTable" style="width: 1380px; left: 193.71px; top: 256.89px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: inline-flex">
    <div data-layer="Table" class="Table" style="align-self: stretch; padding: 20px; background: var(--white, white); border-radius: 8px; outline: 1px var(--light-borders, #F1F5F7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
      <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 20px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="flex: 1 1 0; justify-content: flex-start; align-items: center; gap: 10px; display: flex">
          <div data-layer="label" class="Label" style="color: var(--text-black, #17181A); font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Assigned charts</div>
          <div data-layer="right corner" class="RightCorner" style="flex: 1 1 0; align-self: stretch"></div>
        </div>
        <div data-layer="Button" class="Button" style="border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="_Button base" class="ButtonBase" style="padding-left: 14px; padding-right: 14px; padding-top: 8px; padding-bottom: 8px; background: white; overflow: hidden; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 8px; display: flex">
            <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
              <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10.7113 19.2238C15.3136 19.2238 19.0446 15.4928 19.0446 10.8905C19.0446 6.28809 15.3136 2.55713 10.7113 2.55713C6.10889 2.55713 2.37793 6.28809 2.37793 10.8905C2.37793 15.4928 6.10889 19.2238 10.7113 19.2238Z" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7.38574 12.9823C7.53574 13.2323 7.71907 13.4657 7.9274 13.674C9.46074 15.2073 11.9524 15.2073 13.4941 13.674C14.1191 13.049 14.4774 12.2573 14.5941 11.449" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M6.82715 10.3323C6.94382 9.51565 7.30215 8.73228 7.92715 8.10728C9.46049 6.57394 11.9521 6.57394 13.4938 8.10728C13.7105 8.32394 13.8855 8.55729 14.0355 8.79896" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7.22754 15.2072V12.9822H9.45253" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14.1937 6.57373V8.79872H11.9688" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Refresh charts</div>
          </div>
        </div>
      </div>
      <div data-layer="table" class="Table" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div data-layer="columns" class="Columns" style="flex: 1 1 0; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="column" class="Column" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member ID</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">55820474</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">302274401</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">7729471914</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 200px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Name</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">John Dey</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Alma Anders</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Joanne Smith</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 155px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DOB</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">01/05/1972</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12/15/1953</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">06/30/1951</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 100px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="width: 27px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">LOB</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MA HMO</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 93px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Measure</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="icon text" class="IconText" style="background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <div data-layer="Label" class="Label" style="width: 25px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">CBP</div>
              </div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 140px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review 1</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 68px; padding-left: 12px; padding-right: 12px; padding-top: 13px; padding-bottom: 13px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 140px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review 2</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">-</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 170px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Assigned</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="width: 110px; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">04/15/25 1:30pm</div>
            </div>
          </div>
          <div data-layer="column" class="Column" style="width: 118px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="height: 68px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: #384455; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Status</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="sumbit-button" data-property-1="Default" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: var(--primary-blue, #3870B8); overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                  <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                </div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="sumbit-button" data-property-1="Inactive" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #BFD0EE; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                  <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                </div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 68px; padding-left: 12px; padding-right: 12px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="sumbit-button" data-property-1="Inactive" class="SumbitButton" style="flex: 1 1 0; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: flex">
                <div data-layer="_Button base" class="ButtonBase" style="flex: 1 1 0; padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #BFD0EE; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
                  <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Review</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="12px" class="Px" style="left: 185.88px; top: 1458.13px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">12px</div>
  <div data-layer="24px" class="Px" style="left: 156.28px; top: 1383.21px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">24px</div>
</div>