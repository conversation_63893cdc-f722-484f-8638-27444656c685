@use 'variables' as variables;
@use 'mixins' as mix;

.chart-list-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.chart-list-header {
  display: flex;
  border-bottom: 1px solid variables.$gray-1;
}

.chart-list-header-cell {
  @include mix.table-header;
}

.chart-list-body {
  width: 100%;
}

.chart-list-row {
  display: flex;
  border-bottom: 1px solid var(--light-borders, #F1F5F7);
  
  &:last-child {
    border-bottom: none;
  }
}

.status-badge {
  &.clickable {
    cursor: pointer;
    // Optionally, add other hover effects like a slight background change or underline
    // &:hover {
    //   text-decoration: underline;
    //   background-color: darken(#eef0f2, 5%); // Example, adjust color as needed
    // }
  }
}

.chart-list-cell {
  @include mix.table-cell;
}

.status-badge {
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  
  &.status-review {
    background-color: #3870B8;
    color: white;
  }
  
  &.status-complete {
    background-color: rgba(26, 213, 152, 0.10);
    color: #1AD598;
    border: 1px solid rgba(26, 213, 152, 0.40);
    padding: 4px 8px;
    border-radius: 90px;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &::before {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath d='M4 8.17L6.48 10.65L12 5.13' stroke='%231AD598' stroke-width='1.5'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
    }
  }
}

.no-charts-message {
  padding: 20px;
  text-align: center;
  font-size: 14px;
  color: var(--text-black, #17181A);
}