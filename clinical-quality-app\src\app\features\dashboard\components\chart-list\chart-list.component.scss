@use 'variables' as variables;
@use 'mixins' as mix;

.chart-list-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.chart-list-header {
  display: flex;
  border-bottom: 1px solid variables.$gray-1;
}

.chart-list-header-cell {
  @include mix.table-header;
}

.chart-list-body {
  width: 100%;
}

.chart-list-row {
  display: flex;
  border-bottom: 1px solid var(--light-borders, #F1F5F7);
  
  &:last-child {
    border-bottom: none;
  }
}

// DEPRECATED: Use ButtonComponent instead of status-badge styling

.chart-list-cell {
  @include mix.table-cell;
}

.no-charts-message {
  padding: 20px;
  text-align: center;
  font-size: 14px;
  color: var(--text-black, #17181A);
}