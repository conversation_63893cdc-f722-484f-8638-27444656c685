<div data-layer="Buttons, selectors, and icons" class="ButtonsSelectorsAndIcons" style="width: 945px; height: 970px; position: relative; background: white; overflow: hidden">
  <div data-svg-wrapper data-layer="icon_down" class="IconDown" style="left: 171.85px; top: 918.76px; position: absolute">
    <svg width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2.07617 1.69409L7.85254 7.45409L13.6289 1.69409" stroke="#17181A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="icon_right" class="IconRight" style="left: 205.27px; top: 915.76px; position: absolute">
    <svg width="9" height="15" viewBox="0 0 9 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.21289 13.532L6.97289 7.75562L1.21289 1.97926" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  </div>
  <div data-layer="Frame 926" class="Frame926" style="left: 36px; top: 747px; position: absolute; justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
    <div data-svg-wrapper data-layer="icon_arrow_left" class="IconArrowLeft" style="position: relative">
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15.8327 10H4.16602M4.16602 10L9.16602 15M4.16602 10L9.16602 5" stroke="var(--link, #0071BC)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    <div data-layer="Text" class="Text" style="color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Back</div>
  </div>
  <div data-layer="base" class="Base" style="padding: 8px; left: 36px; top: 904.76px; position: absolute; background: rgba(56, 112, 184, 0.20); overflow: hidden; border-radius: 120px; justify-content: center; align-items: center; gap: 8px; display: inline-flex">
    <div data-svg-wrapper data-layer="icon_user" data-property-1="Profile_Circle" class="IconUser" style="position: relative">
      <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.1009 11.4056C10.0426 11.3973 9.96758 11.3973 9.90091 11.4056C8.43424 11.3556 7.26758 10.1556 7.26758 8.6806C7.26758 7.17227 8.48424 5.94727 10.0009 5.94727C11.5092 5.94727 12.7342 7.17227 12.7342 8.6806C12.7259 10.1556 11.5676 11.3556 10.1009 11.4056Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M15.6161 16.9057C14.1328 18.264 12.1661 19.089 9.99948 19.089C7.83281 19.089 5.86615 18.264 4.38281 16.9057C4.46615 16.1223 4.96615 15.3557 5.85781 14.7557C8.14115 13.239 11.8745 13.239 14.1411 14.7557C15.0328 15.3557 15.5328 16.1223 15.6161 16.9057Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M9.99935 19.089C14.6017 19.089 18.3327 15.3581 18.3327 10.7557C18.3327 6.15332 14.6017 2.42236 9.99935 2.42236C5.39698 2.42236 1.66602 6.15332 1.66602 10.7557C1.66602 15.3581 5.39698 19.089 9.99935 19.089Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
  </div>
  <div data-layer="check" class="Check" style="width: 56px; height: 90px; left: 36px; top: 787px; position: absolute; overflow: hidden; border-radius: 5px; border: 1px #9747FF solid">
    <div data-svg-wrapper data-layer="Property 1=default" class="Property1Default" style="left: 20px; top: 20px; position: absolute">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="0.5" y="0.5" width="15" height="15" rx="4.5" fill="white" stroke="#D9E1E7"/>
      </svg>
    </div>
    <div data-svg-wrapper data-layer="Property 1=checked" class="Property1Checked" style="left: 20px; top: 54px; position: absolute">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="16" height="16" rx="5" fill="#17181A"/>
      <path d="M4 8.16667L7.33333 11.5L12.3333 4" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
  </div>
  <div data-svg-wrapper data-layer="icon_arrow_diag" class="IconArrowDiag" style="left: 85.01px; top: 910.76px; position: absolute">
    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.01367 16.7556L16.0137 8.75562M16.0137 8.75562H10.0137M16.0137 8.75562V14.7556" stroke="#0071BC" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="icon_arrow_down" class="IconArrowDown" style="left: 128.43px; top: 910.76px; position: absolute">
    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16.4336 10.7556L12.4336 14.7556L8.43359 10.7556" stroke="var(--light-primary, #809FB8)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  </div>
  <div data-layer="Buttons, selectors, and icons" class="ButtonsSelectorsAndIcons" style="left: 47px; top: 62.80px; position: absolute; color: black; font-size: 36px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Buttons, selectors, and icons</div>
  <div data-layer="sumbit-button" class="SumbitButton" style="width: 120px; height: 298px; left: 36px; top: 130px; position: absolute; overflow: hidden; border-radius: 5px; border: 1px #9747FF solid">
    <div data-layer="Property 1=Inactive" class="Property1Inactive" style="left: 20px; top: 20px; position: absolute; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="_Button base" class="ButtonBase" style="padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #BFD0EE; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
        <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Submit</div>
      </div>
    </div>
    <div data-layer="Property 1=Default" class="Property1Default" style="left: 20px; top: 93px; position: absolute; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="_Button base" class="ButtonBase" style="padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: var(--primary-blue, #3870B8); overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
        <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Submit</div>
      </div>
    </div>
    <div data-layer="Property 1=Hover" class="Property1Hover" style="left: 20px; top: 166px; position: absolute; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="_Button base" class="ButtonBase" style="padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #468CE7; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
        <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Submit</div>
      </div>
    </div>
    <div data-layer="Property 1=Clicked" class="Property1Clicked" style="left: 20px; top: 241px; position: absolute; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="_Button base" class="ButtonBase" style="padding-left: 16px; padding-right: 16px; padding-top: 10px; padding-bottom: 10px; background: #285082; overflow: hidden; border-radius: 8px; justify-content: center; align-items: center; gap: 8px; display: flex">
        <div data-layer="Text" class="Text" style="color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Submit</div>
      </div>
    </div>
  </div>
  <div data-layer="transition: .3s" class="Transition3s" style="left: 240.89px; top: 264px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">transition: .3s</div>
  <div data-layer="transition: .3s" class="Transition3s" style="left: 300.35px; top: 522.79px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">transition: .3s</div>
  <div data-layer="transition: .3s" class="Transition3s" style="left: 240.89px; top: 341.89px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">transition: .3s</div>
  <div data-layer="transition: .3s" class="Transition3s" style="left: 300.35px; top: 622.22px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">transition: .3s</div>
  <div data-layer="Button" class="Button" style="left: 56.12px; top: 473.16px; position: absolute; box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05); border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="_Button base" class="ButtonBase" style="padding-left: 14px; padding-right: 14px; padding-top: 8px; padding-bottom: 8px; background: white; overflow: hidden; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 8px; display: flex">
      <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10.1165 18.4928C14.7189 18.4928 18.4499 14.7619 18.4499 10.1595C18.4499 5.55713 14.7189 1.82617 10.1165 1.82617C5.51416 1.82617 1.7832 5.55713 1.7832 10.1595C1.7832 14.7619 5.51416 18.4928 10.1165 18.4928Z" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6.79297 12.2514C6.94297 12.5014 7.1263 12.7347 7.33463 12.9431C8.86796 14.4764 11.3596 14.4764 12.9013 12.9431C13.5263 12.3181 13.8846 11.5264 14.0013 10.718" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6.23438 9.60135C6.35104 8.78469 6.70938 8.00132 7.33438 7.37632C8.86771 5.84299 11.3594 5.84299 12.901 7.37632C13.1177 7.59298 13.2927 7.82633 13.4427 8.068" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6.63477 14.4762V12.2512H8.85975" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M13.6019 5.84277V8.06776H11.377" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Refresh charts</div>
    </div>
  </div>
  <div data-layer="Button" class="Button" style="width: 187px; height: 248px; left: 36px; top: 453px; position: absolute; overflow: hidden; border-radius: 5px; border: 1px #9747FF solid">
    <div data-layer="Property 1=Default" class="Property1Default" style="left: 20px; top: 20px; position: absolute; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="_Button base" class="ButtonBase" style="padding-left: 14px; padding-right: 14px; padding-top: 8px; padding-bottom: 8px; background: white; overflow: hidden; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 8px; display: flex">
        <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.99935 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.99984C18.3327 5.39746 14.6017 1.6665 9.99935 1.6665C5.39698 1.6665 1.66602 5.39746 1.66602 9.99984C1.66602 14.6022 5.39698 18.3332 9.99935 18.3332Z" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6.67578 12.0917C6.82578 12.3417 7.00911 12.5751 7.21744 12.7834C8.75078 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6.11719 9.44169C6.23385 8.62502 6.59219 7.84165 7.21719 7.21665C8.75053 5.68332 11.2422 5.68332 12.7839 7.21665C13.0005 7.43332 13.1755 7.66666 13.3255 7.90833" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6.51758 14.3165V12.0916H8.74256" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M13.4848 5.68311V7.90809H11.2598" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Refresh charts</div>
      </div>
    </div>
    <div data-layer="Property 1=Hover" class="Property1Hover" style="left: 20px; top: 106px; position: absolute; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="_Button base" class="ButtonBase" style="padding-left: 14px; padding-right: 14px; padding-top: 8px; padding-bottom: 8px; background: var(--gray-1, #F1F5F7); overflow: hidden; border-radius: 8px; outline: 1px var(--gray-1, #F1F5F7) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 8px; display: flex">
        <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.99935 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.99984C18.3327 5.39746 14.6017 1.6665 9.99935 1.6665C5.39698 1.6665 1.66602 5.39746 1.66602 9.99984C1.66602 14.6022 5.39698 18.3332 9.99935 18.3332Z" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6.67578 12.0917C6.82578 12.3417 7.00911 12.5751 7.21744 12.7834C8.75078 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6.11719 9.44169C6.23385 8.62502 6.59219 7.84165 7.21719 7.21665C8.75053 5.68332 11.2422 5.68332 12.7839 7.21665C13.0005 7.43332 13.1755 7.66666 13.3255 7.90833" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6.51758 14.3165V12.0916H8.74256" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M13.4848 5.68311V7.90809H11.2598" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Refresh charts</div>
      </div>
    </div>
    <div data-layer="Property 1=Clicked" class="Property1Clicked" style="left: 20px; top: 191px; position: absolute; border-radius: 8px; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="_Button base" class="ButtonBase" style="padding-left: 14px; padding-right: 14px; padding-top: 8px; padding-bottom: 8px; background: var(--text-black, #17181A); overflow: hidden; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 8px; display: flex">
        <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.99935 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.99984C18.3327 5.39746 14.6017 1.6665 9.99935 1.6665C5.39698 1.6665 1.66602 5.39746 1.66602 9.99984C1.66602 14.6022 5.39698 18.3332 9.99935 18.3332Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6.67578 12.0917C6.82578 12.3417 7.00911 12.5751 7.21744 12.7834C8.75078 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6.11719 9.44169C6.23385 8.62502 6.59219 7.84165 7.21719 7.21665C8.75053 5.68332 11.2422 5.68332 12.7839 7.21665C13.0005 7.43332 13.1755 7.66666 13.3255 7.90833" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6.51758 14.3165V12.0916H8.74256" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M13.4848 5.68311V7.90809H11.2598" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: white; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Refresh charts</div>
      </div>
    </div>
  </div>
  <div data-layer="Inactive color: #BFD0EE corner-radius: 8px **cannot be clicked" class="InactiveColorBfd0eeCornerRadius8pxCannotBeClicked" style="width: 155.54px; height: 48.82px; left: 170.94px; top: 150px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Inactive<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">color: #BFD0EE<br/>corner-radius: 8px<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">**cannot be clicked</span></div>
  <div data-layer="Default: color: primary-blue corner-radius: 8px" class="DefaultColorPrimaryBlueCornerRadius8px" style="width: 117.86px; height: 37px; left: 170.94px; top: 223px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Default:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">color: primary-blue<br/>corner-radius: 8px</span></div>
  <div data-layer="Default: color: #ffffff border: 1px, gray-2 corner-radius: 8px" class="DefaultColorFfffffBorder1pxGray2CornerRadius8px" style="width: 138.04px; height: 51.19px; left: 237.12px; top: 466.12px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Default:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">color: #ffffff<br/>border: 1px, gray-2<br/>corner-radius: 8px</span></div>
  <div data-layer="Text: button-text color: text-black" class="TextButtonTextColorTextBlack" style="width: 105.59px; height: 36.86px; left: 384.01px; top: 466.12px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Text:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">button-text<br/>color: text-black</span></div>
  <div data-layer="Text: button-text color: #ffffff" class="TextButtonTextColorFfffff" style="width: 105.59px; height: 36.86px; left: 353.77px; top: 150px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Text:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">button-text<br/>color: #ffffff</span></div>
  <div data-layer="Text: button-text color: #ffffff" class="TextButtonTextColorFfffff" style="width: 105.59px; height: 36.86px; left: 353.77px; top: 223px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Text:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">button-text<br/>color: #ffffff</span></div>
  <div data-layer="Text: button-text color: #ffffff" class="TextButtonTextColorFfffff" style="width: 105.59px; height: 36.86px; left: 353.77px; top: 297.83px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Text:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">button-text<br/>color: #ffffff</span></div>
  <div data-layer="Text: button-text color: #ffffff" class="TextButtonTextColorFfffff" style="width: 105.59px; height: 36.86px; left: 353.77px; top: 371px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Text:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">button-text<br/>color: #ffffff</span></div>
  <div data-layer="Text: button-text color: text-black" class="TextButtonTextColorTextBlack" style="width: 105.59px; height: 36.86px; left: 384.01px; top: 561.08px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Text:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">button-text<br/>color: text-black</span></div>
  <div data-layer="Text: button-text color: #ffffff" class="TextButtonTextColorFfffff" style="width: 105.59px; height: 36.86px; left: 384.01px; top: 650.93px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Text:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">button-text<br/>color: #ffffff</span></div>
  <div data-layer="Click: color: text-black border: 1px, gray-2 corner-radius: 8px" class="ClickColorTextBlackBorder1pxGray2CornerRadius8px" style="width: 138.04px; height: 42px; left: 237.12px; top: 650.93px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Click:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">color: text-black<br/>border: 1px, gray-2<br/>corner-radius: 8px</span></div>
  <div data-layer="Hover: color: gray-1 border: 1px, gray-2 corner-radius: 8px" class="HoverColorGray1Border1pxGray2CornerRadius8px" style="width: 138.04px; height: 42px; left: 237.12px; top: 561.08px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Hover:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">color: gray-1<br/>border: 1px, gray-2<br/>corner-radius: 8px</span></div>
  <div data-layer="Inactive" class="Inactive" style="left: 99.11px; top: 803px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Inactive</div>
  <div data-layer="Clicked" class="Clicked" style="left: 99.11px; top: 836.14px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Clicked</div>
  <div data-layer="Hover: color: #468CE7 corner-radius: 8px" class="HoverColor468ce7CornerRadius8px" style="width: 120.19px; height: 33.56px; left: 170.94px; top: 297.83px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Hover:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">color: #468CE7<br/>corner-radius: 8px</span></div>
  <div data-layer="Click: color: #285082 corner-radius: 8px" class="ClickColor285082CornerRadius8px" style="width: 113px; height: 35px; left: 170.94px; top: 371px; position: absolute"><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 700; word-wrap: break-word">Click:<br/></span><span style="color: black; font-size: 12px; font-family: Urbane; font-weight: 500; word-wrap: break-word">color: #285082<br/>corner-radius: 8px</span></div>
  <div data-svg-wrapper data-layer="Arrow 1" class="Arrow1" style="left: 222.92px; top: 263.09px; position: absolute">
    <svg width="16" height="31" viewBox="0 0 16 31" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.21477 29.7769C7.60529 30.1675 8.23846 30.1675 8.62898 29.7769L14.9929 23.413C15.3835 23.0224 15.3835 22.3893 14.9929 21.9988C14.6024 21.6082 13.9693 21.6082 13.5787 21.9988L7.92187 27.6556L2.26502 21.9988C1.8745 21.6082 1.24133 21.6082 0.850806 21.9988C0.460282 22.3893 0.460282 23.0224 0.850806 23.413L7.21477 29.7769ZM7.92188 0.0881348L6.92188 0.0881347L6.92187 29.0698L7.92187 29.0698L8.92187 29.0698L8.92188 0.0881348L7.92188 0.0881348Z" fill="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Arrow 3" class="Arrow3" style="left: 282.39px; top: 521.88px; position: absolute">
    <svg width="15" height="31" viewBox="0 0 15 31" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.67961 30.5687C7.07013 30.9592 7.7033 30.9592 8.09382 30.5687L14.4578 24.2047C14.8483 23.8142 14.8483 23.181 14.4578 22.7905C14.0673 22.4 13.4341 22.4 13.0436 22.7905L7.38672 28.4474L1.72986 22.7905C1.33934 22.4 0.706174 22.4 0.31565 22.7905C-0.0748744 23.181 -0.0748744 23.8142 0.31565 24.2047L6.67961 30.5687ZM7.38672 0.879883L6.38672 0.879883L6.38672 29.8616L7.38672 29.8616L8.38672 29.8616L8.38672 0.879883L7.38672 0.879883Z" fill="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Arrow 2" class="Arrow2" style="left: 222.92px; top: 334.69px; position: absolute">
    <svg width="16" height="31" viewBox="0 0 16 31" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.21477 30.3792C7.60529 30.7698 8.23846 30.7698 8.62898 30.3792L14.9929 24.0153C15.3835 23.6247 15.3835 22.9916 14.9929 22.6011C14.6024 22.2105 13.9693 22.2105 13.5787 22.6011L7.92187 28.2579L2.26502 22.6011C1.8745 22.2105 1.24133 22.2105 0.850806 22.6011C0.460282 22.9916 0.460282 23.6247 0.850806 24.0153L7.21477 30.3792ZM7.92188 0.69043L6.92188 0.69043L6.92187 29.6721L7.92187 29.6721L8.92187 29.6721L8.92188 0.69043L7.92188 0.69043Z" fill="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Arrow 4" class="Arrow4" style="left: 282.39px; top: 615.02px; position: absolute">
    <svg width="15" height="30" viewBox="0 0 15 30" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.67961 29.7071C7.07013 30.0976 7.7033 30.0976 8.09382 29.7071L14.4578 23.3431C14.8483 22.9526 14.8483 22.3195 14.4578 21.9289C14.0673 21.5384 13.4341 21.5384 13.0436 21.9289L7.38672 27.5858L1.72986 21.9289C1.33934 21.5384 0.706174 21.5384 0.31565 21.9289C-0.0748744 22.3195 -0.0748744 22.9526 0.31565 23.3431L6.67961 29.7071ZM7.38672 0.0183105L6.38672 0.0183105L6.38672 29L7.38672 29L8.38672 29L8.38672 0.0183106L7.38672 0.0183105Z" fill="black"/>
    </svg>
  </div>
</div>