@use 'variables' as variables;
@use 'mixins' as mix;

.menu-container {
  width: 100%;
  height: 80px;
  background: variables.$white;
  border-bottom: 1px solid variables.$gray-1;
  position: sticky;
  top: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
}

.menu-content {
  width: 100%;
  height: 100%;
  padding: 12px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.logo-container {
  width: 240px;
  padding: 10px 20px;
  background: variables.$white;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 0.8;
  }
}

.logo-image {
  width: 150px;
  height: 37.4px;
  object-fit: contain;
}

.logo-placeholder {
  font-size: 18px;
  font-family: 'Urbane', sans-serif;
  font-weight: 600;
  color: variables.$text-black;
}

.user-section {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}

.user-info {
  padding: 12px 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}

.user-name {
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  line-height: 20px;
  color: variables.$text-black;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.user-avatar {
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: variables.$gray-1;
  border: 1px solid variables.$gray-2;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    border-color: variables.$gray-3;
    background: variables.$white;
  }
}

.user-icon {
  width: 20px;
  height: 20px;
  color: variables.$gray-3;
  transition: color 0.2s ease;

  .avatar-circle:hover & {
    color: variables.$text-black;
  }
}

.dropdown-arrow {
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 4px;

  &.open {
    transform: rotate(180deg);
  }

  &:hover {
    background-color: variables.$gray-1;
    border-radius: 4px;
  }
}

.arrow-icon {
  width: 16px;
  height: 16px;
  color: variables.$gray-3;
  transition: color 0.2s ease;

  .dropdown-arrow:hover & {
    color: variables.$text-black;
  }
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: variables.$white;
  border-radius: 10px;
  border: 1px solid variables.$gray-2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  margin-top: 8px;
  min-width: 200px;
  overflow: hidden;
}

.dropdown-content {
  padding: 8px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: variables.$text-black;

  &:hover {
    background-color: variables.$gray-1;
  }

  &:active {
    background-color: variables.$gray-2;
  }
}

.item-icon {
  font-size: 14px;
  width: 16px;
  text-align: center;
}

.item-label {
  flex: 1;
}

// Responsive behavior
@include mix.for-tablet-portrait-up {
  .menu-content {
    padding: 12px 20px;
  }
  
  .logo-container {
    width: 200px;
    padding: 8px 16px;
  }
  
  .logo-image {
    width: 120px;
    height: 30px;
  }
}

@include mix.for-phone-only {
  .menu-content {
    padding: 8px 16px;
  }
  
  .logo-container {
    width: auto;
    padding: 4px 8px;
  }
  
  .logo-image {
    width: 100px;
    height: 25px;
  }
  
  .user-name {
    display: none; // Hide user name on mobile
  }
  
  .user-dropdown {
    right: -16px; // Adjust for mobile padding
    min-width: 180px;
  }
}
