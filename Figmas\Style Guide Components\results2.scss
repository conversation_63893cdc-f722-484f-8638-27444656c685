@import "src/app/tokens.scss";

.results-v2_929-2981 {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  background: #ffffff;
  width: 1082px;
  height: 2606px;
}
.rectangle-325_929-2982 {
  top: 1545px;
  left: 237px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-343_1102-539 {
  top: 1545px;
  left: 237px;
  position: absolute;
  display: flex;
  display: flex;
}

.notes_929-7609 {
  padding: 20px;
  top: 1559px;
  left: 256px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 517px;
}
.head_929-7610 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: 100%;
}
.frame-929_929-7611 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.stack_929-7612 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  width: 100%;
}
.text-label_929-7613 {
  color: #17181a;
  @include h-1-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.right-corner_929-7614 {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.text-8px_929-3175 {
  top: 28px;
  left: 95px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 23px;
  height: 4px;
}

.text-16px_929-3176 {
  top: 15px;
  left: 110px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 27px;
  height: 4px;
}

.text-8px_929-3177 {
  top: 28px;
  left: 126px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 23px;
  height: 4px;
}

.menu_929-7615 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 41px;
  width: 100%;
}
.second-menu_929-7616 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: content-box;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-bottom-width: 1px;
  background: #ffffff;
  width: 100%;
}
.bradcrumb_929-7617 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 40px;
}
.box_929-7618 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  height: 100%;
}
.menu_929-7619 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-7620 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-color: $variable-collection-primary-blue;
  border-style: solid;
  border-bottom-width: 1px;
  height: 100%;
}
.menu-item_929-7621 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-7622 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-7624 {
  color: $variable-collection-primary-blue;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item-_929-7626 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-7627 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-7628 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-7630 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item_929-7632 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-7633 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-7635 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.frame-916_929-7637 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.frame-942_929-7638 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
}
.check_929-7639 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-teleheath_929-7640 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.frame-936_929-7641 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-939_929-7642 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-940_929-7643 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.text-sys_929-7644 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-inclusion_929-7645 {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 127px;
  height: 48px;
}
.row_929-7646 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-value_929-7647 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.frame-941_929-7648 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.text-dias_929-7649 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-inclusion_929-7650 {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 48px;
  width: 100%;
}
.row_929-7651 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-value_929-7652 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.frame-937_929-7653 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 215px;
}
.text-date-of-service_929-7654 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.calendar_929-7655 {
  height: 48px;
  width: 100%;
}

.frame-938_929-7656 {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-notes_929-7657 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.notes_929-7658 {
  width: 100%;
}

.vector_1102-540 {
  top: 9px;
  left: 127px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_1102-542 {
  top: 9px;
  left: 258px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-4px_1102-541 {
  top: 13px;
  left: 117px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 23px;
  height: 4px;
}

.text-4px_1102-543 {
  top: 13px;
  left: 248px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 23px;
  height: 4px;
}

.rectangle-337_929-2983 {
  top: 2136px;
  left: 239px;
  position: absolute;
  display: flex;
  display: flex;
}

.notes_929-3537 {
  padding: 20px;
  top: 2157px;
  left: 257px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 517px;
}
.head_929-3538 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: 100%;
}
.frame-929_929-3539 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.stack_929-3540 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  width: 100%;
}
.text-label_929-3541 {
  color: #17181a;
  @include h-1-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.right-corner_929-3542 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.menu_929-3543 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 41px;
  width: 100%;
}
.second-menu_929-3544 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: content-box;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-bottom-width: 1px;
  background: #ffffff;
  width: 100%;
}
.bradcrumb_929-3545 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 40px;
}
.box_929-3546 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  height: 100%;
}
.menu_929-3547 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3548 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-color: $variable-collection-primary-blue;
  border-style: solid;
  border-bottom-width: 1px;
  height: 100%;
}
.menu-item_929-3549 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3550 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3552 {
  color: $variable-collection-primary-blue;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item-_929-3554 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3555 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3556 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3558 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item_929-3560 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3561 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3563 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.frame-916_929-3565 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.frame-942_929-3566 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
}
.check_929-3567 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-teleheath_929-3568 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.frame-936_929-3569 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-939_929-3570 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-940_929-3571 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.text-sys_929-3572 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-inclusion_929-3573 {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 127px;
  height: 48px;
}
.row_929-3574 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-value_929-3575 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.frame-941_929-3576 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.text-dias_929-3577 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-inclusion_929-3578 {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 48px;
  width: 100%;
}
.row_929-3579 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-value_929-3580 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.frame-937_929-3581 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 215px;
}
.text-date-of-service_929-3582 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.calendar_929-3583 {
  height: 48px;
  width: 100%;
}

.frame-938_929-3584 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-notes_929-3585 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.notes_929-3586 {
  width: 100%;
}

.rectangle-315_929-2984 {
  top: 326px;
  left: 235px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-results-container-v2_929-3178 {
  top: 63px;
  left: 47px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 36px;
  font-family: Urbane;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
  width: 398px;
  height: 43px;
}

.text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state--allows-copy-paste-_929-3179 {
  top: 212px;
  left: 47px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  text-wrap: wrap;
  width: 1040px;
  height: 6px;
}

.text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state--allows-copy-paste-_929-3180 {
  top: 212px;
  left: 47px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  text-wrap: wrap;
  width: 1040px;
  height: 6px;
}

.text-entry--has-a-dropdown--reasoning---date-entry--date-of-service---and-text-entry--notes-_929-3181 {
  top: 256px;
  left: 47px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  text-wrap: wrap;
  width: 1040px;
  height: 6px;
}

.text-usage--enter-findings-after-finding-them-in-the-chart-and-validating-them--there-are-three-versions--inclusions--exclusions--and-none-found-_929-3182 {
  top: 137px;
  left: 48px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  text-wrap: wrap;
  width: 975px;
  height: 44px;
}

.text-default--inactive--nothing-entered_929-3183 {
  top: 483px;
  left: 818px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 200px;
  height: 4px;
}

.text-text-entry--sys--dias--and-dos-auto-populate-when-a-measure-is-included-and-allow-for-copy-paste_929-3531 {
  top: 546px;
  left: 827px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 209px;
  height: 64px;
}

.text-inclusions--reasoning--date-of-service--and-notes-shown_929-3184 {
  top: 433px;
  left: 818px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 226px;
  height: 24px;
}

.text-categories-are-in-tabs_929-3185 {
  top: 433px;
  left: 68px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 140px;
  height: 4px;
}

.text-exclusions--reasoning--date-of-service--and-notes-shown_929-3186 {
  top: 808px;
  left: 823px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 226px;
  height: 24px;
}

.text-component--dropdown-exclusion_929-3533 {
  top: 895px;
  left: 823px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 226px;
  height: 4px;
}

.text-all-fields-have-labels-above-font--urbane-light-font-size--10px-font-color--gray3-line-spacing--20px_929-3597 {
  top: 854px;
  left: 37px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 168px;
  height: 84px;
}

.text-component--dropdown-none_929-3535 {
  top: 1314px;
  left: 823px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 226px;
  height: 4px;
}

.text-none-found--reasoning--notes-shown_929-3187 {
  top: 1238px;
  left: 824px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 226px;
  height: 24px;
}

.text-second-menu--stroke-bottom-size--1px-color--gray-1_929-3189 {
  top: 2233px;
  left: 809px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 226px;
  height: 44px;
}

.text-menu-item--active--font--h2-color--primary-blue-stroke--bottom--1px--primary-blue_929-3190 {
  top: 2213px;
  left: 89px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 123px;
  height: 84px;
}

.text-menu-item--inactive--font--h2-color--gray-3-stroke--none_929-3191 {
  top: 2052px;
  left: 388px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 131px;
  height: 64px;
}

.vector-40_929-3194 {
  top: 483px;
  left: 711px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-42_929-3195 {
  top: 433px;
  left: 711px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-45_929-3196 {
  top: 433px;
  left: 214px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-43_929-3197 {
  top: 808px;
  left: 782px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-53_929-3534 {
  top: 894px;
  left: 782px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-55_929-3598 {
  top: 853px;
  left: 218px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-54_929-3536 {
  top: 1313px;
  left: 782px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-52_929-3532 {
  top: 552px;
  left: 782px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-44_929-3198 {
  top: 1238px;
  left: 780px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-49_929-3200 {
  top: 2264px;
  left: 768px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-50_929-3201 {
  top: 2247px;
  left: 220px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-51_929-3202 {
  top: 2221px;
  left: 436px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-spacing-_929-3205 {
  top: 1505px;
  left: 68px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.text-styling-_929-3206 {
  top: 1996px;
  left: 68px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.vector_929-3207 {
  top: 1926px;
  left: 258px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_929-3208 {
  top: 1887px;
  left: 237px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_929-3210 {
  top: 1780px;
  left: 235px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_929-3211 {
  top: 1728px;
  left: 235px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_929-7671 {
  top: 1812px;
  left: 235px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-20px_929-3212 {
  top: 1934px;
  left: 254px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 32px;
  height: 4px;
}

.text-20px_929-3213 {
  top: 1897px;
  left: 203px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 32px;
  height: 4px;
}

.text-12px_929-3215 {
  top: 1786px;
  left: 205px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 27px;
  height: 4px;
}

.text-4px_929-3216 {
  top: 1730px;
  left: 205px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 23px;
  height: 4px;
}

.text-4px_929-7672 {
  top: 1814px;
  left: 205px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 23px;
  height: 4px;
}

.rectangle-293_929-3217 {
  top: 1580px;
  left: 256px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-326_929-3218 {
  top: 1924px;
  left: 256px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-295_929-3219 {
  top: 1924px;
  left: 276px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-327_929-3220 {
  top: 1924px;
  left: 773px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-297_929-3221 {
  top: 1630px;
  left: 276px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-334_929-3222 {
  top: 1732px;
  left: 276px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-341_1102-537 {
  top: 1732px;
  left: 403px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-342_1102-538 {
  top: 1780px;
  left: 538px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-340_929-7673 {
  top: 1816px;
  left: 276px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-339_929-7670 {
  top: 1707px;
  left: 276px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-338_929-7669 {
  top: 1676px;
  left: 276px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-336_929-3223 {
  top: 1792px;
  left: 276px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-300_929-3225 {
  top: 1618px;
  left: 276px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-328_929-3226 {
  top: 1618px;
  left: 360px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-331_929-3227 {
  top: 1618px;
  left: 471px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-330_929-3228 {
  top: 1618px;
  left: 368px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-332_929-3229 {
  top: 1618px;
  left: 479px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-329_929-3230 {
  top: 1618px;
  left: 384px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-333_929-3231 {
  top: 1618px;
  left: 495px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-corner-radius--8-border-style--solid-border-color--gray-1_929-3232 {
  top: 2136px;
  left: 802px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 117px;
  height: 60px;
}

.notes_924-2278 {
  padding: 20px;
  top: 355px;
  left: 254px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 517px;
}
.head_924-2279 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: 100%;
}
.frame-929_924-2280 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.stack_924-2281 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  width: 100%;
}
.text-label_924-2282 {
  color: #17181a;
  @include h-1-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.right-corner_924-2283 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.menu_924-2284 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 41px;
  width: 100%;
}
.second-menu_924-2285 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: content-box;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-bottom-width: 1px;
  background: #ffffff;
  width: 100%;
}
.bradcrumb_924-2286 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 40px;
}
.box_924-2287 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  height: 100%;
}
.menu_924-2288 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_924-2289 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-color: $variable-collection-primary-blue;
  border-style: solid;
  border-bottom-width: 1px;
  height: 100%;
}
.menu-item_924-2290 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_924-2291 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_924-2293 {
  color: $variable-collection-primary-blue;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item-_924-2295 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_924-2296 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_924-2297 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_924-2299 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item_924-2301 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_924-2302 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_924-2304 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.frame-916_924-2306 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.frame-942_924-2307 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
}
.check_924-2308 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-teleheath_924-2309 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.frame-936_924-2310 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-939_924-2311 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-940_924-2312 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.text-sys_924-2313 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-inclusion_924-2314 {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 127px;
  height: 48px;
}
.row_924-2315 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-value_924-2316 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.frame-941_924-2317 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.text-dias_924-2318 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-inclusion_924-2319 {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 48px;
  width: 100%;
}
.row_924-2320 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-value_924-2321 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.frame-937_924-2322 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 215px;
}
.text-date-of-service_924-2323 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.calendar_924-2324 {
  height: 48px;
  width: 100%;
}

.frame-938_924-2325 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-notes_924-2326 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.notes_924-2327 {
  width: 100%;
}

.notes_929-3472 {
  padding: 20px;
  top: 726px;
  left: 255px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 517px;
}
.head_929-3473 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: 100%;
}
.frame-929_929-3474 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.stack_929-3475 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  width: 100%;
}
.text-label_929-3476 {
  color: #17181a;
  @include h-1-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.right-corner_929-3477 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.menu_929-3478 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 41px;
  width: 100%;
}
.second-menu_929-3479 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: content-box;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-bottom-width: 1px;
  background: #ffffff;
  width: 100%;
}
.bradcrumb_929-3480 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 40px;
}
.box_929-3481 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.menu_929-3482 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3483 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3484 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3485 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3487 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item-_929-3489 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-color: $variable-collection-primary-blue;
  border-style: solid;
  border-bottom-width: 1px;
  height: 100%;
}
.menu-item_929-3490 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3491 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3493 {
  color: $variable-collection-primary-blue;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item_929-3495 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3496 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3497 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3499 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.frame-916_929-3501 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.frame-939_929-3502 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-reasoning_929-3503 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-exclusion_929-3504 {
  height: 44px;
  width: 100%;
}

.frame-940_929-3505 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-date-of-service_929-3506 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.calendar_929-3507 {
  height: 48px;
  width: 100%;
}

.frame-941_929-3508 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-notes_929-3509 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.notes_929-3510 {
  width: 100%;
}

.notes_929-3424 {
  padding: 20px;
  top: 1151px;
  left: 255px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 517px;
}
.head_929-3425 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: 100%;
}
.frame-929_929-3426 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.stack_929-3427 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  width: 100%;
}
.text-label_929-3428 {
  color: #17181a;
  @include h-1-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.right-corner_929-3429 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.menu_929-3430 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 41px;
  width: 100%;
}
.second-menu_929-3431 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: content-box;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-bottom-width: 1px;
  background: #ffffff;
  width: 100%;
}
.bradcrumb_929-3432 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 40px;
}
.box_929-3433 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.menu_929-3434 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3435 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3436 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3437 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3439 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item-_929-3441 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3442 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3443 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3445 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item_929-3447 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-color: $variable-collection-primary-blue;
  border-style: solid;
  border-bottom-width: 1px;
  height: 100%;
}
.menu-item_929-3448 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3449 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3451 {
  color: $variable-collection-primary-blue;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.frame-916_929-3453 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.frame-942_929-3454 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-reasoning_929-3455 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-none_929-3456 {
  height: 44px;
  width: 100%;
}

.frame-943_929-3457 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-notes_929-3458 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.notes_929-3459 {
  width: 100%;
}
