<div data-layer="Hits V2" class="HitsV2" style="width: 1094px; height: 1828px; position: relative; background: white; overflow: hidden">
  <div data-layer="Rectangle 291" class="Rectangle291" style="width: 555.48px; height: 285.20px; left: 225.07px; top: 276.08px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Rectangle 316" class="Rectangle316" style="width: 555.48px; height: 285.20px; left: 225.07px; top: 1063.10px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Rectangle 314" class="Rectangle314" style="width: 555.48px; height: 285.20px; left: 228px; top: 642px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Hits" class="Hits" style="width: 517px; padding: 20px; left: 244.31px; top: 296.68px; position: absolute; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="Frame 913" class="Frame913" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-end; display: flex">
      <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="justify-content: flex-start; align-items: center; gap: 10px; display: flex">
          <div data-layer="label" class="Label" style="color: #17181A; font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Hits</div>
        </div>
      </div>
    </div>
    <div data-layer="table" class="Table" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="columns" class="Columns" style="flex: 1 1 0; justify-content: flex-start; align-items: flex-start; display: flex">
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DoS</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">05/21/24</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Sys</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">136</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">140</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">150</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Dias</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 16px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">90</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Page</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">7</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="width: 216px; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Comment</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-top: 2px; padding-bottom: 2px; background: white; justify-content: center; align-items: center; display: inline-flex">
            <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: center; align-items: center; display: flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="width: 53.54px; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Include</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 18px; padding-bottom: 18px; background: white; justify-content: center; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.8125" y="1.17822" width="15" height="15" rx="4.5" fill="white" stroke="#D9E1E7"/>
                </svg>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.8125" y="1.17822" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
                </svg>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.8125" y="1.17822" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Sub-Table" class="SubTable" style="width: 517px; padding: 20px; left: 244.31px; top: 1083.70px; position: absolute; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="Frame 913" class="Frame913" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-end; display: flex">
      <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="justify-content: flex-start; align-items: center; gap: 10px; display: flex">
          <div data-layer="label" class="Label" style="color: #17181A; font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Hits</div>
        </div>
      </div>
    </div>
    <div data-layer="table" class="Table" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="columns" class="Columns" style="flex: 1 1 0; justify-content: flex-start; align-items: flex-start; display: flex">
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DoS</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">05/21/24</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Sys</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">136</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">140</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">150</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Dias</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 16px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">90</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Page</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">7</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="width: 216px; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Comment</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-top: 2px; padding-bottom: 2px; background: white; justify-content: center; align-items: center; display: inline-flex">
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="flex: 1 1 0; height: 30px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="flex: 1 1 0; height: 30px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: center; align-items: center; display: flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="width: 53.54px; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Include</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 18px; padding-bottom: 18px; background: white; justify-content: center; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.8125" y="1.19629" width="15" height="15" rx="4.5" fill="white" stroke="#D9E1E7"/>
                </svg>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.8125" y="1.19629" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
                </svg>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.8125" y="1.19629" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Sub-Table" class="SubTable" style="width: 517px; padding: 20px; left: 247.24px; top: 662.60px; position: absolute; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="Frame 913" class="Frame913" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-end; display: flex">
      <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="justify-content: flex-start; align-items: center; gap: 10px; display: flex">
          <div data-layer="label" class="Label" style="color: #17181A; font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Hits</div>
        </div>
      </div>
    </div>
    <div data-layer="table" class="Table" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="columns" class="Columns" style="flex: 1 1 0; justify-content: flex-start; align-items: flex-start; display: flex">
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DoS</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">05/21/24</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Sys</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">136</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">140</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">150</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Dias</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 16px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">90</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Page</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">7</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="width: 216px; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Comment</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-top: 2px; padding-bottom: 2px; background: white; justify-content: center; align-items: center; display: inline-flex">
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="flex: 1 1 0; height: 30px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="flex: 1 1 0; height: 30px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: center; align-items: center; display: flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="width: 53.54px; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Include</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 18px; padding-bottom: 18px; background: white; justify-content: center; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.742188" y="1.09766" width="15" height="15" rx="4.5" fill="white" stroke="#D9E1E7"/>
                </svg>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.742188" y="1.09766" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
                </svg>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.742188" y="1.09766" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Spacing:" class="Spacing" style="width: 83px; height: 15.69px; left: 63.74px; top: 606.02px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Spacing:</div>
  <div data-layer="Styling:" class="Styling" style="width: 83px; height: 15.69px; left: 62px; top: 1013.77px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Styling:</div>
  <div data-layer="Behavior: Scrolls with page. Interactive in both default and entered state. Categories change based on the measure." class="BehaviorScrollsWithPageInteractiveInBothDefaultAndEnteredStateCategoriesChangeBasedOnTheMeasure" style="width: 1039.94px; height: 33.64px; left: 62px; top: 170px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Behavior: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Scrolls with page. Interactive in both default and entered state. Categories change based on the measure.</span></div>
  <div data-layer="Sorting: Entries are sorted from oldest (bottom) to newest (top) date of service." class="SortingEntriesAreSortedFromOldestBottomToNewestTopDateOfService" style="width: 1039.94px; height: 16px; left: 62px; top: 219.64px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Sorting: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Entries are sorted from oldest (bottom) to newest (top) date of service.</span></div>
  <div data-layer="Usage: The container where the genAI tool’s identifications are housed." class="UsageTheContainerWhereTheGenaiToolSIdentificationsAreHoused" style="width: 1478.31px; left: 62.83px; top: 132px; position: absolute"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Usage: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; word-wrap: break-word">The container where the genAI tool’s identifications are housed.</span></div>
  <div data-layer="Hits container" class="HitsContainer" style="left: 62px; top: 62.80px; position: absolute; color: black; font-size: 36px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Hits container</div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 246.95px; top: 906.85px; position: absolute">
    <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.950194 0.848632L0.950195 9.12889L20.9502 9.12889L20.9502 0.848631M10.9502 9.20708L10.9502 20.6291" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 225.07px; top: 886.85px; position: absolute">
    <svg width="20" height="22" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M19.8518 0.848877L11.5716 0.848877L11.5716 20.8489H19.8518M11.4934 10.8489L0.0712891 10.8489" stroke="black"/>
    </svg>
  </div>
  <div data-layer="20px" class="Px" style="left: 242.48px; top: 934.49px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">20px</div>
  <div data-layer="20px" class="Px" style="left: 191.44px; top: 896.85px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">20px</div>
  <div data-layer="corner-radius: 8 border-style: solid  border-color: gray-1" class="CornerRadius8BorderStyleSolidBorderColorGray1" style="left: 792.78px; top: 1070.10px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">corner-radius: 8<br/>border-style: solid  border-color: gray-1</div>
  <div data-layer="Default: not selected" class="DefaultNotSelected" style="left: 774.11px; top: 419.04px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Default: not selected</div>
  <div data-layer="Comment boxes allow for text entry that saves when the user Submits the review" class="CommentBoxesAllowForTextEntryThatSavesWhenTheUserSubmitsTheReview" style="width: 194.78px; left: 774.11px; top: 474.68px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Comment boxes allow for text entry that saves when the user Submits the review</div>
  <div data-layer="Component: check Align: align-center" class="ComponentCheckAlignAlignCenter" style="left: 791.75px; top: 1245.70px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Component: check<br/>Align: align-center</div>
  <div data-layer="Component: Comments-box" class="ComponentCommentsBox" style="left: 791.75px; top: 1305.70px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Component:<br/>Comments-box</div>
  <div data-layer="Header-item stroke: bottom Size: 1px Color: gray-1" class="HeaderItemStrokeBottomSize1pxColorGray1" style="left: 791.75px; top: 1181.25px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Header-item stroke: bottom <br/>Size: 1px <br/>Color: gray-1</div>
  <div data-layer="Font: Body Color: link Text-decoration: underline" class="FontBodyColorLinkTextDecorationUnderline" style="width: 104.05px; left: 130.57px; top: 1196.06px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: Body<br/>Color: link<br/>Text-decoration: underline</div>
  <div data-layer="Font: Body Color: text-black" class="FontBodyColorTextBlack" style="left: 130.57px; top: 1281.70px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: Body<br/>Color: text-black</div>
  <div data-layer="Font: H1 Color: text-black" class="FontH1ColorTextBlack" style="left: 130.57px; top: 1100.06px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: H1<br/>Color: text-black</div>
  <div data-layer="Font: H3 Color: text-black" class="FontH3ColorTextBlack" style="left: 130.57px; top: 1149.06px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: H3<br/>Color: text-black</div>
  <div data-svg-wrapper data-layer="Vector 15" class="Vector15" style="left: 234.62px; top: 438.23px; position: absolute">
    <svg width="195" height="2" viewBox="0 0 195 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.62207 1.2334H194.312" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 21" class="Vector21" style="left: 234.62px; top: 1225.25px; position: absolute">
    <svg width="194" height="2" viewBox="0 0 194 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.62207 1.25073L193.813 1.25075" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 24" class="Vector24" style="left: 234.62px; top: 1285.25px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.62207 1.25073L34.3389 1.25074" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 25" class="Vector25" style="left: 234.62px; top: 1121.25px; position: absolute">
    <svg width="26" height="2" viewBox="0 0 26 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.62207 1.25073L25.0234 1.25073" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 26" class="Vector26" style="left: 234.62px; top: 1170.25px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.62207 1.25073L34.3389 1.25074" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 16" class="Vector16" style="left: 428.31px; top: 429.04px; position: absolute">
    <svg width="2" height="10" viewBox="0 0 2 10" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.3125 9.33789L1.3125 0.0419918" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 22" class="Vector22" style="left: 427.81px; top: 1216.06px; position: absolute">
    <svg width="2" height="10" viewBox="0 0 2 10" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.8125 9.35547L0.8125 0.05957" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 30" class="Vector30" style="left: 662.63px; top: 1297.70px; position: absolute">
    <svg width="2" height="10" viewBox="0 0 2 10" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.630859 9.99219L0.63086 0.696289" stroke="black"/>
    </svg>
  </div>
  <div data-layer="Clicking page number links to section of page where the corresponding identification is" class="ClickingPageNumberLinksToSectionOfPageWhereTheCorrespondingIdentificationIs" style="width: 166.95px; height: 72px; left: 62.83px; top: 429.04px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Clicking page number links to section of page where the corresponding identification is</div>
  <div data-svg-wrapper data-layer="Vector 14" class="Vector14" style="left: 733.31px; top: 419.04px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.3125 1.04199H34.4951" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 28" class="Vector28" style="left: 658.48px; top: 474.68px; position: absolute">
    <svg width="110" height="2" viewBox="0 0 110 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.478516 0.678223L109.495 0.678232" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 23" class="Vector23" style="left: 749.30px; top: 1247.33px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.300781 1.33301H34.4834" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 29" class="Vector29" style="left: 662.63px; top: 1307.33px; position: absolute">
    <svg width="122" height="2" viewBox="0 0 122 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.630859 1.33301L121.483 1.333" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 27" class="Vector27" style="left: 746.37px; top: 1185.70px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.37207 0.696289H34.5547" stroke="black"/>
    </svg>
  </div>
  <div data-layer="Rectangle 293" class="Rectangle293" style="width: 20px; height: 517.76px; left: 246.48px; top: 682.85px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 294" class="Rectangle294" style="width: 20px; height: 517.76px; left: 246.48px; top: 906.85px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 295" class="Rectangle295" style="width: 20px; height: 244px; left: 266.78px; top: 906.85px; position: absolute; transform: rotate(180deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 296" class="Rectangle296" style="width: 20px; height: 244px; left: 764.24px; top: 906.85px; position: absolute; transform: rotate(180deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 297" class="Rectangle297" style="width: 12px; height: 478.09px; left: 266.15px; top: 726.85px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 298" class="Rectangle298" style="width: 10px; height: 478.09px; left: 266.15px; top: 736.85px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 299" class="Rectangle299" style="width: 10px; height: 478.09px; left: 266.15px; top: 766.85px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 312" class="Rectangle312" style="width: 10px; height: 478.09px; left: 266.15px; top: 886.85px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 300" class="Rectangle300" style="width: 8px; height: 160px; left: 266.78px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 301" class="Rectangle301" style="width: 8px; height: 160px; left: 329.48px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 302" class="Rectangle302" style="width: 8px; height: 160px; left: 337.48px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 303" class="Rectangle303" style="width: 8px; height: 160px; left: 368.24px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 304" class="Rectangle304" style="width: 8px; height: 160px; left: 376.24px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 305" class="Rectangle305" style="width: 8px; height: 160px; left: 411.24px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 315" class="Rectangle315" style="width: 8px; height: 160px; left: 684.24px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 306" class="Rectangle306" style="width: 8px; height: 160px; left: 419.24px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 307" class="Rectangle307" style="width: 8px; height: 160px; left: 460.24px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 311" class="Rectangle311" style="width: 8px; height: 160px; left: 736.24px; top: 726.85px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 245.48px; top: 714.85px; position: absolute">
    <svg width="21" height="14" viewBox="0 0 21 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.262 0.848877L11.9817 0.848877L11.9817 12.8489L20.262 12.8489M11.9035 6.84888L0.481445 6.84888" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 245.48px; top: 726.85px; position: absolute">
    <svg width="21" height="12" viewBox="0 0 21 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.262 0.848877L11.9817 0.848877L11.9817 10.8489L20.262 10.8489M11.9035 5.84888L0.481445 5.84888" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 245.48px; top: 756.85px; position: absolute">
    <svg width="21" height="12" viewBox="0 0 21 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.262 0.848877L11.9817 0.848877L11.9817 10.8489L20.262 10.8489M11.9035 5.84888L0.481445 5.84888" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 245.48px; top: 876.85px; position: absolute">
    <svg width="21" height="12" viewBox="0 0 21 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.262 0.848877L11.9817 0.848877L11.9817 10.8489L20.262 10.8489M11.9035 5.84888L0.481445 5.84888" stroke="black"/>
    </svg>
  </div>
  <div data-layer="12px" class="Px" style="left: 214.35px; top: 718.85px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12px</div>
  <div data-layer="10px" class="Px" style="left: 214.35px; top: 731.85px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">10px</div>
  <div data-layer="10px" class="Px" style="left: 214.35px; top: 761.85px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">10px</div>
  <div data-layer="10px" class="Px" style="left: 214.35px; top: 881.85px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">10px</div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 329.48px; top: 886.85px; position: absolute">
    <svg width="10" height="23" viewBox="0 0 10 23" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.47851 0.848877L1.47852 9.99036L9.47852 9.99036L9.47851 0.848877M5.47852 10.0767L5.47852 22.6868" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 337.98px; top: 886.85px; position: absolute">
    <svg width="10" height="23" viewBox="0 0 10 23" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.978515 0.848877L0.978515 9.99036L8.97852 9.99036L8.97851 0.848877M4.97852 10.0767L4.97852 22.6868" stroke="black"/>
    </svg>
  </div>
  <div data-layer="8px" class="Px" style="left: 311.74px; top: 914.12px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">8px</div>
  <div data-layer="8px" class="Px" style="left: 338.98px; top: 914.12px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">8px</div>
  <div data-layer="All table items are align-left except for checkboxes, which are align-center" class="AllTableItemsAreAlignLeftExceptForCheckboxesWhichAreAlignCenter" style="width: 169.24px; left: 798.52px; top: 764.85px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">All table items are align-left except for checkboxes, which are align-center</div>
  <div data-layer="Hits" class="Hits" style="width: 517px; padding: 20px; left: 149.81px; top: 1443.10px; position: absolute; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="Frame 913" class="Frame913" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-end; display: flex">
      <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="justify-content: flex-start; align-items: center; gap: 10px; display: flex">
          <div data-layer="label" class="Label" style="color: #17181A; font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Hits</div>
        </div>
      </div>
    </div>
    <div data-layer="table" class="Table" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="columns" class="Columns" style="flex: 1 1 0; justify-content: flex-start; align-items: flex-start; display: flex">
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DoS</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: #EFF3FA; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">05/21/24</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Sys</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: #EFF3FA; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">136</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">140</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">150</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Dias</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: #EFF3FA; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="width: 16px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">90</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Page</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: #EFF3FA; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
              <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">7</div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="width: 216px; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Comment</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-top: 2px; padding-bottom: 2px; background: #EFF3FA; justify-content: center; align-items: center; display: inline-flex">
            <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
              </div>
            </div>
          </div>
        </div>
        <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: center; align-items: center; display: flex">
            <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
              <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Label" class="Label" style="width: 53.54px; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Include</div>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 18px; padding-bottom: 18px; background: #EFF3FA; justify-content: center; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="1.3125" y="0.603516" width="15" height="15" rx="4.5" fill="white" stroke="#D9E1E7"/>
                </svg>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="1.3125" y="0.603516" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
                </svg>
              </div>
            </div>
            <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="1.3125" y="0.603516" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>