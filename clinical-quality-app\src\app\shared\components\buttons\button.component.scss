@use "sass:color";
@use 'styles/variables' as vars;
@use 'styles/mixins' as mix;
@use 'styles/typography' as type;

// Base button styles
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px; // Figma specification: 8px gap between icon and text
  border-radius: vars.$border-radius-lg;
  font-family: type.$font-family-base; // Urbane font family
  font-size: type.$font-size-base; // 12px
  font-weight: type.$font-weight-medium; // 500
  line-height: type.$line-height-base; // 20px
  cursor: pointer;
  transition: all 0.3s ease; // Transition all properties for smooth changes
  border: none;
  outline: none;

  &:focus {
    outline: none;
  }

  &.button-with-icon {
    gap: 8px; // Consistent 8px gap
  }

  .button-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
  }

  // Ensure proper alignment for refresh icon
  app-refresh-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    vertical-align: middle;
    flex-shrink: 0;
  }

  &.btn-icon-right {
    flex-direction: row-reverse;
  }
}

// Primary button
.button-primary {
  background-color: vars.$primary-blue;
  color: vars.$white;
  padding: vars.$spacing-sm vars.$spacing-lg; // Default: 8px 16px

  // Figma-exact sizing override
  &.figma-exact {
    padding: 10px 16px; // Figma specification: 10px 16px
  }

  // Figma state overrides - exact submit button specifications
  &.figma-state-inactive {
    background-color: #BFD0EE; // Exact Figma inactive color
    color: vars.$white;
    cursor: not-allowed;
    transition: none; // No transitions for inactive state

    &:hover {
      background-color: #BFD0EE; // No hover effect when inactive
      transform: none;
      box-shadow: none;
    }

    &:active {
      background-color: #BFD0EE; // No active effect when inactive
    }
  }

  &.figma-state-default {
    background-color: vars.$primary-blue; // #3870B8 - Exact Figma default color
    color: vars.$white;
    transition: all 0.3s ease; // Figma specifies 0.3s transition
  }

  &.figma-state-hover {
    background-color: #468CE7; // Exact Figma hover color
    color: vars.$white;
  }

  &.figma-state-click {
    background-color: #285082; // Exact Figma click color
    color: vars.$white;
  }

  // Default hover/active states (when not using figmaState)
  &:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click) {
    &:hover {
      background-color: #468CE7; // Use Figma hover color for consistency
    }

    &:active {
      background-color: #285082; // Use Figma click color for consistency
    }
  }

  // When figmaExact is true, always use Figma colors for interactive states
  &.figma-exact:not(.figma-state-inactive):not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click) {
    &:hover {
      background-color: #468CE7; // Figma hover color
    }

    &:active {
      background-color: #285082; // Figma click color
    }
  }

  &.button-disabled {
    background-color: #BFD0EE; // Use same color as inactive for consistency with Figma
    color: vars.$white;
    cursor: not-allowed;
  }
}

// Secondary button
.button-secondary {
  background-color: vars.$white;
  color: vars.$text-black;
  border: vars.$border-width-default solid vars.$gray-2;
  padding: vars.$spacing-sm vars.$spacing-md; // Default: 8px 12px

  // Figma-exact sizing override
  &.figma-exact {
    padding: 8px 14px; // Figma specification: 8px 14px
  }

  // Figma state overrides for demo purposes
  &.figma-state-default {
    background-color: vars.$white;
    color: vars.$text-black;
    border: vars.$border-width-default solid vars.$gray-2;

    app-refresh-icon {
      color: vars.$text-black; // Figma: icon should be black on white/gray buttons
    }
  }

  &.figma-state-hover {
    background-color: vars.$gray-1; // Figma hover: gray-1
    color: vars.$text-black;
    border: vars.$border-width-default solid vars.$gray-2;

    app-refresh-icon {
      color: vars.$text-black; // Figma: icon stays black on hover
    }
  }

  &.figma-state-click {
    background-color: vars.$text-black; // Figma click: text-black
    color: vars.$white;
    border: vars.$border-width-default solid vars.$gray-2;

    app-refresh-icon {
      color: vars.$white; // Figma: icon becomes white on dark background
    }
  }

  // Default hover/active states (when not using figmaState)
  &:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click) {
    &:hover {
      background-color: vars.$gray-1; // Use Figma hover color
    }

    &:active {
      background-color: vars.$text-black; // Use Figma click color
      color: vars.$white;
    }
  }

  // When figmaExact is true, ensure Figma colors for interactive states
  &.figma-exact:not(.figma-state-default):not(.figma-state-hover):not(.figma-state-click) {
    app-refresh-icon {
      color: vars.$text-black; // Figma: icon should be black by default
    }

    &:hover {
      background-color: vars.$gray-1; // Figma hover: gray-1

      app-refresh-icon {
        color: vars.$text-black; // Figma: icon stays black on hover
      }
    }

    &:active {
      background-color: vars.$text-black; // Figma click: text-black
      color: vars.$white;

      app-refresh-icon {
        color: vars.$white; // Figma: icon becomes white on dark background
      }
    }
  }

  &.button-disabled {
    color: vars.$gray-3;
    border-color: vars.$gray-1;
    cursor: not-allowed;
  }
}

// Tertiary button
.button-tertiary {
  background-color: transparent;
  color: vars.$primary-blue;
  box-shadow: none;

  &:hover {
    background-color: rgba(vars.$primary-blue, 0.05);
  }

  &:active {
    background-color: rgba(vars.$primary-blue, 0.1);
  }

  &.button-disabled {
    color: vars.$gray-3;
    cursor: not-allowed;
  }
}

// Button sizes
.button-sm {
  padding: vars.$spacing-sm vars.$spacing-md;
  font-size: type.$font-size-sm;
}

.button-lg {
  padding: vars.$spacing-lg vars.$spacing-xl;
  font-size: type.$font-size-md;
}

// Full width button
.button-block {
  width: 100%;
  display: flex;
}