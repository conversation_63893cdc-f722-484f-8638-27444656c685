{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"clinical-quality-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/clinical-quality-app", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "stylePreprocessorOptions": {"includePaths": ["src/styles", "src", "node_modules"]}, "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "/assets/"}, {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["src/styles.scss"], "scripts": [], "server": "src/main.server.ts", "prerender": true, "ssr": {"entry": "src/server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1.5MB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "10kB", "maximumError": "15kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "clinical-quality-app:build:production"}, "development": {"buildTarget": "clinical-quality-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "stylePreprocessorOptions": {"includePaths": ["src/styles", "src", "node_modules"]}, "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "/assets/"}, {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}