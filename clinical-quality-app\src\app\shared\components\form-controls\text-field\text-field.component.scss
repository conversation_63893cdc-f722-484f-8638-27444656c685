@use '../../../../../styles/variables';
@use '../../../../../styles/mixins';
@use '../../../../../styles/typography' as typography;

.text-field-container {
  display: flex;
  flex-direction: column;
  margin-bottom: variables.$spacing-md;
  width: 100%;
}

.text-field-label {
  font-size: typography.$font-size-base;
  font-weight: typography.$font-weight-medium;
  color: variables.$text-black;
  margin-bottom: variables.$spacing-xs;
  
  .required-indicator {
    color: variables.$success-green;
    margin-left: variables.$spacing-xs;
  }
}

.input-wrapper {
  position: relative;
}

.text-field-input {
  @include mixins.input-field;
  height: 40px;
  transition: all 0.2s ease;
  padding: variables.$spacing-md;
  border-radius: variables.$border-radius-xl;
  
  &:focus {
    outline: variables.$border-width-default variables.$primary-blue solid;
    outline-offset: -(variables.$border-width-default);
    box-shadow: 0 0 0 2px rgba(variables.$primary-blue, 0.1);
  }
  
  &:disabled {
    background-color: variables.$gray-1;
    color: variables.$gray-3;
    cursor: not-allowed;
  }
}

.error-message {
  font-size: typography.$font-size-sm;
  color: variables.$success-green;
  margin-top: variables.$spacing-xs;
}

.helper-text {
  font-size: typography.$font-size-sm;
  color: variables.$gray-3;
  margin-top: variables.$spacing-xs;
}

// State classes
.text-field-container {
  &.focused {
    .text-field-input {
      outline: variables.$border-width-default variables.$primary-blue solid;
      outline-offset: -(variables.$border-width-default);
    }
  }
  
  &.hovered:not(.focused):not(.disabled) {
    .text-field-input {
      border-color: variables.$gray-3;
    }
  }
  
  &.has-error {
    .text-field-input {
      outline: variables.$border-width-default variables.$success-green solid;
      outline-offset: -(variables.$border-width-default);
    }
  }
}