<nav class="menu-container" (document:click)="onDocumentClick($event)">
  <div class="menu-content">
    <!-- Logo Section -->
    <div class="logo-section">
      <div class="logo-container" (click)="onLogoClick()">
        <img 
          *ngIf="logoSrc" 
          [src]="logoSrc" 
          [alt]="logoAlt" 
          class="logo-image"
        />
        <div *ngIf="!logoSrc" class="logo-placeholder">
          {{ logoAlt }}
        </div>
      </div>
    </div>

    <!-- User Section -->
    <div class="user-section" *ngIf="user">
      <div class="user-info">
        <span class="user-name">{{ user.name }}</span>
        
        <!-- User Avatar -->
        <div class="user-avatar" (click)="onUserClick()">
          <div class="avatar-circle">
            <svg class="user-icon" viewBox="0 0 20 20" fill="none">
              <!-- User head -->
              <circle cx="10" cy="7.5" r="2.75" stroke="currentColor" stroke-width="1.5" fill="none"/>
              <!-- User body -->
              <path d="M4.5 16.5c0-3 2.5-5.5 5.5-5.5s5.5 2.5 5.5 5.5" stroke="currentColor" stroke-width="1.5" fill="none"/>
              <!-- Outer circle -->
              <circle cx="10" cy="10" r="8.33" stroke="currentColor" stroke-width="1.5" fill="none"/>
            </svg>
          </div>
        </div>

        <!-- Dropdown Arrow -->
        <div 
          class="dropdown-arrow" 
          [class.open]="isDropdownOpen"
          (click)="onDropdownToggle()"
          *ngIf="showUserDropdown"
        >
          <svg class="arrow-icon" viewBox="0 0 24 24" fill="none">
            <path d="M8 10l4 4 4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>

      <!-- Dropdown Menu -->
      <div class="user-dropdown" *ngIf="isDropdownOpen && menuItems.length > 0">
        <div class="dropdown-content">
          <div 
            *ngFor="let item of menuItems" 
            class="dropdown-item"
            (click)="onMenuItemClick(item)"
          >
            <span *ngIf="item.icon" class="item-icon">{{ item.icon }}</span>
            <span class="item-label">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>
