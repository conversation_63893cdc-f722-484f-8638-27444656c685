@use './styles/variables' as variables;
@use './styles/typography' as type;
@use './styles/mixins' as mix;
@use './styles/theme' as theme;


/* Global Styles */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: variables.$light-background;
}

body {
  font-family: 'Urbane', sans-serif;
  color: variables.$text-black;
}

/* Essential Utility Classes */

// Width/Height
.w-100 { width: 100%; }
.h-100 { height: 100%; }

// Common Components
.card {
  @include mix.card;
}

.input-field {
  @include mix.input-field;
}

.textarea-field {
  @include mix.textarea-field;
}

