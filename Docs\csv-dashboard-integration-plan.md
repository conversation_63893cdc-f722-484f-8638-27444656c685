# CSV Dashboard Integration Implementation Plan

## Overview

This document outlines the implementation plan for integrating CSV data with the dashboard component, aligning the UI with updated Figma specifications, and replacing hardcoded mock data with dynamic CSV-loaded data.

## Current State Analysis

### Updated Figma Files
- **dashboard.html**: Shows detailed table structure with specific columns (Member ID, First name, Last name, Middle name, DOB, LOB, Measure, Review 1, Review 2, Assigned, Status)
- **dashboard.scss**: Contains comprehensive styling specifications with proper spacing, typography, and layout definitions

### CSV Data Structure
The `MRSDS_Template_CQ(Template).csv` file contains the following fields:
- `FILENAME`: Chart filename identifier
- `MBR_LNAME`, `MBR_MNAME`, `MBR_FNAME`: Member name components
- `MBR_DOB`: Member date of birth
- `LOB`: Line of business
- `MeasureKey`: Measure identifier (CBP)
- `BSC_MBR_ID`: Member ID
- `MBR_GENDER`: Member gender
- `PRVR_NPI`, `PRVR_LNAME`, `PRVR_FNAME`: Provider information

### Current Implementation Issues
- Dashboard uses hardcoded mock data
- Table structure doesn't match Figma specifications
- Missing separation of name fields (first, middle, last)
- Styling doesn't align with designer specifications
- No dynamic data loading capability

## Implementation Plan

### Phase 1: Data Models and Services

#### 1.1 Create Enhanced Data Models
**Files to create:**
- `src/app/core/data/models/chart-data.models.ts`

**Objectives:**
- Define interfaces matching CSV structure
- Create transformation models for UI consumption
- Add provider information models
- Ensure type safety throughout the application

#### 1.2 Create CSV Data Service
**Files to create:**
- `src/app/core/data/services/csv-data.service.ts`

**Objectives:**
- Load CSV files from assets directory
- Parse CSV data using appropriate library
- Transform raw CSV data to component-friendly format
- Implement caching and refresh mechanisms
- Handle loading states and error conditions

#### 1.3 Update Dashboard Component
**Files to modify:**
- `src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.ts`

**Objectives:**
- Replace hardcoded data with service-loaded data
- Implement proper data loading states
- Add error handling for CSV loading failures
- Integrate refresh functionality with CSV reloading

### Phase 2: UI Component Updates

#### 2.1 Update Assigned Table Component
**Files to modify:**
- `src/app/features/dashboard/components/assigned-table/assigned-table.component.ts`
- `src/app/features/dashboard/components/assigned-table/assigned-table.component.html`

**Objectives:**
- Update interface to match new data structure
- Modify column definitions to separate name fields
- Update template to match Figma design structure
- Ensure proper data binding and display

#### 2.2 Apply Figma Styling
**Files to modify:**
- `src/app/features/dashboard/components/assigned-table/assigned-table.component.scss`
- `src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.scss`

**Objectives:**
- Replace current SCSS with Figma-aligned styles
- Fix `-webkit-fill-available` compatibility issues (replace with `100%`)
- Implement responsive design patterns
- Apply proper spacing, typography, and color specifications
- Ensure consistent styling across components

### Phase 3: Integration and Testing

#### 3.1 Integration Testing
**Objectives:**
- Test CSV loading functionality
- Verify data transformation accuracy
- Test refresh functionality
- Validate error handling scenarios

#### 3.2 UI/UX Validation
**Objectives:**
- Ensure design matches Figma specifications exactly
- Test responsive behavior across different screen sizes
- Validate accessibility compliance
- Test user interactions and navigation

## Detailed Implementation Steps

### Step 1: Install Dependencies
```bash
npm install papaparse
npm install --save-dev @types/papaparse
```

### Step 2: Create Data Models
Create comprehensive TypeScript interfaces for:
- Raw CSV data structure
- Transformed chart data for UI
- Provider information
- Loading states and error handling

### Step 3: Implement CSV Data Service
- HTTP client integration for loading CSV files
- CSV parsing using PapaParse library
- Data transformation logic
- Caching mechanism for performance
- Error handling and retry logic

### Step 4: Update Dashboard Components
- Replace mock data with service calls
- Implement loading indicators
- Add error state handling
- Update refresh functionality

### Step 5: UI Component Modernization
- Update table structure to match Figma
- Apply new styling specifications
- Implement responsive design
- Ensure cross-browser compatibility

### Step 6: Style Integration
- Replace existing SCSS with Figma styles
- Fix CSS compatibility issues
- Implement proper spacing and typography
- Ensure consistent visual design

## Key Benefits

1. **Data-Driven Architecture**: Dashboard displays real data from CSV files
2. **Figma-Aligned Design**: UI matches designer specifications exactly
3. **Maintainable Code**: Clean separation of data loading and presentation
4. **Scalable Solution**: Easy to extend for additional data sources
5. **Responsive Design**: Proper responsive behavior across devices
6. **Type Safety**: Full TypeScript integration for better development experience

## Files Requiring Changes

### New Files
- `src/app/core/data/models/chart-data.models.ts`
- `src/app/core/data/services/csv-data.service.ts`

### Modified Files
- `src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.ts`
- `src/app/features/dashboard/components/assigned-table/assigned-table.component.ts`
- `src/app/features/dashboard/components/assigned-table/assigned-table.component.html`
- `src/app/features/dashboard/components/assigned-table/assigned-table.component.scss`
- `src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.scss`

### Dependencies
- `papaparse` - CSV parsing library
- `@types/papaparse` - TypeScript definitions

## Success Criteria

1. Dashboard loads data dynamically from CSV files
2. Table structure matches Figma specifications exactly
3. All styling aligns with designer specifications
4. Refresh functionality works correctly
5. Error handling provides appropriate user feedback
6. Responsive design works across all target devices
7. Code maintains high quality and type safety standards

## Next Steps

1. Create the implementation plan document ✓
2. Install required dependencies
3. Begin Phase 1 implementation with data models and services
4. Proceed through phases systematically
5. Test and validate each phase before proceeding
6. Document any deviations or additional requirements discovered during implementation
