<div class="text-field-container" 
     [class.focused]="isFocused" 
     [class.hovered]="isHovered"
     [class.disabled]="disabled"
     [class.has-error]="errorMessage">
  <label *ngIf="label" [for]="id" class="text-field-label">
    {{ label }}
    <span *ngIf="required" class="required-indicator">*</span>
  </label>
  <div class="input-wrapper">
    <input
      [type]="type"
      [id]="id"
      [name]="name"
      [placeholder]="placeholder"
      [disabled]="disabled"
      [required]="required"
      [attr.maxlength]="maxLength"
      [value]="value"
      (input)="onInputChange($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
      (mouseenter)="onMouseEnter()"
      (mouseleave)="onMouseLeave()"
      class="text-field-input"
    />
  </div>
  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>
  <div *ngIf="helperText && !errorMessage" class="helper-text">
    {{ helperText }}
  </div>
</div>