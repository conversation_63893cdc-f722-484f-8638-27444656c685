<div class="dropdown_506-9753">
  <div class="rectangle-314_506-9757">
    <svg
      width="456"
      height="207"
      viewBox="0 0 456 207"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.884766"
        y="0.333008"
        width="454.672"
        height="206.597"
        fill="#F2F2F2"
      />
    </svg>
  </div>
  <div class="rectangle-333_506-9758">
    <svg
      width="449"
      height="411"
      viewBox="0 0 449 411"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.884766"
        y="0.621582"
        width="447.313"
        height="410.037"
        fill="#F2F2F2"
      />
    </svg>
  </div>
  <div class="rectangle-335_649-5211">
    <svg
      width="449"
      height="411"
      viewBox="0 0 449 411"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.884766"
        y="0.647949"
        width="447.313"
        height="410.037"
        fill="#F2F2F2"
      />
    </svg>
  </div>
  <span class="text-spacing-_501-14380">Spacing:</span>
  <span class="text-styling-_501-14381">Styling:</span>
  <span class="text-variants-_649-5099">Variants:</span>
  <span class="text-exclusions-_649-5213">Exclusions:</span>
  <span class="text-none-found-_649-5214">None found:</span>
  <span
    class="text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state-_307-12563"
    >Behavior: Scrolls with page. Interactive in both default and entered
    state.</span
  >
  <span
    class="text-sorting--sorting-for-all-items-in-the-dropdown-is-alphabetical-_488-12548"
    >Sorting: Sorting for all items in the dropdown is alphabetical.</span
  >
  <span class="text-usage--select-reasoning-for-a-finding-_307-12564"
    >Usage: Select reasoning for a finding.</span
  >
  <span class="text-dropdown_307-12565">Dropdown</span>
  <span class="text-default--no-reasoning-selected_307-12578"
    >Default: no reasoning selected</span
  >
  <span class="text-clicked--top-level-clicked_307-12580"
    >Clicked: top-level clicked</span
  >
  <span
    class="text-items-in-list-are-multiselect--and-the-user-can-select-multiple-at-the-same-time_488-12551"
    >Items in list are multiselect, and the user can select multiple at the same
    time</span
  >
  <span class="text-selected--item-selected-from-dropdown_307-12582"
    >Selected: item selected from dropdown</span
  >
  <span
    class="text-entered--item-selected-from-dropdown--label-changes-to-match-selected-item_307-12584"
    >Entered: item selected from dropdown, label changes to match selected
    item</span
  >
  <span
    class="text-corner-radius--10-border-style--solid-border-color--gray-2_506-9751"
    >corner-radius: 10 border-style: solidborder-color: gray-2</span
  >
  <span class="text-border-style--solid-border-color--gray-3_938-15889"
    >border-style: solidborder-color: gray-3</span
  >
  <span class="text-border-style--solid-border-color--gray-3_938-15891"
    >border-style: solidborder-color: gray-3</span
  >
  <div class="vector-9_307-12579">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.958984 1.08032H35.1416" stroke="black" />
    </svg>
  </div>
  <div class="vector-10_307-12581">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.958984 1.08032H35.1416" stroke="black" />
    </svg>
  </div>
  <div class="vector-13_488-12552">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.958984 1.08032H35.1416" stroke="black" />
    </svg>
  </div>
  <div class="vector-11_307-12583">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.958984 1.08081H35.1416" stroke="black" />
    </svg>
  </div>
  <div class="vector-12_307-12585">
    <svg
      width="36"
      height="2"
      viewBox="0 0 36 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.958984 1.08081H35.1416" stroke="black" />
    </svg>
  </div>
  <div class="dropdown_649-5178">
    <div class="row_649-5179">
      <span class="text-select_649-5180">Select</span>
      <app-icon-right class="icon-right_649-5181"></app-icon-right>
    </div>
  </div>
  <div class="dropdown_649-5182">
    <div class="row_649-5183">
      <span class="text-select_649-5184">Select</span>
      <app-icon-down class="icon-down_649-5185"></app-icon-down>
    </div>
    <div class="div_649-5186">
      <div class="rectangle-1_649-5187">
        <svg
          width="411"
          height="2"
          viewBox="0 0 411 2"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="-3.02148"
            y="0.166504"
            width="418"
            height="1"
            fill="#D9E1E7"
          />
        </svg>
      </div>
    </div>
    <div class="row_649-5188">
      <app-check [property1]="'checked'" class="check_649-5189"></app-check>
      <span class="text-selection_649-5190">Selection</span>
    </div>
  </div>
  <div class="dropdown_649-5191">
    <div class="row_649-5192">
      <span class="text-select_649-5193">Select</span>
      <app-icon-down class="icon-down_649-5194"></app-icon-down>
    </div>
    <div class="div_649-5195">
      <div class="rectangle-1_649-5196">
        <svg
          width="411"
          height="2"
          viewBox="0 0 411 2"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="-3.02148"
            y="0.166504"
            width="418"
            height="1"
            fill="#D9E1E7"
          />
        </svg>
      </div>
    </div>
    <div class="row_649-5197">
      <app-check [property1]="'default'" class="check_649-5198"></app-check>
      <span class="text-selection_649-5199">Selection</span>
    </div>
  </div>
  <div class="dropdown_649-5200">
    <div class="row_649-5201">
      <span class="text-select_649-5202">Select</span>
      <app-icon-right class="icon-right_649-5203"></app-icon-right>
    </div>
  </div>
  <div class="dropdown-variant8_501-16775">
    <div class="row_501-16776">
      <span class="text-select_501-16777">Select</span>
      <div class="icon-stroke_501-16778">
        <svg
          width="9"
          height="15"
          viewBox="0 0 9 15"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1.91699 12.8301L7.67699 7.05371L1.91699 1.27735"
            stroke="#547996"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="rectangle-318_501-16845">
        <svg
          width="5"
          height="48"
          viewBox="0 0 5 48"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="0.978516"
            y="0.108887"
            width="4"
            height="47.7268"
            fill="#007D00"
            fill-opacity="0.25"
          />
        </svg>
      </div>
    </div>
    <div class="rectangle-320_501-16847">
      <svg
        width="13"
        height="45"
        viewBox="0 0 13 45"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x="0.978516"
          y="0.0537109"
          width="12"
          height="44"
          fill="#C54600"
          fill-opacity="0.3"
        />
      </svg>
    </div>
    <div class="rectangle-321_501-16848">
      <svg
        width="13"
        height="49"
        viewBox="0 0 13 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x="0.916016"
          y="0.0537109"
          width="12"
          height="48"
          fill="#C54600"
          fill-opacity="0.3"
        />
      </svg>
    </div>
  </div>
  <div class="rectangle-319_501-16846">
    <svg
      width="5"
      height="49"
      viewBox="0 0 5 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.912109"
        y="0.0537109"
        width="3.19741"
        height="48"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-320_505-833">
    <svg
      width="4"
      height="45"
      viewBox="0 0 4 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.78125"
        y="0.382324"
        width="3.19741"
        height="44"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="dropdown-variant8_501-16808">
    <div class="row_501-16809">
      <span class="text-select_501-16810">Select</span>
      <div class="icon-stroke_501-16811">
        <svg
          width="9"
          height="15"
          viewBox="0 0 9 15"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1.00098 13.3679L6.76098 7.59156L1.00098 1.81519"
            stroke="#547996"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
  </div>
  <div class="dropdown-variant7_501-16780">
    <div class="row_501-16781">
      <span class="text-select_501-16782">Select</span>
      <div class="icon-stroke_501-16783">
        <svg
          width="15"
          height="9"
          viewBox="0 0 15 9"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2.02051 1.3208L7.79687 7.0808L13.5732 1.3208"
            stroke="#17181A"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
    <div class="div_501-16785">
      <div class="rectangle-1_501-16786">
        <svg
          width="411"
          height="2"
          viewBox="0 0 411 2"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="-3.20312"
            y="0.882324"
            width="418"
            height="1"
            fill="#D9E1E7"
          />
        </svg>
      </div>
    </div>
    <div class="row_501-16787">
      <app-check [property1]="'default'" class="check_501-16788"></app-check>
      <span class="text-selection_501-16789">Selection</span>
    </div>
  </div>
  <div class="rectangle-322_505-860">
    <svg
      width="13"
      height="89"
      viewBox="0 0 13 89"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="0.382324"
        width="12"
        height="88"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-332_505-866">
    <svg
      width="13"
      height="45"
      viewBox="0 0 13 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="0.382324"
        width="12"
        height="44"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-331_505-865">
    <svg
      width="16"
      height="89"
      viewBox="0 0 16 89"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.308594"
        y="0.382324"
        width="15.3663"
        height="88"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="dropdown-variant7_501-16813">
    <div class="row_501-16814">
      <span class="text-select_501-16815">Select</span>
      <div class="icon-stroke_501-16816">
        <svg
          width="15"
          height="9"
          viewBox="0 0 15 9"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2.10645 1.85864L7.88281 7.61864L13.6592 1.85864"
            stroke="#17181A"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
    <div class="div_501-16818">
      <div class="rectangle-1_501-16819">
        <svg
          width="411"
          height="2"
          viewBox="0 0 411 2"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="-3.11719"
            y="0.420166"
            width="418"
            height="1"
            fill="#D9E1E7"
          />
        </svg>
      </div>
    </div>
    <div class="row_501-16820">
      <app-check [property1]="'default'" class="check_501-16821"></app-check>
      <span class="text-selection_501-16822">Selection</span>
    </div>
  </div>
  <div class="dropdown-variant6_501-16823">
    <div class="row_501-16824">
      <span class="text-select_501-16825">Select</span>
      <div class="icon-stroke_501-16826">
        <svg
          width="15"
          height="9"
          viewBox="0 0 15 9"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1.28613 1.1875L7.0625 6.9475L12.8389 1.1875"
            stroke="#17181A"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
    <div class="div_501-16828">
      <div class="rectangle-1_501-16829">
        <svg
          width="411"
          height="2"
          viewBox="0 0 411 2"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="-3.9375"
            y="0.749023"
            width="418"
            height="1"
            fill="#D9E1E7"
          />
        </svg>
      </div>
    </div>
    <div class="row_501-16830">
      <app-check [property1]="'checked'" class="check_501-16831"></app-check>
      <span class="text-selection_501-16832">Selection</span>
    </div>
  </div>
  <div class="dropdown-variant5_501-16833">
    <div class="row_501-16834">
      <span class="text-select_501-16835">Select</span>
      <div class="icon-stroke_501-16836">
        <svg
          width="9"
          height="15"
          viewBox="0 0 9 15"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1.82129 13.3164L7.58129 7.54004L1.82129 1.76368"
            stroke="#547996"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
  </div>
  <div class="rectangle-314_501-16841">
    <svg
      width="411"
      height="5"
      viewBox="0 0 411 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="4.05371"
        width="4"
        height="410"
        transform="rotate(-90 0.796875 4.05371)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-321_505-834">
    <svg
      width="411"
      height="5"
      viewBox="0 0 411 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="4.38232"
        width="4"
        height="409.767"
        transform="rotate(-90 0.796875 4.38232)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-326_505-839">
    <svg
      width="411"
      height="5"
      viewBox="0 0 411 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="4.38232"
        width="4"
        height="409.767"
        transform="rotate(-90 0.796875 4.38232)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-317_501-16844">
    <svg
      width="411"
      height="5"
      viewBox="0 0 411 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="4.05371"
        width="4"
        height="410"
        transform="rotate(-90 0.796875 4.05371)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-322_505-835">
    <svg
      width="411"
      height="5"
      viewBox="0 0 411 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="4.38232"
        width="4"
        height="409.767"
        transform="rotate(-90 0.796875 4.38232)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-327_505-840">
    <svg
      width="411"
      height="5"
      viewBox="0 0 411 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="4.38232"
        width="4"
        height="409.767"
        transform="rotate(-90 0.796875 4.38232)"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-315_501-16842">
    <svg
      width="411"
      height="9"
      viewBox="0 0 411 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="8.05371"
        width="8"
        height="410"
        transform="rotate(-90 0.796875 8.05371)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-323_505-836">
    <svg
      width="411"
      height="9"
      viewBox="0 0 411 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="8.38232"
        width="8"
        height="409.767"
        transform="rotate(-90 0.796875 8.38232)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-328_505-841">
    <svg
      width="411"
      height="9"
      viewBox="0 0 411 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="8.38232"
        width="8"
        height="409.767"
        transform="rotate(-90 0.796875 8.38232)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-316_501-16843">
    <svg
      width="411"
      height="9"
      viewBox="0 0 411 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="8.05371"
        width="8"
        height="410"
        transform="rotate(-90 0.796875 8.05371)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-324_505-837">
    <svg
      width="411"
      height="9"
      viewBox="0 0 411 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="8.38232"
        width="8"
        height="409.767"
        transform="rotate(-90 0.796875 8.38232)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="rectangle-329_505-842">
    <svg
      width="411"
      height="9"
      viewBox="0 0 411 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="8.38232"
        width="8"
        height="409.767"
        transform="rotate(-90 0.796875 8.38232)"
        fill="#C54600"
        fill-opacity="0.3"
      />
    </svg>
  </div>
  <div class="vector_505-843">
    <svg
      width="20"
      height="10"
      viewBox="0 0 20 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.7981 0.556152L11.5178 0.556152L11.5178 8.55615L19.7981 8.55615M11.4397 4.55616L0.0175781 4.55616"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-852">
    <svg
      width="20"
      height="10"
      viewBox="0 0 20 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.7981 1.22754L11.5178 1.22754L11.5178 9.22754L19.7981 9.22754M11.4397 5.22754L0.0175781 5.22754"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-856">
    <svg
      width="14"
      height="20"
      viewBox="0 0 14 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.978515 0.0537104L0.978515 8.33396L12.9785 8.33396L12.9785 0.0537099M6.97852 8.41215L6.97852 19.8342"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-867">
    <svg
      width="14"
      height="20"
      viewBox="0 0 14 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.978515 0.149658L0.978515 8.42991L12.9785 8.42991L12.9785 0.149657M6.97852 8.5081L6.97852 19.9302"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-871">
    <svg
      width="14"
      height="20"
      viewBox="0 0 14 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.796874 0.149658L0.796874 8.42991L12.7969 8.42991L12.7969 0.149657M6.79688 8.5081L6.79688 19.9302"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-848">
    <svg
      width="20"
      height="10"
      viewBox="0 0 20 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.7981 1.38232L11.5178 1.38232L11.5178 9.38232L19.7981 9.38232M11.4397 5.38233L0.0175781 5.38233"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-846">
    <svg
      width="20"
      height="6"
      viewBox="0 0 20 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.7981 0.556152L11.5178 0.556152V4.55615L19.7981 4.55615M11.4397 2.55615L0.0175781 2.55615"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-853">
    <svg
      width="20"
      height="6"
      viewBox="0 0 20 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.7981 1.22754L11.5178 1.22754V5.22754L19.7981 5.22754M11.4397 3.22754L0.0175781 3.22754"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-858">
    <svg
      width="6"
      height="20"
      viewBox="0 0 6 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.978515 0.0537104L0.978515 8.33396L4.97852 8.33396L4.97851 0.0537102M2.97852 8.41215L2.97852 19.8342"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-868">
    <svg
      width="6"
      height="20"
      viewBox="0 0 6 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.978515 0.149658L0.978515 8.42991L4.97852 8.42991L4.97851 0.149657M2.97852 8.5081L2.97852 19.9302"
        stroke="black"
      />
    </svg>
  </div>
  <div class="vector_505-849">
    <svg
      width="20"
      height="6"
      viewBox="0 0 20 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.7981 0.556152L11.5178 0.556152V4.55615L19.7981 4.55615M11.4397 2.55615L0.0175781 2.55615"
        stroke="black"
      />
    </svg>
  </div>
  <span class="text-8px_505-844">8px</span>
  <span class="text-8px_505-854">8px</span>
  <span class="text-12px_505-857">12px</span>
  <span class="text-12px_505-869">12px</span>
  <span class="text-12px_505-872">12px</span>
  <span class="text-8px_505-850">8px</span>
  <span class="text-4px_505-847">4px</span>
  <span class="text-4px_505-855">4px</span>
  <span class="text-4px_505-859">4px</span>
  <span class="text-4px_505-870">4px</span>
  <span class="text-4px_505-851">4px</span>
  <div class="rectangle-319_505-862">
    <svg
      width="5"
      height="89"
      viewBox="0 0 5 89"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.796875"
        y="0.208252"
        width="4"
        height="88.1741"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <div class="rectangle-330_505-864">
    <svg
      width="6"
      height="89"
      viewBox="0 0 6 89"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.674805"
        y="0.208252"
        width="5.1221"
        height="88.1741"
        fill="#007D00"
        fill-opacity="0.25"
      />
    </svg>
  </div>
  <span class="text-font--input-text-color--gray-3_506-8911"
    >Font: Input text Color: gray-3</span
  >
  <span class="text-font--input-text-color--text-black_506-8914"
    >Font: Input text Color: text-black</span
  >
  <span
    class="text-component--icon-down-color--text-black--when-active-_506-8924"
    >Component: icon_down Color: text-black (when active)</span
  >
  <span class="text-component--icon-right-color--gray-3_506-8926"
    >Component: icon_right Color: gray 3</span
  >
  <span
    class="text-font--input-text-color--text-black-component--check_506-8918"
    >Font: Input text Color: text-black Component: check</span
  >
  <span class="text-component--check-color--text-black--when-active-_506-8920"
    >Component: check Color: text-black (when active)</span
  >
  <span class="text-font--input-text-color--text-black_506-8916"
    >Font: Input text Color: text-black</span
  >
  <span class="text-font--input-text-color--text-black_506-8922"
    >Font: Input text Color: text-black</span
  >
  <div class="vector-28_506-8912">
    <svg
      width="25"
      height="2"
      viewBox="0 0 25 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.261719 0.782471L24.6631 0.782473" stroke="black" />
    </svg>
  </div>
  <div class="vector-29_506-8915">
    <svg
      width="25"
      height="2"
      viewBox="0 0 25 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.261719 0.929688L24.6631 0.92969" stroke="black" />
    </svg>
  </div>
  <div class="vector-34_506-8925">
    <svg
      width="26"
      height="2"
      viewBox="0 0 26 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.796875 0.73877L25.1982 0.738772" stroke="black" />
    </svg>
  </div>
  <div class="vector-35_506-8927">
    <svg
      width="26"
      height="2"
      viewBox="0 0 26 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.796875 0.591553L25.1982 0.591555" stroke="black" />
    </svg>
  </div>
  <div class="vector-31_506-8919">
    <svg
      width="25"
      height="2"
      viewBox="0 0 25 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.261719 1.11108L24.6631 1.11109" stroke="black" />
    </svg>
  </div>
  <div class="vector-32_506-8921">
    <svg
      width="25"
      height="2"
      viewBox="0 0 25 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.261719 1.43994L24.6631 1.43994" stroke="black" />
    </svg>
  </div>
  <div class="vector-30_506-8917">
    <svg
      width="25"
      height="2"
      viewBox="0 0 25 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.261719 1.2583L24.6631 1.2583" stroke="black" />
    </svg>
  </div>
  <div class="vector-33_506-8923">
    <svg
      width="25"
      height="2"
      viewBox="0 0 25 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.261719 1.43018L24.6631 1.43018" stroke="black" />
    </svg>
  </div>
  <div class="dropdown-exclusion_649-5299">
    <div class="property-1-entered_649-5298">
      <div class="row_649-5290">
        <span class="text-reasoning_649-5291">Reasoning</span>
        <app-icon-right class="icon-right_649-5292"></app-icon-right>
      </div>
    </div>
    <div class="property-1-default_649-5296">
      <div class="row_649-5286">
        <span class="text-select_649-5287">Select</span>
        <app-icon-right class="icon-right_649-5288"></app-icon-right>
      </div>
    </div>
    <div class="property-1-active_649-5297">
      <div class="row_649-5226">
        <span class="text-select_649-5227">Select</span>
        <div class="icon-stroke_649-5228">
          <svg
            width="15"
            height="9"
            viewBox="0 0 15 9"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.5332 1.5459L7.30957 7.3059L13.0859 1.5459"
              stroke="#17181A"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
      <div class="div_649-5230">
        <div class="rectangle-1_649-5231">
          <svg
            width="409"
            height="2"
            viewBox="0 0 409 2"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="-3.69043"
              y="0.107422"
              width="416"
              height="1"
              fill="#547996"
            />
          </svg>
        </div>
      </div>
      <div class="row_649-5232">
        <app-check [property1]="'default'" class="check_649-5233"></app-check>
        <span class="text-acute-inpatient-and-ed-visit_649-5234"
          >Acute inpatient and ED visit</span
        >
      </div>
      <div class="row_649-5235">
        <app-check [property1]="'default'" class="check_649-5236"></app-check>
        <span class="text-end-stage-renal-disease_649-5237"
          >End-stage renal disease</span
        >
      </div>
      <div class="row_649-5238">
        <app-check [property1]="'default'" class="check_649-5239"></app-check>
        <span
          class="text-frailty--member-81--years-as-of-12-31-of-the-my_649-5240"
          >Frailty: Member 81+ years as of 12/31 of the MY</span
        >
      </div>
      <div class="row_649-5241">
        <app-check [property1]="'default'" class="check_649-5242"></app-check>
        <span class="text-lidocaine-and-epinephrine-given-to-patient_649-5243"
          >Lidocaine and Epinephrine given to patient</span
        >
      </div>
      <div class="row_649-5244">
        <app-check [property1]="'default'" class="check_649-5245"></app-check>
        <span
          class="text-medicare-member-in-an-institutional-snp--i-snp-_649-5246"
          >Medicare member in an Institutional SNP (I-SNP)</span
        >
      </div>
      <div class="row_649-5247">
        <app-check [property1]="'default'" class="check_649-5248"></app-check>
        <span class="text-medicare-member-living-in-long-term-care_649-5249"
          >Medicare member living in long-term care</span
        >
      </div>
      <div class="row_649-5250">
        <app-check [property1]="'default'" class="check_649-5251"></app-check>
        <span class="text-member-66-80-years-as-of-12-31-of-the-my_649-5252"
          >Member 66-80 years as of 12/31 of the MY</span
        >
      </div>
      <div class="row_649-5253">
        <app-check [property1]="'default'" class="check_649-5254"></app-check>
        <span class="text-member-died-during-the-my_649-5255"
          >Member died during the MY</span
        >
      </div>
      <div class="row_649-5256">
        <app-check [property1]="'default'" class="check_649-5257"></app-check>
        <span class="text-member-in-hospice-anytime-during-the-my_649-5258"
          >Member in hospice anytime during the MY</span
        >
      </div>
      <div class="row_649-5259">
        <app-check [property1]="'default'" class="check_649-5260"></app-check>
        <span class="text-non-acute-inpatient-admission_649-5261"
          >Non-acute inpatient admission</span
        >
      </div>
      <div class="row_649-5262">
        <app-check [property1]="'default'" class="check_649-5263"></app-check>
        <span class="text-palliative-care_649-5264">Palliative Care</span>
      </div>
      <div class="row_649-5265">
        <app-check [property1]="'default'" class="check_649-5266"></app-check>
        <span class="text-pregnancy_649-5267">Pregnancy</span>
      </div>
      <div class="row_649-5268">
        <app-check [property1]="'default'" class="check_649-5269"></app-check>
        <span class="text-other_649-5270">Other</span>
      </div>
    </div>
  </div>
  <div class="dropdown-none_649-5451">
    <div class="property-1-entered_649-5450">
      <div class="row_649-5367">
        <span class="text-reasoning_649-5368">Reasoning</span>
        <app-icon-right class="icon-right_649-5369"></app-icon-right>
      </div>
    </div>
    <div class="property-1-default_649-5448">
      <div class="row_649-5371">
        <span class="text-select_649-5372">Select</span>
        <app-icon-right class="icon-right_649-5373"></app-icon-right>
      </div>
    </div>
    <div class="property-1-clicked_649-5449">
      <div class="row_649-5311">
        <span class="text-select_649-5312">Select</span>
        <div class="icon-stroke_649-5313">
          <svg
            width="15"
            height="9"
            viewBox="0 0 15 9"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.65039 1.76953L7.42675 7.52953L13.2031 1.76953"
              stroke="#17181A"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
      <div class="div_649-5315">
        <div class="rectangle-1_649-5316">
          <svg
            width="409"
            height="2"
            viewBox="0 0 409 2"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="-3.57324"
              y="0.331055"
              width="416"
              height="1"
              fill="#547996"
            />
          </svg>
        </div>
      </div>
      <div class="row_649-5317">
        <app-check [property1]="'default'" class="check_649-5318"></app-check>
        <span class="text-blood-pressure-values-do-not-match_649-5319"
          >Blood pressure values do not match</span
        >
      </div>
      <div class="row_649-5320">
        <app-check [property1]="'default'" class="check_649-5321"></app-check>
        <span class="text-bmi-not-found_649-5322">BMI not found</span>
      </div>
      <div class="row_649-5323">
        <app-check [property1]="'default'" class="check_649-5324"></app-check>
        <span class="text-chart-summary-not-found_649-5325"
          >Chart summary not found</span
        >
      </div>
      <div class="row_649-5326">
        <app-check [property1]="'default'" class="check_649-5327"></app-check>
        <span class="text-dates-do-not-match_649-5328">Dates do not match</span>
      </div>
      <div class="row_649-5329">
        <app-check [property1]="'default'" class="check_649-5330"></app-check>
        <span class="text-documentation-does-not-match-entry_649-5331"
          >Documentation does not match entry</span
        >
      </div>
      <div class="row_649-5332">
        <app-check [property1]="'default'" class="check_649-5333"></app-check>
        <span class="text-insufficient-documentation_649-5334"
          >Insufficient documentation</span
        >
      </div>
      <div class="row_649-5335">
        <app-check [property1]="'default'" class="check_649-5336"></app-check>
        <span class="text-insufficient-patient-identifiers_649-5337"
          >Insufficient patient identifiers</span
        >
      </div>
      <div class="row_649-5338">
        <app-check [property1]="'default'" class="check_649-5339"></app-check>
        <span class="text-lab-value-not-entered_649-5340"
          >Lab value not entered</span
        >
      </div>
      <div class="row_649-5341">
        <app-check [property1]="'default'" class="check_649-5342"></app-check>
        <span class="text-no-documentation_649-5343">No documentation</span>
      </div>
      <div class="row_649-5344">
        <app-check [property1]="'default'" class="check_649-5345"></app-check>
        <span class="text-out-of-timeframe-for-measurement-period_649-5346"
          >Out of timeframe for measurement period</span
        >
      </div>
      <div class="row_649-5347">
        <app-check [property1]="'default'" class="check_649-5348"></app-check>
        <span class="text-patient-name-dob-does-not-match-records_649-5349"
          >Patient name/DOB does not match records</span
        >
      </div>
      <div class="row_649-5350">
        <app-check [property1]="'default'" class="check_649-5351"></app-check>
        <span class="text-other_649-5352">Other</span>
      </div>
    </div>
  </div>
</div>
