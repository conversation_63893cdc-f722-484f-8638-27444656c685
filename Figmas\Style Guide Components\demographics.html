<div data-layer="Demographics V2" class="DemographicsV2" style="width: 1831px; height: 932px; position: relative; background: white; overflow: hidden">
  <div data-layer="Spacing:" class="Spacing" style="width: 83px; height: 15.69px; left: 45.26px; top: 407.41px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Spacing:</div>
  <div data-layer="Styling:" class="Styling" style="width: 83px; height: 15.69px; left: 45.26px; top: 668.61px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Styling:</div>
  <div data-layer="Behavior: Scrolls with the page. Clicking the back button goes back to the dashboard." class="BehaviorScrollsWithThePageClickingTheBackButtonGoesBackToTheDashboard" style="width: 1039.94px; height: 16px; left: 44.43px; top: 170.01px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Behavior: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Scrolls with the page. Clicking the back button goes back to the dashboard.</span></div>
  <div data-layer="Rectangle 287" class="Rectangle287" style="width: 1478.31px; height: 123.01px; left: 140.90px; top: 218.31px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Rectangle 290" class="Rectangle290" style="width: 1478.31px; height: 123.01px; left: 140.90px; top: 439.87px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Rectangle 291" class="Rectangle291" style="width: 1478.31px; height: 123.01px; left: 146.90px; top: 704.69px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Demographics-V2" class="DemographicsV2" style="width: 1380px; left: 178.54px; top: 239.60px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: inline-flex">
    <div data-layer="Table" class="Table" style="align-self: stretch; padding: 20px; background: white; border-radius: 8px; outline: 1px var(--gray-1, #F1F5F7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
      <div data-layer="Frame 925" class="Frame925" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 48px; display: inline-flex">
        <div data-layer="Frame 927" class="Frame927" style="flex-direction: column; justify-content: flex-end; align-items: flex-start; gap: 48px; display: inline-flex">
          <div data-layer="Frame 928" class="Frame928" style="justify-content: flex-start; align-items: flex-start; gap: 36px; display: inline-flex">
            <div data-layer="Back-to-home" class="BackToHome" style="justify-content: flex-start; align-items: center; gap: 8px; display: flex">
              <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
                <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15.8708 10.5972H4.2041M4.2041 10.5972L9.2041 15.5972M4.2041 10.5972L9.2041 5.59717" stroke="var(--link, #0071BC)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div data-layer="Text" class="Text" style="color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Back</div>
            </div>
            <div data-layer="member-id" class="MemberId" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Controlling Blood Pressure (CBP)" class="ControllingBloodPressureCbp" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Controlling Blood Pressure (CBP)</div>
              <div data-layer="Measure" class="Measure" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Measure</div>
            </div>
          </div>
        </div>
        <div data-layer="Frame 922" class="Frame922" style="justify-content: flex-start; align-items: center; gap: 60px; display: flex">
          <div data-layer="Frame 924" class="Frame924" style="justify-content: flex-start; align-items: center; gap: 40px; display: flex">
            <div data-layer="Frame 916" class="Frame916" style="width: 68px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="55820474" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">55820474</div>
              <div data-layer="Member ID" class="MemberId" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member ID</div>
            </div>
            <div data-layer="Frame 920" class="Frame920" style="width: 57px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="John Dey" class="JohnDey" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">John Dey</div>
              <div data-layer="Member" class="Member" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member</div>
            </div>
            <div data-layer="Frame 917" class="Frame917" style="width: 69px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="01/05/1972" class="051972" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">01/05/1972</div>
              <div data-layer="DOB" class="Dob" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DOB</div>
            </div>
            <div data-layer="Frame 918" class="Frame918" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="M" class="M" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">M</div>
              <div data-layer="Gender" class="Gender" style="color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Gender</div>
            </div>
            <div data-layer="Frame 919" class="Frame919" style="width: 51px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="MAHMO" class="Mahmo" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">MAHMO</div>
              <div data-layer="LOB" class="Lob" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">LOB</div>
            </div>
          </div>
          <div data-layer="Frame 923" class="Frame923" style="justify-content: flex-start; align-items: center; gap: 24px; display: flex">
            <div data-layer="Frame 921" class="Frame921" style="width: 115px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Nicolas Dejong PA" class="NicolasDejongPa" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Nicolas Dejong PA</div>
              <div data-layer="Provider" class="Provider" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Provider</div>
            </div>
            <div data-layer="Frame 920" class="Frame920" style="width: 68px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="882716229" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">882716229</div>
              <div data-layer="NPI" class="Npi" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">NPI</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Demographics-V2" class="DemographicsV2" style="width: 1380px; left: 178.54px; top: 461.16px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: inline-flex">
    <div data-layer="Table" class="Table" style="align-self: stretch; padding: 20px; background: white; border-radius: 8px; outline: 1px var(--gray-1, #F1F5F7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
      <div data-layer="Frame 925" class="Frame925" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 48px; display: inline-flex">
        <div data-layer="Frame 927" class="Frame927" style="flex-direction: column; justify-content: flex-end; align-items: flex-start; gap: 48px; display: inline-flex">
          <div data-layer="Frame 928" class="Frame928" style="justify-content: flex-start; align-items: flex-start; gap: 36px; display: inline-flex">
            <div data-layer="Back-to-home" class="BackToHome" style="justify-content: flex-start; align-items: center; gap: 8px; display: flex">
              <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
                <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15.8698 10.1577H4.20312M4.20312 10.1577L9.20312 15.1577M4.20312 10.1577L9.20313 5.15771" stroke="var(--link, #0071BC)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div data-layer="Text" class="Text" style="color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Back</div>
            </div>
            <div data-layer="member-id" class="MemberId" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Controlling Blood Pressure (CBP)" class="ControllingBloodPressureCbp" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Controlling Blood Pressure (CBP)</div>
              <div data-layer="Measure" class="Measure" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Measure</div>
            </div>
          </div>
        </div>
        <div data-layer="Frame 922" class="Frame922" style="justify-content: flex-start; align-items: center; gap: 60px; display: flex">
          <div data-layer="Frame 924" class="Frame924" style="justify-content: flex-start; align-items: center; gap: 40px; display: flex">
            <div data-layer="Frame 916" class="Frame916" style="width: 68px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="55820474" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">55820474</div>
              <div data-layer="Member ID" class="MemberId" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member ID</div>
            </div>
            <div data-layer="Frame 920" class="Frame920" style="width: 57px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="John Dey" class="JohnDey" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">John Dey</div>
              <div data-layer="Member" class="Member" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member</div>
            </div>
            <div data-layer="Frame 917" class="Frame917" style="width: 69px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="01/05/1972" class="051972" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">01/05/1972</div>
              <div data-layer="DOB" class="Dob" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DOB</div>
            </div>
            <div data-layer="Frame 918" class="Frame918" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="M" class="M" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">M</div>
              <div data-layer="Gender" class="Gender" style="color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Gender</div>
            </div>
            <div data-layer="Frame 919" class="Frame919" style="width: 51px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="MAHMO" class="Mahmo" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">MAHMO</div>
              <div data-layer="LOB" class="Lob" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">LOB</div>
            </div>
          </div>
          <div data-layer="Frame 923" class="Frame923" style="justify-content: flex-start; align-items: center; gap: 24px; display: flex">
            <div data-layer="Frame 921" class="Frame921" style="width: 115px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Nicolas Dejong PA" class="NicolasDejongPa" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Nicolas Dejong PA</div>
              <div data-layer="Provider" class="Provider" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Provider</div>
            </div>
            <div data-layer="Frame 920" class="Frame920" style="width: 68px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="882716229" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">882716229</div>
              <div data-layer="NPI" class="Npi" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">NPI</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Demographics-V2" class="DemographicsV2" style="width: 1380px; left: 184.54px; top: 725.98px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px; display: inline-flex">
    <div data-layer="Table" class="Table" style="align-self: stretch; padding: 20px; background: white; border-radius: 8px; outline: 1px var(--gray-1, #F1F5F7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
      <div data-layer="Frame 925" class="Frame925" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 48px; display: inline-flex">
        <div data-layer="Frame 927" class="Frame927" style="flex-direction: column; justify-content: flex-end; align-items: flex-start; gap: 48px; display: inline-flex">
          <div data-layer="Frame 928" class="Frame928" style="justify-content: flex-start; align-items: flex-start; gap: 36px; display: inline-flex">
            <div data-layer="Back-to-home" class="BackToHome" style="justify-content: flex-start; align-items: center; gap: 8px; display: flex">
              <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
                <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15.8708 10.9761H4.2041M4.2041 10.9761L9.2041 15.9761M4.2041 10.9761L9.2041 5.97607" stroke="var(--link, #0071BC)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div data-layer="Text" class="Text" style="color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Back</div>
            </div>
            <div data-layer="member-id" class="MemberId" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Controlling Blood Pressure (CBP)" class="ControllingBloodPressureCbp" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Controlling Blood Pressure (CBP)</div>
              <div data-layer="Measure" class="Measure" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Measure</div>
            </div>
          </div>
        </div>
        <div data-layer="Frame 922" class="Frame922" style="justify-content: flex-start; align-items: center; gap: 60px; display: flex">
          <div data-layer="Frame 924" class="Frame924" style="justify-content: flex-start; align-items: center; gap: 40px; display: flex">
            <div data-layer="Frame 916" class="Frame916" style="width: 68px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="55820474" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">55820474</div>
              <div data-layer="Member ID" class="MemberId" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member ID</div>
            </div>
            <div data-layer="Frame 920" class="Frame920" style="width: 57px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="John Dey" class="JohnDey" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">John Dey</div>
              <div data-layer="Member" class="Member" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Member</div>
            </div>
            <div data-layer="Frame 917" class="Frame917" style="width: 69px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="01/05/1972" class="051972" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">01/05/1972</div>
              <div data-layer="DOB" class="Dob" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DOB</div>
            </div>
            <div data-layer="Frame 918" class="Frame918" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="M" class="M" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">M</div>
              <div data-layer="Gender" class="Gender" style="color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Gender</div>
            </div>
            <div data-layer="Frame 919" class="Frame919" style="width: 51px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="MAHMO" class="Mahmo" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">MAHMO</div>
              <div data-layer="LOB" class="Lob" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">LOB</div>
            </div>
          </div>
          <div data-layer="Frame 923" class="Frame923" style="justify-content: flex-start; align-items: center; gap: 24px; display: flex">
            <div data-layer="Frame 921" class="Frame921" style="width: 115px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Nicolas Dejong PA" class="NicolasDejongPa" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Nicolas Dejong PA</div>
              <div data-layer="Provider" class="Provider" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Provider</div>
            </div>
            <div data-layer="Frame 920" class="Frame920" style="width: 68px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="882716229" style="align-self: stretch; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">882716229</div>
              <div data-layer="NPI" class="Npi" style="align-self: stretch; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">NPI</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Usage: Demographics header that is used on all chart review pages. The demographics information populate based on the member chart." class="UsageDemographicsHeaderThatIsUsedOnAllChartReviewPagesTheDemographicsInformationPopulateBasedOnTheMemberChart" style="width: 1478.31px; left: 45.26px; top: 130.22px; position: absolute"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Usage: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; word-wrap: break-word">Demographics header that is used on all chart review pages. The demographics information populate based on the member chart. </span></div>
  <div data-layer="Demographics V2" class="DemographicsV2" style="left: 45.26px; top: 62.80px; position: absolute; color: black; font-size: 36px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Demographics V2</div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 260.05px; top: 554.66px; position: absolute">
    <svg width="38" height="21" viewBox="0 0 38 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.549804 0.663818L0.549804 8.94407L36.5498 8.94407L36.5498 0.663816M18.5498 9.02226L18.5498 20.4443" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 638.29px; top: 554.66px; position: absolute">
    <svg width="50" height="21" viewBox="0 0 50 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.786132 0.663818L0.786132 8.94407L48.7861 8.94407L48.7861 0.663816M24.7862 9.02226L24.7862 20.4443" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 752.54px; top: 554.66px; position: absolute">
    <svg width="42" height="21" viewBox="0 0 42 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.03613 0.663818L1.03613 8.94407L41.0361 8.94407L41.0361 0.663816M21.0362 9.02226L21.0362 20.4443" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 861.54px; top: 554.66px; position: absolute">
    <svg width="42" height="21" viewBox="0 0 42 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.03613 0.663818L1.03613 8.94407L41.0361 8.94407L41.0361 0.663816M21.0362 9.02226L21.0362 20.4443" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 940.54px; top: 554.66px; position: absolute">
    <svg width="42" height="21" viewBox="0 0 42 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.03613 0.663818L1.03613 8.94407L41.0361 8.94407L41.0361 0.663816M21.0362 9.02226L21.0362 20.4443" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1047.54px; top: 554.66px; position: absolute">
    <svg width="42" height="21" viewBox="0 0 42 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.03711 0.663818L1.03711 8.94407L41.0371 8.94407L41.0371 0.663816M21.0371 9.02226L21.0371 20.4443" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1140.04px; top: 554.66px; position: absolute">
    <svg width="57" height="21" viewBox="0 0 57 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.537109 0.663818L0.537109 8.94407L55.5371 8.94407L55.5371 0.663815M28.0371 9.02226L28.0371 20.4443" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1311.35px; top: 554.66px; position: absolute">
    <svg width="26" height="21" viewBox="0 0 26 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.850585 0.663818L0.850585 8.94407L24.8506 8.94407L24.8506 0.663817M12.8506 9.02226L12.8506 20.4443" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 150.60px; top: 461.85px; position: absolute">
    <svg width="25" height="90" viewBox="0 0 25 90" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M24.5586 0.84668H14.3203V88.8467H24.5586M14.2236 44.8467H0.100586" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1563.64px; top: 481.08px; position: absolute">
    <svg width="25" height="50" viewBox="0 0 25 50" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.138672 49.3955L10.377 49.3955L10.377 1.07715L0.138676 1.07714M10.4736 25.2363L24.5967 25.2363" stroke="black"/>
    </svg>
  </div>
  <div data-layer="88px" class="Px" style="left: 112.90px; top: 499.37px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">88px</div>
  <div data-layer="items aligned top left" class="ItemsAlignedTopLeft" style="left: 1592.50px; top: 505.40px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">items aligned top left</div>
  <div data-layer="36px" class="Px" style="left: 262.05px; top: 582.30px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">36px</div>
  <div data-layer="Font: H1 Color: text-black" class="FontH1ColorTextBlack" style="left: 296.31px; top: 692.69px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: H1<br/>Color: text-black</div>
  <div data-layer="Font: H5 Color: text-black" class="FontH5ColorTextBlack" style="left: 675.90px; top: 692.69px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: H5<br/>Color: text-black</div>
  <div data-layer="48px" class="Px" style="left: 646.29px; top: 582.30px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">48px</div>
  <div data-layer="40px" class="Px" style="left: 756.54px; top: 582.30px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">40px</div>
  <div data-layer="40px" class="Px" style="left: 865.54px; top: 582.30px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">40px</div>
  <div data-layer="40px" class="Px" style="left: 944.54px; top: 582.30px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">40px</div>
  <div data-layer="40px" class="Px" style="left: 1051.54px; top: 582.30px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">40px</div>
  <div data-layer="55px" class="Px" style="left: 1152.04px; top: 582.30px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">55px</div>
  <div data-layer="24px" class="Px" style="left: 1307.85px; top: 582.30px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">24px</div>
  <div data-layer="corner-radius: 8 border-style: solid  border-color: gray-1" class="CornerRadius8BorderStyleSolidBorderColorGray1" style="left: 1572.86px; top: 748.09px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">corner-radius: 8<br/>border-style: solid  border-color: gray-1</div>
  <div data-svg-wrapper data-layer="Vector 1" class="Vector1" style="left: 339.68px; top: 725.51px; position: absolute">
    <svg width="2" height="29" viewBox="0 0 2 29" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.18164 28.137V0.508057" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 2" class="Vector2" style="left: 722.01px; top: 721.51px; position: absolute">
    <svg width="1" height="29" viewBox="0 0 1 29" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.509766 28.1367V0.507812" stroke="black"/>
    </svg>
  </div>
  <div data-layer="Font: H6 Color: gray-3" class="FontH6ColorGray3" style="left: 315.18px; top: 844.87px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: H6<br/>Color: gray-3</div>
  <div data-layer="Font: Button text Color: link" class="FontButtonTextColorLink" style="left: 197.31px; top: 844.87px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: Button text<br/>Color: link</div>
  <div data-layer="component: icon_arrow_left" class="ComponentIconArrowLeft" style="left: 45.26px; top: 752.09px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">component:<br/>icon_arrow_left</div>
  <div data-svg-wrapper data-layer="Vector 3" class="Vector3" style="left: 339.68px; top: 802.09px; position: absolute">
    <svg width="2" height="28" viewBox="0 0 2 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.18164 27.7239V0.0949707" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 4" class="Vector4" style="left: 232.90px; top: 778.09px; position: absolute">
    <svg width="2" height="52" viewBox="0 0 2 52" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.39649 51.7239L1.39648 0.0949707" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 20" class="Vector20" style="left: 151.16px; top: 764.09px; position: absolute">
    <svg width="47" height="2" viewBox="0 0 47 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.658203 1.09497L46.8105 1.09497" stroke="black"/>
    </svg>
  </div>
  <div data-layer="Rectangle 249" class="Rectangle249" style="width: 20px; height: 1380px; left: 178.54px; top: 481.16px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 250" class="Rectangle250" style="width: 20px; height: 1380px; left: 178.54px; top: 549.16px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 252" class="Rectangle252" style="width: 20px; height: 88px; left: 178.54px; top: 461.16px; position: absolute; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 253" class="Rectangle253" style="width: 36px; height: 88px; left: 258.54px; top: 461.16px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 254" class="Rectangle254" style="width: 48px; height: 88px; left: 636.54px; top: 461.16px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 255" class="Rectangle255" style="width: 40px; height: 88px; left: 752.54px; top: 461.16px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 256" class="Rectangle256" style="width: 40px; height: 88px; left: 850.04px; top: 461.16px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 257" class="Rectangle257" style="width: 40px; height: 88px; left: 958.54px; top: 461.16px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 292" class="Rectangle292" style="width: 40px; height: 88px; left: 1045.54px; top: 461.16px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 258" class="Rectangle258" style="width: 55px; height: 88px; left: 1140.04px; top: 461.16px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 259" class="Rectangle259" style="width: 24px; height: 88px; left: 1311.35px; top: 461.16px; position: absolute; background: rgba(197.12, 69.62, 0, 0.30)"></div>
</div>