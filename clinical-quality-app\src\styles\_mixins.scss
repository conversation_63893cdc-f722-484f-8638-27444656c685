// Import variables
@use 'variables' as variables;

// Flexbox Mixins
@mixin flex-row {
  display: flex;
  flex-direction: row;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

@mixin flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

// Layout Mixins
@mixin container {
  width: 100%;
  padding-left: variables.$spacing-xxxl;
  padding-right: variables.$spacing-xxxl;
}

@mixin card {
  background: variables.$white;
  border-radius: variables.$border-radius-lg;
  outline: variables.$border-width-default variables.$gray-1 solid;
  outline-offset: -(variables.$border-width-default);
  padding: variables.$spacing-xl;
  margin-bottom: variables.$spacing-xl;
}

// Button Mixins - DEPRECATED: Use ButtonComponent instead

// Form Element Mixins
@mixin input-field {
  padding: variables.$spacing-md;
  border-radius: variables.$border-radius-xl;
  outline: variables.$border-width-default variables.$gray-2 solid;
  outline-offset: -(variables.$border-width-default);
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  width: 100%;

  &:focus {
    outline-color: variables.$gray-3;
  }
}

@mixin textarea-field {
  padding: variables.$spacing-md;
  border-radius: variables.$border-radius-xl;
  outline: variables.$border-width-default variables.$gray-2 solid;
  outline-offset: -(variables.$border-width-default);
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  width: 100%;
  min-height: 88px;

  &:focus {
    outline-color: variables.$gray-3;
  }
}

@mixin checkbox {
  width: 16px;
  height: 16px;
  border-radius: 5px; // Figma specifies 5px border radius
  border: 1px solid variables.$gray-2;
  background-color: variables.$white;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;

  &:checked {
    background-color: variables.$text-black;
    border-color: variables.$text-black;

    &::after {
      content: '';
      position: absolute;
      left: 4px;
      top: 4px;
      width: 8.33px;
      height: 7.5px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8.33' height='7.5' viewBox='0 0 8.33 7.5'%3E%3Cpath d='M1 3.75L3.5 6.25L7.33 1.25' stroke='white' stroke-width='1.5' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
    }
  }

  &:hover:not(:disabled) {
    border-color: variables.$gray-3;
  }

  &:focus {
    outline: 2px solid variables.$primary-blue;
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// Table Mixins
@mixin table-header {
  padding: variables.$spacing-md;
  border-bottom: variables.$border-width-default variables.$gray-1 solid;
  font-weight: 500;
  color: variables.$text-black;
  height: 68px;
  display: flex;
  align-items: center;
}

@mixin table-cell {
  padding: variables.$spacing-md;
  font-weight: 300;
  height: 68px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}



// Icon Mixins
@mixin icon-container {
  width: 20px;
  height: 20px;
  position: relative;
}

// Status Indicators - DEPRECATED: Use ButtonComponent with appropriate variants instead

// Responsive Mixins
@mixin for-phone-only {
  @media (max-width: 599px) { @content; }
}

@mixin for-tablet-portrait-up {
  @media (min-width: 600px) { @content; }
}

@mixin for-tablet-landscape-up {
  @media (min-width: 900px) { @content; }
}

@mixin for-desktop-up {
  @media (min-width: 1200px) { @content; }
}

@mixin for-big-desktop-up {
  @media (min-width: 1800px) { @content; }
}