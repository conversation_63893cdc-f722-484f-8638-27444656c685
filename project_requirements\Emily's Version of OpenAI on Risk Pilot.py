# Databricks notebook source
# MAGIC %md
# MAGIC # Risk-Adjustment Experiment
# MAGIC
# MAGIC This notebook contains the code for running an LLM experiment using OpenAI models.
# MAGIC
# MAGIC The focus of this notebook is for the initial risk-adjustment pilot.

# COMMAND ----------

# MAGIC %md 
# MAGIC # Set up
# MAGIC
# MAGIC All the code for setting up the experiment and any processing.

# COMMAND ----------

import asyncio
import glob
import json
import os
import random
import time
from typing import List, Optional, TypedDict

import mlflow
import nest_asyncio
import numpy as np
import pandas as pd
import pyspark.sql.functions as f
from openai import (
    APITimeoutError,
    AsyncOpenAI,
    BadRequestError,
    ContentFilterFinishReasonError,
    InternalServerError,
    OpenAI,
    RateLimitError,
)
from pydantic import BaseModel, ValidationError
from tenacity import (
    retry,
    retry_if_exception_type,
    retry_if_not_exception_type,
    stop_after_attempt,
    wait_exponential,
    wait_random_exponential,
)
from tqdm import tqdm

random.seed(23)
nest_asyncio.apply()
pd.set_option("display.max_colwidth", None)

# MLflow settings.
mlflow.tracing.disable() # Automatic tracing doesn't work with OpenAI async client.
# mlflow.set_experiment("/Workspace/Users/<USER>/Risk/risk_pilot_experiments")


def group_pages_with_overlap(df: pd.DataFrame, group_size: int = 3, overlap: int = 2) -> pd.DataFrame:
    """Group pages with overlap, ensuring pages from different files are not grouped together."""
    assert group_size > overlap, "Group size must be greater than overlap."

    grouped_data = []
    for filename in df["filename"].unique():
        file_df = df[df["filename"] == filename]
        if len(file_df) > overlap:
            for i in range(0, len(file_df), group_size - overlap):
                end_index = min(i + group_size, len(file_df))
                group = file_df.iloc[i:end_index]
                if len(group["page"].tolist()) > overlap:
                    grouped_data.append(
                        {
                            "filename": group["filename"].iloc[0],
                            "page": group["page"].tolist(),
                            "text": " ".join(group["text"].tolist()),
                        }
                    )
        else:  # If the pages are less than the overlap, just return `file_df` appropriately formated.
            grouped_data.append(
                {
                    "filename": file_df["filename"].iloc[0],
                    "page": file_df["page"].tolist(),
                    "text": " ".join(file_df["text"].tolist()),
                }
            )
    return pd.DataFrame(grouped_data)


class RiskDict(TypedDict):
    Justification: str
    MedicalEntity: str
    DOS: str
    Assertion: str
    DXcode: str


class RiskOutput(BaseModel):
    output: Optional[List[RiskDict]]


class RiskDictMEAT(TypedDict):
    Justification: str
    MedicalEntity: str
    DOS: str
    Assertion: str
    DXcode: str
    Monitoring: str
    Evaluating: str
    Assessing: str
    Treating: str


class RiskOutputMEAT(BaseModel):
    output: Optional[List[RiskDictMEAT]]


class DiscriminatorOutput(BaseModel):
    RiskValid: bool


# COMMAND ----------

text_table = "dbc_clinical_code_extraction.default.chart_extracted_text_risk_pilot"

df = spark.sql(f""" SELECT DISTINCT filename, page, text 
              FROM {text_table}
              WHERE text NOT LIKE '%ERROR%'
              ORDER BY filename, page""")
print(f"Charts: {df.select('filename').distinct().count():,}")
print(f"Chart-pages before filtering: {df.count():,}")

df = df.filter(
    ~(f.lower(df.text).contains("cover sheet")
    | f.lower(df.text).contains("fax received")
    | f.lower(df.text).contains("fax sheet")
    | f.lower(df.text).contains("this fax was distributed"))
).toPandas()
print(f"Chart-pages after filtering fax sheets: {df.shape[0]:,}")

df = df[(df["text"].str.split().str.len() > 50) & (df["text"].str.split().str.len() < 1000)].drop_duplicates(subset=["text"])
print(f"Chart-pages after filtering short and long pages: {df.shape[0]:,}")

df.head()

# COMMAND ----------

# How to get your Databricks token: https://docs.databricks.com/en/dev-tools/auth/pat.html
DATABRICKS_TOKEN = (
    dbutils.notebook.entry_point.getDbutils().notebook().getContext().apiToken().get()
)

client = AsyncOpenAI(
# client = OpenAI(
    api_key=DATABRICKS_TOKEN,
    base_url="https://adb-640321604414221.1.azuredatabricks.net/serving-endpoints",
)             

model_reasoning = "azure-o3-mini"
model = "azure-gpt-4o-max-2024-08-06"
model_mini="azure-gpt-4o-mini-max-2024-07-18"

# COMMAND ----------

# MAGIC %md
# MAGIC # Experiment
# MAGIC
# MAGIC Core code for the LLM experiment.

# COMMAND ----------

# # Small subset for testing.
# noHCC_charts = ['1CH0T83WH41_1679569636_240430_236560157800_WAVE1.pdf',
#  '1CH0T83WH41_1679569636_240430_236560157800_WAVE1.pdf',
#  '1CH0T83WH41_1679569636_240430_236560157800_WAVE1.pdf',
#  '1HM9H59GM27_1194711945_240430_240111366200_WAVE1.pdf',]
# df = df.query("filename.isin(@noHCC_charts)")

# COMMAND ----------

# Only run on the no HCC charts.
noHCC_charts = ['1CH0T83WH41_1679569636_240430_236560157800_WAVE1.pdf',
 '1CH0T83WH41_1679569636_240430_236560157800_WAVE1.pdf',
 '1CH0T83WH41_1679569636_240430_236560157800_WAVE1.pdf',
 '1HM9H59GM27_1194711945_240430_240111366200_WAVE1.pdf',
 '1HM9H59GM27_1194711945_240430_240111366200_WAVE1.pdf',
 '1HM9H59GM27_1194711945_240430_240111366200_WAVE1.pdf',
 '1J78X87HR39_1417342668_240430_235919830500_WAVE1.pdf',
 '1J78X87HR39_1417342668_240430_235919830500_WAVE1.pdf',
 '1JH0Y04YX23_1194711945_240430_236559774200_WAVE1.pdf',
 '1JH0Y04YX23_1194711945_240430_236559774200_WAVE1.pdf',
 '2A30J14GX39_1750382552_240430_234634730600_WAVE1.pdf',
 '2FF1KF6VQ25_1992071211_240430_235555689800_WAVE1.pdf',
 '2FF1KF6VQ25_1992071211_240430_235555689800_WAVE1.pdf',
 '2HE6FV9RT75_1417117979_240430_234535007300_WAVE1.pdf',
 '2HE6FV9RT75_1417117979_240430_234535007300_WAVE1.pdf',
 '2HW5YU2RA56_1063440840_240430_240119695100_WAVE1.pdf',
 '2MH0FW8JY14_1689935066_240430_234046163600_WAVE1.pdf',
 '2MH0FW8JY14_1689935066_240430_234046163600_WAVE1.pdf',
 '2MH0FW8JY14_1881882264_240430_233039133200_WAVE1.pdf',
 '2MH0FW8JY14_1881882264_240430_233039133200_WAVE1.pdf',
 '2MH0FW8JY14_1881882264_240430_233039133200_WAVE1.pdf',
 '2WK0C49FV23_1497742621_240430_234381177700_WAVE1.pdf',
 '2WK0C49FV23_1497742621_240430_234381177700_WAVE1.pdf',
 '2WK0C49FV23_1497742621_240430_234381177700_WAVE1.pdf',
 '2WK0C49FV23_1497742621_240430_234381177700_WAVE1.pdf',
 '2WK0C49FV23_1497742621_240430_234381177700_WAVE1.pdf',
 '2WK0C49FV23_1497742621_240430_234381177700_WAVE1.pdf',
 '2WK0C49FV23_1497742621_240430_234381177700_WAVE1.pdf',
 '3C55DD5CQ65_1104812833_240430_236294426600_WAVE1.pdf',
 '3C55DD5CQ65_1104812833_240430_236294426600_WAVE1.pdf',
 '3C55DD5CQ65_1104812833_240430_236294426600_WAVE1.pdf',
 '3C55DD5CQ65_1104812833_240430_236294426600_WAVE1.pdf',
 '3DP3MQ3UQ01_1467454819_240430_234448047700_WAVE1.pdf',
 '3DP3MQ3UQ01_1467454819_240430_234448047700_WAVE1.pdf',
 '3KJ0K55WR30_1518018217_240430_232872376800_WAVE1.pdf',
 '3KJ0K55WR30_1518018217_240430_232872376800_WAVE1.pdf',
 '3KJ0K55WR30_1518018217_240430_232872376800_WAVE1.pdf',
 '3KT2GP0YP94_1033137633_240430_235556616000_WAVE1.pdf',
 '3N96N06CC49_1215943253_240430_235919888800_WAVE1.pdf',
 '3Q23VG1QD73_1205129731_240430_234291514600_WAVE1.pdf',
 '3V43WA2PG08_1962591198_240430_235649451300_WAVE1.pdf',
 '3YY7P88CU43_1003917147_240430_231954746800_WAVE1.pdf',
 '3YY7P88CU43_1003917147_240430_231954746800_WAVE1.pdf',
 '4CH2Y58XX13_1194711945_240430_234138040600_WAVE1.pdf',
 '4CW1G86NX24_1619372265_240430_234507704400_WAVE1.pdf',
 '4CW1G86NX24_1619372265_240430_234507704400_WAVE1.pdf',
 '4CW1G86NX24_1619372265_240430_234507704400_WAVE1.pdf',
 '4DP1MJ6EM19_1013002435_240430_999999999999_WAVE1.pdf',
 '4DP1MJ6EM19_1013002435_240430_999999999999_WAVE1.pdf',
 '4DP1MJ6EM19_1013002435_240430_999999999999_WAVE1.pdf',
 '4DP1MJ6EM19_1013002435_240430_999999999999_WAVE1.pdf',
 '4DP1MJ6EM19_1013002435_240430_999999999999_WAVE1.pdf',
 '4DP1MJ6EM19_1013002435_240430_999999999999_WAVE1.pdf',
 '4DP1MJ6EM19_1013002435_240430_999999999999_WAVE1.pdf',
 '4GH2KC2TA15_1871638080_240430_235258647800_WAVE1.pdf',
 '4GH2KC2TA15_1871638080_240430_235258647800_WAVE1.pdf',
 '4JW3DP0KW12_1710209622_240430_232348300600_WAVE1.pdf',
 '4K88GH1VU22_1013195841_240430_240491769500_WAVE1.pdf',
 '4MA3E72GF02_1639336134_240430_231691455200_WAVE1.pdf',
 '4MA3E72GF02_1639336134_240430_231691455200_WAVE1.pdf',
 '4MA3E72GF02_1639336134_240430_231691455200_WAVE1.pdf',
 '4MA3E72GF02_1639336134_240430_231691455200_WAVE1.pdf',
 '4P73UH0QD70_1639336134_240430_233014872800_WAVE1.pdf',
 '4P73UH0QD70_1639336134_240430_233014872800_WAVE1.pdf',
 '4PY7A27DN30_1306015607_240430_234137934800_WAVE1.pdf',
 '4Q32TT9TE25_1710086533_240430_240480143600_WAVE1.pdf',
 '4Q32TT9TE25_1710086533_240430_240480143600_WAVE1.pdf',
 '4Q32TT9TE25_1710086533_240430_240480143600_WAVE1.pdf',
 '4Q32TT9TE25_1710086533_240430_240480143600_WAVE1.pdf',
 '4Q32TT9TE25_1710086533_240430_240480143600_WAVE1.pdf',
 '4Q32TT9TE25_1710086533_240430_240480143600_WAVE1.pdf',
 '4Q32TT9TE25_1710086533_240430_240480143600_WAVE1.pdf',
 '4R44A10CQ04_1881671154_240430_233391769900_WAVE1.pdf',
 '4RD2D82RG26_1285620658_240430_240110887800_WAVE1.pdf',
 '4YD9VU3GH27_1457347411_240430_235908365900_WAVE1.pdf',
 '4YD9VU3GH27_1457347411_240430_235908365900_WAVE1.pdf',
 '5EK7DC6GG72_1801814231_240430_999999999999_WAVE1.pdf',
 '5QK8RK8PU62_1053573519_240430_240024132900_WAVE1.pdf',
 '5QK8RK8PU62_1053573519_240430_240024132900_WAVE1.pdf',
 '5RG4A12TE67_1578574596_240430_235663803500_WAVE1.pdf',
 '5TU6A00QE67_1376724005_240430_234653474700_WAVE1.pdf',
 '5TU6A00QE67_1407517451_240430_231975754900_WAVE1.pdf',
 '5UR8VJ9NT46_1265463731_240430_235133433300_WAVE1.pdf',
 '5UR8VJ9NT46_1265463731_240430_235133433300_WAVE1.pdf',
 '5UR8VJ9NT46_1265463731_240430_235133433300_WAVE1.pdf',
 '5UR8VJ9NT46_1265463731_240430_235133433300_WAVE1.pdf',
 '5UR8VJ9NT46_1265463731_240430_235133433300_WAVE1.pdf',
 '5UR8VJ9NT46_1265463731_240430_235133433300_WAVE1.pdf',
 '5UR8VJ9NT46_1265463731_240430_235133433300_WAVE1.pdf',
 '5V92YJ8XW15_1992071211_240430_236608489100_WAVE1.pdf',
 '5V92YJ8XW15_1992071211_240430_236608489100_WAVE1.pdf',
 '5V92YJ8XW15_1992071211_240430_236608489100_WAVE1.pdf',
 '5VK9Y89QR49_1205946027_240430_236170379100_WAVE1.pdf',
 '5VK9Y89QR49_1205946027_240430_236170379100_WAVE1.pdf',
 '5VK9Y89QR49_1205946027_240430_236170379100_WAVE1.pdf',
 '5WX7QY7QT41_1518973502_240430_240122355500_WAVE1.pdf',
 '5WX7QY7QT41_1518973502_240430_240122355500_WAVE1.pdf',
 '5WX7QY7QT41_1518973502_240430_240122355500_WAVE1.pdf',
 '5XH4XQ6XR08_1861479057_240430_999999999999_WAVE1.pdf',
 '5XH4XQ6XR08_1861479057_240430_999999999999_WAVE1.pdf',
 '5XH4XQ6XR08_1861479057_240430_999999999999_WAVE1.pdf',
 '5Y79H49RM25_1326035874_240430_235112605000_WAVE1.pdf',
 '6CG7N27XF50_1649417239_240430_999999999999_WAVE1.pdf',
 '6DV3NP6RF99_1639300528_240430_231521219500_WAVE1.pdf',
 '6EF6MX6TY82_1902246580_240430_999999999999_WAVE1.pdf',
 '6EF6MX6TY82_1902246580_240430_999999999999_WAVE1.pdf',
 '6EF6MX6TY82_1902246580_240430_999999999999_WAVE1.pdf',
 '6FW6QY5FJ53_1184985434_240430_240914939800_WAVE1.pdf',
 '6H60QR5TC36_1467815936_240430_232224086800_WAVE1.pdf',
 '6MC7N99TC41_1780621821_240430_234748559800_WAVE1.pdf',
 '6Q32RX6QC93_1134129182_240430_236559862700_WAVE1.pdf',
 '6Q32RX6QC93_1134129182_240430_236559862700_WAVE1.pdf',
 '6Q32RX6QC93_1134129182_240430_236559862700_WAVE1.pdf',
 '6Q32RX6QC93_1134129182_240430_236559862700_WAVE1.pdf',
 '6R03GA8RV15_1558456814_240430_240120586500_WAVE1.pdf',
 '6R03GA8RV15_1558456814_240430_240120586500_WAVE1.pdf',
 '6R37W56VJ49_1972515823_240430_999999999999_WAVE1.pdf',
 '6R37W56VJ49_1972515823_240430_999999999999_WAVE1.pdf',
 '6UE7AE1AG66_1801814231_240430_999999999999_WAVE1.pdf',
 '6UE7AE1AG66_1801814231_240430_999999999999_WAVE1.pdf',
 '6WY5T41XX66_1457347411_240430_232204941700_WAVE1.pdf',
 '6WY5T41XX66_1457347411_240430_232204941700_WAVE1.pdf',
 '6WY5T41XX66_1457347411_240430_232204941700_WAVE1.pdf',
 '6WY5T41XX66_1457347411_240430_232204941700_WAVE1.pdf',
 '6YA5TQ9FJ56_1144243908_240430_240122377700_WAVE1.pdf',
 '6YA5TQ9FJ56_1144243908_240430_240122377700_WAVE1.pdf',
 '6YA5TQ9FJ56_1144243908_240430_240122377700_WAVE1.pdf',
 '7J01DY4CU14_1912189663_240430_235918962100_WAVE1.pdf',
 '7J01DY4CU14_1912189663_240430_235918962100_WAVE1.pdf',
 '7J01DY4CU14_1912189663_240430_235918962100_WAVE1.pdf',
 '7J01DY4CU14_1912189663_240430_235918962100_WAVE1.pdf',
 '7J01DY4CU14_1912189663_240430_235918962100_WAVE1.pdf',
 '7J01DY4CU14_1912189663_240430_235918962100_WAVE1.pdf',
 '7J88C42KC40_1912972985_240430_999999999999_WAVE1.pdf',
 '7J88C42KC40_1912972985_240430_999999999999_WAVE1.pdf',
 '7J88C42KC40_1912972985_240430_999999999999_WAVE1.pdf',
 '7J88C42KC40_1912972985_240430_999999999999_WAVE1.pdf',
 '7M82HE5EQ89_1346872231_240430_233303940900_WAVE1.pdf',
 '7M82HE5EQ89_1346872231_240430_233303940900_WAVE1.pdf',
 '7M82HE5EQ89_1346872231_240430_233303940900_WAVE1.pdf',
 '7M82HE5EQ89_1346872231_240430_233303940900_WAVE1.pdf',
 '7P96E09DA40_1740361187_240430_235378764100_WAVE1.pdf',
 '7PD8XM1YK58_1992071211_240430_234555687800_WAVE1.pdf',
 '7PR2W29TC03_1457302812_240430_240378421800_WAVE1.pdf',
 '7PR2W29TC03_1457302812_240430_240378421800_WAVE1.pdf',
 '7PR2W29TC03_1457302812_240430_240378421800_WAVE1.pdf',
 '7RJ5R03AJ92_1417117979_240430_235696189100_WAVE1.pdf',
 '7RJ5R03AJ92_1417117979_240430_235696189100_WAVE1.pdf',
 '7UG1KK7PE97_1063778173_240430_999999999999_WAVE1.pdf',
 '7UG1KK7PE97_1063778173_240430_999999999999_WAVE1.pdf',
 '7UG1KK7PE97_1063778173_240430_999999999999_WAVE1.pdf',
 '7UG1KK7PE97_1063778173_240430_999999999999_WAVE1.pdf',
 '7UG1KK7PE97_1063778173_240430_999999999999_WAVE1.pdf',
 '7UG1KK7PE97_1063778173_240430_999999999999_WAVE1.pdf',
 '7UG1KK7PE97_1063778173_240430_999999999999_WAVE1.pdf',
 '7UG1KK7PE97_1063778173_240430_999999999999_WAVE1.pdf',
 '7UG1KK7PE97_1063778173_240430_999999999999_WAVE1.pdf',
 '7VN9FH4HG54_1972515823_240430_235798373600_WAVE1.pdf',
 '8AN7J18GM68_1003094046_240430_235008080400_WAVE1.pdf',
 '8AN7J18GM68_1003094046_240430_235008080400_WAVE1.pdf',
 '8AN7J18GM68_1003094046_240430_235008080400_WAVE1.pdf',
 '8AN7J18GM68_1003094046_240430_235008080400_WAVE1.pdf',
 '8AN7J18GM68_1003094046_240430_235008080400_WAVE1.pdf',
 '8C89D35RN37_1467435107_240430_999999999999_WAVE1.pdf',
 '8C89D35RN37_1467435107_240430_999999999999_WAVE1.pdf',
 '8DM3JR1VN70_1295787638_240430_234876688300_WAVE1.pdf',
 '8E11EE5TM00_1174699151_240430_999999999999_WAVE1.pdf',
 '8E11EE5TM00_1174699151_240430_999999999999_WAVE1.pdf',
 '8E11EE5TM00_1174699151_240430_999999999999_WAVE1.pdf',
 '8E11EE5TM00_1174699151_240430_999999999999_WAVE1.pdf',
 '8G15XG3VV56_1538111927_240430_236571465600_WAVE1.pdf',
 '8G47VY1AK71_1992071211_240430_236333892600_WAVE1.pdf',
 '8G47VY1AK71_1992071211_240430_236333892600_WAVE1.pdf',
 '8G47VY1AK71_1992071211_240430_236333892600_WAVE1.pdf',
 '8G47VY1AK71_1992071211_240430_236333892600_WAVE1.pdf',
 '8G47VY1AK71_1992071211_240430_236333892600_WAVE1.pdf',
 '8H24UE9NC82_1972515823_240430_234631518900_WAVE1.pdf',
 '8H24UE9NC82_1972515823_240430_234631518900_WAVE1.pdf',
 '8H24UE9NC82_1972515823_240430_234631518900_WAVE1.pdf',
 '8H24UE9NC82_1972515823_240430_234631518900_WAVE1.pdf',
 '8HG2P35FQ26_1255368338_240430_233761198600_WAVE1.pdf',
 '8HR3R50QQ94_1174539100_240430_999999999999_WAVE1.pdf',
 '8HR3R50QQ94_1174539100_240430_999999999999_WAVE1.pdf',
 '8R11J92YK80_1043444615_240430_232625702700_WAVE1.pdf',
 '8R11J92YK80_1043444615_240430_232625702700_WAVE1.pdf',
 '8RU3GV1AY92_1902246580_240430_233516132800_WAVE1.pdf',
 '8YA4UR3TJ66_1013002435_240430_999999999999_WAVE1.pdf',
 '8YA4UR3TJ66_1013002435_240430_999999999999_WAVE1.pdf',
 '8YA4UR3TJ66_1013002435_240430_999999999999_WAVE1.pdf',
 '8YA4UR3TJ66_1013002435_240430_999999999999_WAVE1.pdf',
 '8YW3RQ4JR09_1871609404_240430_236049647500_WAVE1.pdf',
 '8YW3RQ4JR09_1871609404_240430_236049647500_WAVE1.pdf',
 '9AX4RV5YG72_1518018217_240430_235649406900_WAVE1.pdf',
 '9AX4RV5YG72_1518018217_240430_235649406900_WAVE1.pdf',
 '9AX4RV5YG72_1518018217_240430_235649406900_WAVE1.pdf',
 '9AX4RV5YG72_1518018217_240430_235649406900_WAVE1.pdf',
 '9AX4RV5YG72_1518018217_240430_235649406900_WAVE1.pdf',
 '9AX4RV5YG72_1518018217_240430_235649406900_WAVE1.pdf',
 '9AX4RV5YG72_1518018217_240430_235649406900_WAVE1.pdf',
 '9AX4RV5YG72_1518018217_240430_235649406900_WAVE1.pdf',
 '9AX4RV5YG72_1518018217_240430_235649406900_WAVE1.pdf',
 '9CJ8D83KD20_1801208707_240430_231811039900_WAVE1.pdf',
 '9F99RQ0PR21_1942590542_240430_234024235000_WAVE1.pdf',
 '9FD8M86AX29_1477729713_240430_240045686000_WAVE1.pdf',
 '9GF8T20MH49_1235411844_240430_999999999999_WAVE1.pdf',
 '9GF8T20MH49_1235411844_240430_999999999999_WAVE1.pdf',
 '9GF8T20MH49_1235411844_240430_999999999999_WAVE1.pdf',
 '9GH8HQ3DX23_1992071211_240430_240119357400_WAVE1.pdf',
 '9JN0VE0KD21_1174575476_240430_999999999999_WAVE1.pdf',
 '9JN0VE0KD21_1174575476_240430_999999999999_WAVE1.pdf',
 '9NF4DU0NU68_1063440840_240430_236059741400_WAVE1.pdf',
 '9RC4GR2RH86_1699138255_240430_231260261100_WAVE1.pdf',
 '9RC4GR2RH86_1699138255_240430_231260261100_WAVE1.pdf',
 '9RC4GR2RH86_1699138255_240430_231260261100_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9RE9U83EF26_1649298738_240430_235258664400_WAVE1.pdf',
 '9VG4YG2YQ06_1275534745_240430_999999999999_WAVE1.pdf',
 '9Y59DU9RP15_1790890408_240430_999999999999_WAVE1.pdf',
 'MA_CP_CHART_1AK4NR9CK65_1528070166_240430_233426637000_WAVE1.pdf',
 'MA_CP_CHART_1AK4NR9CK65_1528070166_240430_233426637000_WAVE1.pdf',
 'MA_CP_CHART_1CP1Y15PV96_1164997698_240430_232650830200_WAVE1.pdf',
 'MA_CP_CHART_1CP1Y15PV96_1164997698_240430_232650830200_WAVE1.pdf',
 'MA_CP_CHART_1HA4W61VY88_1770740441_240430_234116828000_WAVE1.pdf',
 'MA_CP_CHART_1HA4W61VY88_1770740441_240430_234116828000_WAVE1.pdf',
 'MA_CP_CHART_1KP6T88CJ77_1265835383_240430_236459483200_WAVE1.pdf',
 'MA_CP_CHART_1KP6T88CJ77_1265835383_240430_236459483200_WAVE1.pdf',
 'MA_CP_CHART_1MC6MK5UD72_1528106192_240430_236214449000_WAVE1.pdf',
 'MA_CP_CHART_1MC6MK5UD72_1528106192_240430_236214449000_WAVE1.pdf',
 'MA_CP_CHART_1MC6MK5UD72_1528106192_240430_236214449000_WAVE1.pdf',
 'MA_CP_CHART_1T41GF9AC04_1174054274_240430_235957564800_WAVE1.pdf',
 'MA_CP_CHART_1T41GF9AC04_1174054274_240430_235957564800_WAVE1.pdf',
 'MA_CP_CHART_1T41GF9AC04_1174054274_240430_235957564800_WAVE1.pdf',
 'MA_CP_CHART_1T41GF9AC04_1174054274_240430_235957564800_WAVE1.pdf',
 'MA_CP_CHART_1T41GF9AC04_1174054274_240430_235957564800_WAVE1.pdf',
 'MA_CP_CHART_1UA2XT9GE11_1811912074_240430_240093575600_WAVE1.pdf',
 'MA_CP_CHART_1UA2XT9GE11_1811912074_240430_240093575600_WAVE1.pdf',
 'MA_CP_CHART_1UA2XT9GE11_1811912074_240430_240093575600_WAVE1.pdf',
 'MA_CP_CHART_1UA2XT9GE11_1811912074_240430_240093575600_WAVE1.pdf',
 'MA_CP_CHART_1UA2XT9GE11_1811912074_240430_240093575600_WAVE1.pdf',
 'MA_CP_CHART_1UA2XT9GE11_1811912074_240430_240093575600_WAVE1.pdf',
 'MA_CP_CHART_1UA2XT9GE11_1811912074_240430_240093575600_WAVE1.pdf',
 'MA_CP_CHART_2AY5V36FA68_1265688311_240430_231297949700_WAVE1.pdf',
 'MA_CP_CHART_2AY5V36FA68_1265688311_240430_231297949700_WAVE1.pdf',
 'MA_CP_CHART_2AY5V36FA68_1265688311_240430_231297949700_WAVE1.pdf',
 'MA_CP_CHART_2NP7M81YK47_1487816211_240430_235423309200_WAVE1.pdf',
 'MA_CP_CHART_2NP7M81YK47_1487816211_240430_235423309200_WAVE1.pdf',
 'MA_CP_CHART_3G11FJ1FT04_1619130408_240430_240046563000_WAVE1.pdf',
 'MA_CP_CHART_3TG2PV6ER97_1316618481_240430_233040165500_WAVE1.pdf',
 'MA_CP_CHART_4D24WQ1XF84_1881012995_240430_240210819400_WAVE1.pdf',
 'MA_CP_CHART_4D24WQ1XF84_1881012995_240430_240210819400_WAVE1.pdf',
 'MA_CP_CHART_4EF4C20VD04_1417069295_240430_234170781700_WAVE1.pdf',
 'MA_CP_CHART_4EF4C20VD04_1417069295_240430_234170781700_WAVE1.pdf',
 'MA_CP_CHART_4EF4C20VD04_1417069295_240430_234170781700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4H39K99PC53_1053322719_240430_233951962700_WAVE1.pdf',
 'MA_CP_CHART_4Q69PD4YV41_1548453814_240430_234643326000_WAVE1.pdf',
 'MA_CP_CHART_4Q69PD4YV41_1548453814_240430_234643326000_WAVE1.pdf',
 'MA_CP_CHART_4Q69PD4YV41_1548453814_240430_234643326000_WAVE1.pdf',
 'MA_CP_CHART_4Q69PD4YV41_1548453814_240430_234643326000_WAVE1.pdf',
 'MA_CP_CHART_4Q69PD4YV41_1548453814_240430_234643326000_WAVE1.pdf',
 'MA_CP_CHART_4Q69PD4YV41_1548453814_240430_234643326000_WAVE1.pdf',
 'MA_CP_CHART_4YC4JQ6PF93_1588732911_240430_233109631000_WAVE1.pdf',
 'MA_CP_CHART_4YC4JQ6PF93_1588732911_240430_233109631000_WAVE1.pdf',
 'MA_CP_CHART_4YC4JQ6PF93_1588732911_240430_233109631000_WAVE1.pdf',
 'MA_CP_CHART_5A50K86TE17_1013209568_240430_231368316900_WAVE1.pdf',
 'MA_CP_CHART_5A50K86TE17_1013209568_240430_231368316900_WAVE1.pdf',
 'MA_CP_CHART_5A50K86TE17_1013209568_240430_231368316900_WAVE1.pdf',
 'MA_CP_CHART_5A50K86TE17_1013209568_240430_231368316900_WAVE1.pdf',
 'MA_CP_CHART_5A50K86TE17_1013209568_240430_231368316900_WAVE1.pdf',
 'MA_CP_CHART_5A50K86TE17_1013209568_240430_231368316900_WAVE1.pdf',
 'MA_CP_CHART_5DD5GH5NU66_1720009301_240430_999999999999_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5H76JR2KU81_1467742213_240430_231238233800_WAVE1.pdf',
 'MA_CP_CHART_5KM8X53KU43_1174502629_240430_231239743100_WAVE1.pdf',
 'MA_CP_CHART_5KM8X53KU43_1174502629_240430_231239743100_WAVE1.pdf',
 'MA_CP_CHART_5KM8X53KU43_1174502629_240430_231239743100_WAVE1.pdf',
 'MA_CP_CHART_5KM8X53KU43_1174502629_240430_231239743100_WAVE1.pdf',
 'MA_CP_CHART_5KM8X53KU43_1174502629_240430_231239743100_WAVE1.pdf',
 'MA_CP_CHART_5KM8X53KU43_1174502629_240430_231239743100_WAVE1.pdf',
 'MA_CP_CHART_5KM8X53KU43_1174502629_240430_231239743100_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6QY7CW2HA78_1114157518_240430_233265967400_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_6XJ1EM1CV11_1518012335_240430_230454475000_WAVE1.pdf',
 'MA_CP_CHART_7KX6R16RD79_1497765309_240430_233986115200_WAVE1.pdf',
 'MA_CP_CHART_7KX6R16RD79_1497765309_240430_233986115200_WAVE1.pdf',
 'MA_CP_CHART_7MY7RJ1GN30_1770554651_240430_231106084500_WAVE1.pdf',
 'MA_CP_CHART_7MY7RJ1GN30_1770554651_240430_231106084500_WAVE1.pdf',
 'MA_CP_CHART_7MY7RJ1GN30_1770554651_240430_231106084500_WAVE1.pdf',
 'MA_CP_CHART_7MY7RJ1GN30_1770554651_240430_231106084500_WAVE1.pdf',
 'MA_CP_CHART_7MY7RJ1GN30_1770554651_240430_231106084500_WAVE1.pdf',
 'MA_CP_CHART_7MY7RJ1GN30_1770554651_240430_231106084500_WAVE1.pdf',
 'MA_CP_CHART_7MY7RJ1GN30_1770554651_240430_231106084500_WAVE1.pdf',
 'MA_CP_CHART_7MY7RJ1GN30_1770554651_240430_231106084500_WAVE1.pdf',
 'MA_CP_CHART_7MY7RJ1GN30_1770554651_240430_231106084500_WAVE1.pdf',
 'MA_CP_CHART_7NN1GW6FM80_1114178076_240430_235094478501_WAVE1.pdf',
 'MA_CP_CHART_7NN1GW6FM80_1114178076_240430_235094478501_WAVE1.pdf',
 'MA_CP_CHART_7NN1GW6FM80_1114178076_240430_235094478501_WAVE1.pdf',
 'MA_CP_CHART_7NN1GW6FM80_1114178076_240430_235094478501_WAVE1.pdf',
 'MA_CP_CHART_7NN1GW6FM80_1114178076_240430_235094478501_WAVE1.pdf',
 'MA_CP_CHART_7UF6GK1JG30_1891992319_240430_234724297000_WAVE1.pdf',
 'MA_CP_CHART_7UF6GK1JG30_1891992319_240430_234724297000_WAVE1.pdf',
 'MA_CP_CHART_8DJ4JD1XM75_1407880511_240430_232049665800_WAVE1.pdf',
 'MA_CP_CHART_8DJ4JD1XM75_1407880511_240430_232049665800_WAVE1.pdf',
 'MA_CP_CHART_8DJ4JD1XM75_1407880511_240430_232049665800_WAVE1.pdf',
 'MA_CP_CHART_8DJ4JD1XM75_1407880511_240430_232049665800_WAVE1.pdf',
 'MA_CP_CHART_8DJ4JD1XM75_1407880511_240430_232049665800_WAVE1.pdf',
 'MA_CP_CHART_8DJ4JD1XM75_1407880511_240430_232049665800_WAVE1.pdf',
 'MA_CP_CHART_8DJ4JD1XM75_1407880511_240430_232049665800_WAVE1.pdf',
 'MA_CP_CHART_8DJ4JD1XM75_1407880511_240430_232049665800_WAVE1.pdf',
 'MA_CP_CHART_8DJ4JD1XM75_1407880511_240430_232049665800_WAVE1.pdf',
 'MA_CP_CHART_8H73PG0DX74_1588809420_240430_234600565600_WAVE1.pdf',
 'MA_CP_CHART_8NM3F13WK16_1720002512_240430_235493807600_WAVE1.pdf',
 'MA_CP_CHART_8NM3F13WK16_1720002512_240430_235493807600_WAVE1.pdf',
 'MA_CP_CHART_8NM3F13WK16_1720002512_240430_235493807600_WAVE1.pdf',
 'MA_CP_CHART_8NM3F13WK16_1720002512_240430_235493807600_WAVE1.pdf',
 'MA_CP_CHART_8NM3F13WK16_1720002512_240430_235493807600_WAVE1.pdf',
 'MA_CP_CHART_8NM3F13WK16_1720002512_240430_235493807600_WAVE1.pdf',
 'MA_CP_CHART_8NM3F13WK16_1720002512_240430_235493807600_WAVE1.pdf',
 'MA_CP_CHART_8UN6VC1PX87_1003350984_240430_235694996000_WAVE1.pdf',
 'MA_CP_CHART_8UN6VC1PX87_1003350984_240430_235694996000_WAVE1.pdf',
 'MA_CP_CHART_9EG0K83AF18_1578507216_240430_234393365800_WAVE1.pdf',
 'MA_CP_CHART_9EG0K83AF18_1578507216_240430_234393365800_WAVE1.pdf',
 'MA_CP_CHART_9EG0K83AF18_1578507216_240430_234393365800_WAVE1.pdf',
 'MA_CP_CHART_9EG0K83AF18_1578507216_240430_234393365800_WAVE1.pdf']
 
df = df.query("filename.isin(@noHCC_charts)")

# COMMAND ----------

system_prompt = """As a skilled medical coder, your objective is to meticulously examine a series of medical chart pages to identify diagnoses that explicitly satisfy the MEAT criteria: Monitoring, Evaluating, Assessing/Addressing, and Treating. For each active diagnosis, substantiate your findings with exact quotes from the medical record, and clearly note the date of service. A diagnosis can only be coded when it is explicitly named in the medical record.

## MEAT Criteria Definition

* **Monitoring**: Detailed documentation of signs, symptoms, or changes in the disease’s progression or regression.
* **Evaluating**: Specific results from tests, evidence of medications' effectiveness, and detailed observations on the patient's response to treatment.
* **Assessing/Addressing**: Comprehensive engagement, such as ordered tests, medical records review, or counseling.
* **Treating**: Explicit treatments given, including medications, therapies, referrals to specialists, or any other direct treatment actions. Medications must state which condition they are treating.

## Steps for Extraction

1. **Identify Medical Entities**: Rigorously examine each chart to find medical conditions that meet the MEAT criterion. Look for explicit evidence of signs, symptoms, treatments, or diagnostic tests that indicate active management or evaluation.
2. **Determine Assertion Status**: For each identified condition, clearly determine its status:
  - **Present**: The condition is actively managed or ongoing and satisfies the MEAT criteria.
  - **Possible**: The condition is under consideration but not fully substantiated.
  - **Past**: The condition is noted in prior encounters, clearly identified as no longer active.
  - **Absent**: Explicit documentation confirms the condition is not present.
  - **Family**: The condition is recorded as part of the patient's family history.
  - **Hypothetical**: The condition is mentioned as a possibility or theoretical scenario.
3. **Extract Date of Service (DOS)**: Accurately determine the date of service corresponding to each condition.
4. **Assign ICD-10 Codes**: Assign the correct ICD-10 code to each validated medical entity. If any uncertainty exists, refrain from assigning a code.

## Additional Notes

* Multiple diagnoses may be present. Ensure each one is thoroughly vetted for its relevance and context as per the MEAT criteria.
* In situations where notes reference past visits, diligently verify the chronological context relative to the current service date. Ensure conditions are correctly marked as past when identified as such.
* Conduct a meticulous review of the complete chart for precise coding and assertion status assignments, leaving no room for assumptions or incomplete evaluations."""

# filename_subset = list(df['filename'].value_counts().loc[lambda x: x < 50].loc[lambda x: x > 10].index)
# filename_subset = random.sample(filename_subset, 100)
# df = df.query("filename in @filename_subset")

with mlflow.start_run():
    
    # Get information on the MLflow run in case we want to use it later.
    run = mlflow.active_run()
    mlflow_run_id = run.info.run_id
    mlflow_run_name = run.info.run_name

    # Parameters
    temperature = 0.1
    max_tokens = 16384 * 2
    group_size = 3
    overlap = 1

    # Log everything
    # mlflow.log_text(text=system_prompt, artifact_file=f"system_prompt.txt")
    mlflow.log_param('system_prompt', system_prompt)
    mlflow.log_param('temperature', temperature)
    mlflow.log_param('max_tokens', max_tokens)
    mlflow.log_param('model', model)
    mlflow.log_param('group_size', group_size)
    mlflow.log_param('overlap', overlap)

    df_grouped = group_pages_with_overlap(df, group_size=group_size, overlap=overlap)
    expected_tokens = df_grouped["text"].str.split().str.len().sum() + (df_grouped.shape[0] * len(system_prompt.split()))

    # expected_tokens = df["text"].str.split().str.len().sum() + (df.shape[0] * len(system_prompt.split()))

    mlflow.log_param('chart_count', df_grouped['filename'].nunique())
    mlflow.log_param('records', df_grouped.shape[0])
    mlflow.log_param('input_word_tokens', expected_tokens)

    @retry(
        wait=wait_random_exponential(multiplier=10, max=180),
        stop=stop_after_attempt(4),
        retry=retry_if_exception_type((RateLimitError, APITimeoutError, InternalServerError)),
    )
    async def process_page(chart: str) -> str:
        try:
            response = await client.beta.chat.completions.parse(
                model=model_reasoning,
                # temperature=temperature,
                # max_tokens=max_tokens, 
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": chart},
                ],
                response_format=RiskOutputMEAT,
            )
            return response.choices[0].message.content
        except BadRequestError as e:
            print(e)
            return "ERROR: BadRequestError was raised."
        except ContentFilterFinishReasonError as e:
            print(e)
            return "ERROR: ContentFilterFinishReasonError was raised."
        except ValidationError as e:
            print(e)
            return "ERROR: ValidationError was raised."


    async def process_pdf_dataframe_all_at_once(df):
        """ "Process entire Dataframe at once and outputs the filename and page."""
        everything = [
            (row["filename"], row["page"], process_page(row["text"]))
            for _, row in df.iterrows()
        ]
        filenames = [item[0] for item in everything]
        pages = [item[1] for item in everything]
        tasks = [item[2] for item in everything]
        results = await asyncio.gather(*tasks)
        return filenames, pages, results


    # Run the asynchronous processing
    # Can't run it all at once due to Databricks telling me to contact an admin to increase how many parallel executions I can have.
    consumed_tokens = 0
    all_results = [[], [], []]
    for df_chunk in tqdm(np.array_split(df_grouped, round(df_grouped.shape[0] / 50))):
        results = asyncio.run(process_pdf_dataframe_all_at_once(df_chunk))
        all_results[0].extend(results[0])
        all_results[1].extend(results[1])
        all_results[2].extend(results[2])
        consumed_tokens += df_chunk["text"].str.split().str.len().sum() + (df_chunk.shape[0] * len(system_prompt.split()))
        print(f"Consumed tokens: {consumed_tokens:,} / {expected_tokens:,}")

    output_df = pd.DataFrame([all_results[0], all_results[1], all_results[2]]).T
    output_df.columns = "filename", "page", "llm_results"
    output_df.head(5)

    # Kinda annoying to download, but provides quick ability to view the results in the Experiments dashbord.
    mlflow.log_table(data=output_df, artifact_file="raw_results.json")

    output_path = f"/Workspace/Users/<USER>/Risk/results/{mlflow_run_id}_{mlflow_run_name}_llm_output_raw.zip"
    output_df.to_csv(output_path, index=False)
    mlflow.log_artifact(output_path)

# COMMAND ----------

# MAGIC %md
# MAGIC # Post Processing
# MAGIC
# MAGIC The LLM outputs need further post-processing before they're ready for consumption. We need to restrict the results to valid risk-adjusting codes that are within the proper DOS and marked as present.

# COMMAND ----------

# Here's a handy code snippet for in case the processing fails and you want to re-run from the raw data.
from mlflow.tracking import MlflowClient
mlf_client = MlflowClient()

mlflow_run_id = "7e212ba2eddd4b458f73b04a37d03740"
mlflow_run_name = "ambitious-dove-776"
local_dir = "/tmp"
local_path = mlf_client.download_artifacts(mlflow_run_id, "raw_results.json", local_dir)

output_df = pd.read_json("/tmp/raw_results.json", orient='split')
output_df = output_df.query("""llm_results != '{"output":null}'""")

# COMMAND ----------

import ast
import re
from dateutil.parser import parse


def standardize_dates(date: str) -> str:
    """Put dates in the format YYYY-MM-DD."""

    # Fix dates that contain a '9' instead of a '0' at the beginning.
    # E.g. 95/07/2024 should be 05/07/2024.
    def replace_nine_with_zero(match):
        return "0" + match.group(1)

    date = re.sub(r"^9(\d)", replace_nine_with_zero, str(date))

    try:
        dt = parse(str(date))
        return dt.strftime("%Y-%m-%d")
    except ValueError:
        return date


def clean_result_df(result_df: pd.DataFrame) -> pd.DataFrame:
    print(f"Records: {result_df.shape[0]:,}")

    # Remove records with blank DX_CD.
    result_df = result_df.query("DX_CD not in ['', '-']").dropna(subset=['DX_CD'])
    print(f"Records with DX_CD: {result_df.shape[0]:,}")

    # Restrict to risk-adjusting ICD-10 codes.
    result_df = result_df.query(
        "((DX_CD in @risk_adjusting_icd10s_ACA) and (LOB == 'ACA')) or ((DX_CD in @risk_adjusting_icd10s_MA) and (LOB == 'MRA'))"
    )
    print(f"Records with risk-adjusting codes: {result_df.shape[0]:,}")

    # Keep only `Present` assertions.
    result_df.loc[:, "ASSERTION"] = result_df["ASSERTION"].str.strip().str.title()
    result_df = result_df.query("ASSERTION in ['Present']")
    result_df = result_df.drop(["ASSERTION"], axis=1)
    print(f"Records with `Present` assertions: {result_df.shape[0]:,}")

    # Clean up the index.
    result_df = result_df.reset_index(drop=True)

    return result_df

# COMMAND ----------

# Get all ICD-10 codes.
icd10_codes = pd.read_csv("/Volumes/dbc_clinical_code_extraction/default/support_files/icd10cm-codes-April-2024.txt", delimiter="\t")
icd10_codes["code"] = icd10_codes["code"].str.strip()
icd10_codes.head(3)
valid_icd10s = icd10_codes["code"].values
valid_icd10s = valid_icd10s.tolist()
print(f"Number of valid ICD-10s: {len(valid_icd10s):,}")

risk_adjusting_codes_path = "/Volumes/dbc_clinical_code_extraction/default/support_files/risk_models_fromNoemi_20241113.xlsx"

# Get all risk-adjusting ICD-10 codes.
risk_adjusting_icd10s_ACA = (
    pd.read_excel(risk_adjusting_codes_path, sheet_name="ACA Model", header=3)["ICD10"]
    .dropna()
    .drop_duplicates()
    .values.tolist()
)
# Just to be extra sure, lets make sure we're only capturing the valid ICDs and not extra cruft from the Excel
# such as the footer or header.
risk_adjusting_icd10s_ACA = sorted(list(set(risk_adjusting_icd10s_ACA).intersection(set(valid_icd10s))))
print(f"First five ACA risk-adjusting ICDs: {risk_adjusting_icd10s_ACA[:5]}")
print(f"Number of risk-adjusting ICD-10s (ACA): {len(risk_adjusting_icd10s_ACA):,}") 

risk_adjusting_icd10s_MA = (
    pd.read_excel(risk_adjusting_codes_path, sheet_name="MA_HCC Model", header=3).query("~`CMS-HCC Model Category V28`.isna()")["DiagnosisCode"]
    .dropna()
    .drop_duplicates()
    .values.tolist()
)
# Same as above, clean up the list of ICD codes.
risk_adjusting_icd10s_MA = sorted(list(set(risk_adjusting_icd10s_MA).intersection(set(valid_icd10s))))
print(f"First five MA risk-adjusting ICDs: {risk_adjusting_icd10s_MA[:5]}")
print(f"Number of risk-adjusting ICD-10s (MA): {len(risk_adjusting_icd10s_MA):,}") 

all_risk_adjusting_codes = set(risk_adjusting_icd10s_ACA + risk_adjusting_icd10s_MA)

# COMMAND ----------

# Xwalk between member id and chart PDF name. Needed for associating the charts with claims data.
mbrid_chrtpdfnm_xwalk = pd.concat(
    [
        pd.read_excel(
            "/Volumes/dbc_clinical_code_extraction/default/support_files/Altais_Wave1_N&U_11082024.xlsx"
        )[["MBR_ID", "CHRT_PDF_NM"]],
        pd.read_excel(
            "/Volumes/dbc_clinical_code_extraction/default/support_files/Altais_N&U_GenAI_11082024.xlsx"
        )[["mbr_id", "CHRT_PDF_NM"]].rename(columns={"mbr_id": "MBR_ID"}),
    ]
).drop_duplicates()

# Codes already found in chart review.
cr_found_codes = (
    pd.concat(
        [
            pd.read_excel(
                "/Volumes/dbc_clinical_code_extraction/default/support_files/Altais_186_Coding.xlsx"
            )[["BSC_CHART_ID", "CHRT_PDF_NM", "DX_FINAL", "COMMENT1"]],
            pd.read_excel(
                "/Volumes/dbc_clinical_code_extraction/default/support_files/Altais_Wave1_N&U_11082024.xlsx"
            )[["BSC_CHART_ID", "CHRT_PDF_NM", "dx_code", "COMMENT1"]].rename(
                columns={"dx_code": "DX_FINAL"}
            ),
        ]
    )
    .dropna(subset=["DX_FINAL"])
    .query("COMMENT1 == 'ADD'")
    .rename(columns={"DX_FINAL": "DX_CD"})
    .drop_duplicates()
)
cr_found_codes.loc[:, "DX_CD"] = cr_found_codes["DX_CD"].str.replace(".", "", regex=False)
print(f"Records found in CR: {cr_found_codes.shape[0]:,}")

# Xwalk between the LOB and chart PDF name.
chart_LOB_xwalk = spark.sql(f"SELECT DISTINCT filename AS CHRT_PDF_NM, LOB, project_year FROM {text_table}").toPandas()
# chart_LOB_xwalk = pd.read_csv(
#     "/Volumes/dbc_clinical_code_extraction/default/support_files/chart_LOB_xwalk.csv"
# )
# chart_LOB_xwalk.loc[:, "CHRT_PDF_NM"] = chart_LOB_xwalk["filename"]

# Load in claims data.
# TODO ideally we could refresh this on Databricks and not need to pull data locally.
claims_data = pd.read_csv(
    "/Volumes/dbc_clinical_code_extraction/default/support_files/claims_20241217_relevant.csv"
)
claims_data_chart = pd.merge(
    claims_data[["MBR_ID", "DX_ICD10"]], mbrid_chrtpdfnm_xwalk, on="MBR_ID", how="inner"
).rename({"DX_ICD10": "DX_CD"}, axis=1)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Process LLM outputs

# COMMAND ----------

llm_results = output_df.query("~llm_results.str.contains('null')").copy(deep=True)

# input_pages = sum([1 for k in llm_results.keys() for page_results in llm_results[k]])
# print(f"Input pages: {input_pages:,}")
print(f"Output word tokens: {llm_results['llm_results'].str.split().str.len().sum():,}")
print()
print(f"Initial records: {llm_results.shape[0]:,}")
print(f"Initial charts: {llm_results['filename'].nunique():,}")

# Filter results with errors.
llm_results = llm_results.query("~llm_results.str.contains('ERROR')")
print(f"Records after removing errors: {llm_results.shape[0]:,}")

# Split the JSON to one key per column.
llm_results["llm_results"] = llm_results["llm_results"].apply(json.loads).apply(lambda x: x["output"])

# The structured output was allowed to output a null.
llm_results = llm_results.dropna()
print(f"Records after removing null: {llm_results.shape[0]:,}")

# Some pages have multiple outputs, we'll explode them.
llm_results = llm_results.explode("llm_results")
print(f"Records after exploding pages with multiple entries: {llm_results.shape[0]:,}")

# TODO: why is o3-mini leading to another column?
try:
    structured_output_keys = list(llm_results["llm_results"].head(1).values[0].keys())
    llm_results[structured_output_keys] = llm_results["llm_results"].apply(pd.Series)
except ValueError:
    structured_output_keys += ['mystery']
    llm_results[structured_output_keys] = llm_results["llm_results"].apply(pd.Series)
    llm_results = llm_results.drop(['mystery'], axis=1)

llm_results = llm_results.drop("llm_results", axis=1)

# TODO: deal with DXcode that contain multiple entries.
llm_results = llm_results[~llm_results['DXcode'].isna()]
llm_results = llm_results.query("~DXcode.str.contains(',')")
print(f"Records after removing malformed ICD outputs: {llm_results.shape[0]:,}")

# Clean up column names.
llm_results = llm_results.rename(
    {
        "filename": "CHRT_PDF_NM",
        "page": "PAGE",
        "DOS": "LLM_DOS",
        "MedicalEntity": "LLM_EXTRACTED_ENTITY",
        "DXcode": "DX_CD",
        "Assertion": "ASSERTION",
    },
    axis=1,
)

# Standardize DX_CD.
llm_results.loc[:, "DX_CD"] = llm_results["DX_CD"].str.replace(".", "", regex=False).str.strip()

# Add in ICD-10 descriptions and LOB information.
# TODO - it looks like some of the DX codes that didn't join are valid. Maybe I need a new DX code list.
llm_results = llm_results.merge(icd10_codes, left_on="DX_CD", right_on="code", how="inner").drop(
    columns=["code"], axis=1
)
print(f"Records after joining on ICD-10 descriptions: {llm_results.shape[0]:,}")
llm_results = llm_results.merge(chart_LOB_xwalk[["CHRT_PDF_NM", "LOB"]], on="CHRT_PDF_NM", how="left")
print(f"Records after joining on LOB information: {llm_results.shape[0]:,}")

print("\n===Begin Cleaning===")
llm_results = clean_result_df(llm_results)

llm_results = llm_results.rename({"description": "ICD10_DESC"}, axis=1)

unique_chart_codes = llm_results[["CHRT_PDF_NM", "DX_CD"]].drop_duplicates().shape[0]
print(f"Unique chart-code pairs before filtering DOS: {unique_chart_codes:,}")

if "LLM_DOS" in llm_results.columns:
    llm_results["LLM_DOS"] = llm_results["LLM_DOS"].apply(standardize_dates)
    # What year to keep depends on LOB. MA is past year and ACA is current year.
    # TODO this should not be hardcoded.
    llm_results = llm_results.query(
        "(LLM_DOS.str.contains('2024') and (LOB == 'ACA')) or (LLM_DOS.str.contains('2023') and (LOB == 'MRA'))"
    )
    
unique_chart_codes = llm_results[["CHRT_PDF_NM", "DX_CD"]].drop_duplicates().shape[0]
print(f"Unique chart-code pairs after filtering DOS: {unique_chart_codes:,}")

print("LOB breakdown per chart:")
print(llm_results[["CHRT_PDF_NM", "LOB"]].drop_duplicates()["LOB"].value_counts().to_frame().reset_index())

llm_results.head(3)

# COMMAND ----------

# llm_results
# llm_results[["CHRT_PDF_NM",'DX_CD', "MEAT_count", "Monitoring", "Monitoring2", "Evaluating","Evaluating2", "Assessing","Assessing2", "Treating", "Treating2"]].query("MEAT_count != 4")
# llm_results[llm_results['Treating'] != llm_results['Treating2'] ][["CHRT_PDF_NM",'DX_CD', "MEAT_count", "Monitoring", "Monitoring2", "Evaluating","Evaluating2", "Assessing","Assessing2", "Treating", "Treating2"]].sample(20)

# COMMAND ----------

# Normalize instances of a MEAT criteria being absent so that we can do some filtering.

# TODO I'm not super keen on these handwritten rules, but from a quick analysis they seem to be working well.
# An automated LLM-as-a-judge approach might be cleaner (but definitely slower).
MEAT_columns = ['Monitoring', 'Evaluating', 'Assessing', 'Treating']
# for col in MEAT_columns:
#     llm_results[f"{col}2"] = llm_results[col]
for col in MEAT_columns:
    llm_results[col] = llm_results[col].str.strip().str.replace(r"(None|N/A)", '', regex=True)
    llm_results[col][llm_results[col].str.contains('Not explicitly')] = ''
    llm_results[col][llm_results[col].str.contains('No explicit')] = ''
    
llm_results['Monitoring'][llm_results["Monitoring"].str.contains('No specific monitor')] = ''
llm_results['Evaluating'][llm_results["Evaluating"].str.contains('No( ?\w* )eval', regex=True)] = ''
llm_results['Evaluating'][llm_results["Evaluating"].str.contains('No( ?\w* )test', regex=True)] = ''
llm_results['Assessing'][llm_results["Assessing"].str.contains('Not? specific(ally)? assess', regex=True)] = ''
llm_results['Treating'][llm_results["Treating"].str.contains('No( ?\w* \w*? ?)treat', regex=True)] = ''
llm_results['Treating'][llm_results["Treating"].str.contains('No specific med')] = ''
llm_results['Treating'][llm_results["Treating"].str.contains('Not on antidepressants')] = ''

def MEAT_count(row) -> int:
    """Count number of MEAT criteria that are present.

    Note: this counts if they are present not if they are valid."""
    return sum(row[MEAT_columns] != "")


llm_results["MEAT_count"] = llm_results.apply(MEAT_count, axis=1)
print("MEAT count breakdown:")
print(llm_results["MEAT_count"].value_counts())

# # Remove records where all 4 MEAT criteria are ''.
# llm_results = llm_results.query("MEAT_count != 0")
# Remove codes without more than 2 MEAT criteria met.
llm_results = llm_results.query("MEAT_count > 2")

unique_chart_codes = llm_results[["CHRT_PDF_NM", "DX_CD"]].drop_duplicates().shape[0]
print(f"Unique chart-code pairs after removing codes with no MEAT: {unique_chart_codes:,}")
mlflow.log_metric('llm_unique_codes', unique_chart_codes, run_id=mlflow_run_id)

# COMMAND ----------

llm_codes_found_in_chart_review = llm_results.merge(
    cr_found_codes[["CHRT_PDF_NM", "DX_CD"]], on=["CHRT_PDF_NM", "DX_CD"], how="inner"
).drop_duplicates()

llm_codes_found_in_claims = llm_results.drop(['PAGE'], axis=1).merge(
    claims_data_chart[["CHRT_PDF_NM", "DX_CD"]], on=["CHRT_PDF_NM", "DX_CD"], how="inner"
).drop_duplicates()

chartreview_codes_not_found_by_llm = (
    cr_found_codes[["CHRT_PDF_NM", "DX_CD"]]
    .merge(llm_results, on=["CHRT_PDF_NM", "DX_CD"], how="left", indicator=True)
    .query('_merge == "left_only"')
    .drop(columns=["_merge"])
).drop_duplicates()

print("REMOVE CODES FOUND IN CHART REVIEW")
# Do a left-anti join to remove codes already found in chart review.
llm_results = (
    llm_results.merge(cr_found_codes[["CHRT_PDF_NM", "DX_CD"]], on=["CHRT_PDF_NM", "DX_CD"], how="left", indicator=True)
    .query('_merge == "left_only"')
    .drop(columns=["_merge"])
)

# display(llm_results["LOB"].value_counts().to_frame().reset_index())
print(llm_results[["CHRT_PDF_NM", "LOB"]].drop_duplicates()["LOB"].value_counts().to_frame().reset_index())

print("REMOVE CODES FOUND IN CLAIMS")
# Do a left-anti join to remove codes already found in claims.
llm_results = (
    llm_results.merge(
        claims_data_chart[["CHRT_PDF_NM", "DX_CD"]], on=["CHRT_PDF_NM", "DX_CD"], how="left", indicator=True
    )
    .query('_merge == "left_only"')
    .drop(columns=["_merge"])
)

# display(llm_results["LOB"].value_counts().to_frame().reset_index())
print(llm_results[["CHRT_PDF_NM", "LOB"]].drop_duplicates()["LOB"].value_counts().to_frame().reset_index())

# COMMAND ----------

# Get all the codes found in CR or claims at the chart level.
unique_chart_codes_found_CR_claims = pd.concat([llm_codes_found_in_chart_review[["CHRT_PDF_NM", "DX_CD"]], llm_codes_found_in_claims[["CHRT_PDF_NM", "DX_CD"]]]).drop_duplicates().shape[0]

llm_codes_found_CR = llm_codes_found_in_chart_review[["CHRT_PDF_NM", "DX_CD"]].drop_duplicates().shape[0]
llm_codes_found_claims = llm_codes_found_in_claims[["CHRT_PDF_NM", "DX_CD"]].drop_duplicates().shape[0]
print(f"CR codes, claims codes, CR or claims: {llm_codes_found_CR:,}, {llm_codes_found_claims:,}, {unique_chart_codes_found_CR_claims:,}")
print(f"All LLM found codes: {unique_chart_codes:,}\n")

llm_codes_in_CR = llm_codes_found_CR / unique_chart_codes
print(f"Fraction of LLM codes that were found in chart review: {llm_codes_in_CR:.4f}")
mlflow.log_metric('fraction_llm_codes_found_in_CR', llm_codes_in_CR, run_id=mlflow_run_id)

llm_codes_in_claims = llm_codes_found_claims / unique_chart_codes
print(f"Fraction of LLM codes that were found in claims: {llm_codes_in_claims:.4f}")
mlflow.log_metric('fraction_llm_codes_found_in_claims', llm_codes_in_claims, run_id=mlflow_run_id)

llm_codes_in_cr_or_claims = unique_chart_codes_found_CR_claims / unique_chart_codes
print(f"Fraction of LLM codes that were found in chart review or claims: {llm_codes_in_cr_or_claims:.4f}")
mlflow.log_metric('fraction_llm_codes_found_in_CR_or_claims', llm_codes_in_cr_or_claims, run_id=mlflow_run_id)

fraction_cr_codes_found = llm_codes_found_CR / cr_found_codes[["CHRT_PDF_NM", "DX_CD"]].drop_duplicates().shape[0]
print(f"Fraction of CR codes found by LLM: {fraction_cr_codes_found:.4f}")
mlflow.log_metric('fraction_cr_codes_found_by_llm', fraction_cr_codes_found, run_id=mlflow_run_id)

# COMMAND ----------

llm_results = llm_results.sort_values(["LOB", "CHRT_PDF_NM"]).reset_index(drop=True)

# def aggregate_coding_results(results):
#     results_agg = results.groupby(["LOB","CHRT_PDF_NM",  "DX_CD", "ICD10_DESC"]).agg(list)
#     # results_agg["LLM_EXTRACTED_ENTITY"] = results_agg["LLM_EXTRACTED_ENTITY"].astype(str).str.replace("[", '').str.replace("]", '')
#     results_agg["PAGE"] = results_agg["PAGE"].apply(lambda x:  [ast.literal_eval(str(i)) for i in x])
#     # results_agg["PAGE"] = results_agg["PAGE"].apply(lambda x:  set([l for sublist in [ast.literal_eval(str(i)) for i in x] for l in sublist]))
#     return results_agg

# llm_results_agg = aggregate_coding_results(llm_results)
# llm_codes_found_in_chart_review_agg = aggregate_coding_results(llm_codes_found_in_chart_review)
# # llm_codes_found_in_claims_agg = aggregate_coding_results(llm_codes_found_in_claims)

# llm_results_agg.shape[0]
# llm_results_agg.head(5)

output_path = f"/Workspace/Users/<USER>/Risk/results/{mlflow_run_id}_{mlflow_run_name}_llm_output_processed.zip"
llm_results.to_csv(output_path, index=False)
mlflow.log_artifact(output_path, run_id=mlflow_run_id)
mlflow.log_table(data=llm_results, artifact_file="processed_results.json", run_id=mlflow_run_id)