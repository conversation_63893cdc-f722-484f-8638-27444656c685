@import "src/app/tokens.scss";

.property-1-inclusions_1166-1183 {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.head_924-2279 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: 100%;
}
.frame-929_924-2280 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.stack_924-2281 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  width: 100%;
}
.text-label_924-2282 {
  color: #17181a;
  @include h-1-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.right-corner_924-2283 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.menu_924-2284 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 41px;
  width: 100%;
}
.second-menu_924-2285 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: content-box;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-bottom-width: 1px;
  background: #ffffff;
  width: 100%;
}
.bradcrumb_924-2286 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 40px;
}
.box_924-2287 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  height: 100%;
}
.menu_924-2288 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_924-2289 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-color: $variable-collection-primary-blue;
  border-style: solid;
  border-bottom-width: 1px;
  height: 100%;
}
.menu-item_924-2290 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_924-2291 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_924-2293 {
  color: $variable-collection-primary-blue;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item-_924-2295 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_924-2296 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_924-2297 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_924-2299 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item_924-2301 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_924-2302 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_924-2304 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.frame-916_924-2306 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.frame-942_924-2307 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
}
.check_924-2308 {
  position: relative;
  width: 16px;
  height: 16px;
}

.text-teleheath_924-2309 {
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.frame-936_924-2310 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-939_924-2311 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.frame-940_924-2312 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.text-sys_924-2313 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-inclusion_924-2314 {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: 127px;
  height: 48px;
}
.row_924-2315 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-value_924-2316 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.frame-941_924-2317 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 127px;
}
.text-dias_924-2318 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-inclusion_924-2319 {
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 48px;
  width: 100%;
}
.row_924-2320 {
  padding: 8px 12px 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffff;
  width: 100%;
}
.text-value_924-2321 {
  color: $variable-collection-gray-3;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 100%;
}

.frame-937_924-2322 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 215px;
}
.text-date-of-service_924-2323 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.calendar_924-2324 {
  height: 48px;
  width: 100%;
}

.frame-938_924-2325 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-notes_924-2326 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.notes_924-2327 {
  width: 100%;
}
