<div data-layer="Calendar" class="Calendar" style="width: 1094px; height: 1104px; position: relative; background: white; overflow: hidden">
  <div data-layer="Rectangle 315" class="Rectangle315" style="width: 450px; height: 88.34px; left: 285px; top: 575px; position: absolute; background: #F2F2F2"></div>
  <div data-svg-wrapper data-layer="Rectangle 324" class="Rectangle324" style="left: 232.79px; top: 807.93px; position: absolute">
    <svg width="451" height="238" viewBox="0 0 451 238" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.785156 0.932617H450.785V237.621H0.785156V0.932617Z" fill="#F2F2F2"/>
    </svg>
  </div>
  <div data-layer="Rectangle 314" class="Rectangle314" style="width: 450px; height: 232px; left: 285px; top: 285px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Behavior: Scrolls with page. Interactive in both default and entered state. Allows copy/paste." class="BehaviorScrollsWithPageInteractiveInBothDefaultAndEnteredStateAllowsCopyPaste" style="width: 1039.94px; height: 16px; left: 47px; top: 181px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Behavior: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Scrolls with page. Interactive in both default and entered state. Allows copy/paste.</span></div>
  <div data-layer="Behavior: Scrolls with page. Interactive in both default and entered state. Allows copy/paste." class="BehaviorScrollsWithPageInteractiveInBothDefaultAndEnteredStateAllowsCopyPaste" style="width: 1039.94px; height: 16px; left: 47px; top: 181px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Behavior: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Scrolls with page. Interactive in both default and entered state. Allows copy/paste.</span></div>
  <div data-layer="Entry: Date entry is manual, done by typing or copy/paste." class="EntryDateEntryIsManualDoneByTypingOrCopyPaste" style="width: 1039.94px; height: 16px; left: 47px; top: 224.53px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Entry: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Date entry is manual, done by typing or copy/paste.</span></div>
  <div data-layer="Usage: Type a date of service." class="UsageTypeADateOfService" style="width: 1478.31px; left: 47.83px; top: 137px; position: absolute"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Usage: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; word-wrap: break-word">Type a date of service.</span></div>
  <div data-layer="Calendar" class="Calendar" style="left: 47px; top: 62.80px; position: absolute; color: black; font-size: 36px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Calendar</div>
  <div data-layer="Default: no date entered" class="DefaultNoDateEntered" style="left: 762.58px; top: 330px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Default: no date entered</div>
  <div data-layer="Default: no date entered" class="DefaultNoDateEntered" style="left: 762.58px; top: 399px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Default: no date entered</div>
  <div data-layer="Default: no date entered" class="DefaultNoDateEntered" style="left: 762.58px; top: 330px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Default: no date entered</div>
  <div data-layer="Default: no date entered" class="DefaultNoDateEntered" style="left: 762.58px; top: 399px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Default: no date entered</div>
  <div data-layer="Entered: date entered" class="EnteredDateEntered" style="left: 762.58px; top: 474px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Entered: date entered</div>
  <div data-svg-wrapper data-layer="Vector 7" class="Vector7" style="left: 721.78px; top: 330px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.777344 1H34.96" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 41" class="Vector41" style="left: 721.78px; top: 399px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.777344 1H34.96" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 40" class="Vector40" style="left: 721.78px; top: 330px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.777344 1H34.96" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 42" class="Vector42" style="left: 721.78px; top: 399px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.777344 1H34.96" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 8" class="Vector8" style="left: 721.78px; top: 474px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.777344 1H34.96" stroke="black"/>
    </svg>
  </div>
  <div data-layer="Calendar" class="Calendar" style="width: 450px; height: 232px; left: 285px; top: 285px; position: absolute; overflow: hidden; border-radius: 5px; border: 1px #9747FF solid">
    <div data-layer="Property 1=Default" class="Property1Default" style="width: 410px; padding: 4px; left: 20px; top: 20px; position: absolute; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
      <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
        <div data-layer="Date of Service" class="DateOfService" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Date of Service</div>
        <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 7.99976H20M4 7.99976V16.8C4 17.9201 4 18.4798 4.21799 18.9076C4.40973 19.284 4.71547 19.5902 5.0918 19.782C5.5192 19.9998 6.07899 19.9998 7.19691 19.9998H16.8031C17.921 19.9998 18.48 19.9998 18.9074 19.782C19.2837 19.5902 19.5905 19.284 19.7822 18.9076C20 18.4802 20 17.9212 20 16.8033V7.99976M4 7.99976V7.19995C4 6.07985 4 5.51938 4.21799 5.09155C4.40973 4.71523 4.71547 4.40949 5.0918 4.21774C5.51962 3.99976 6.08009 3.99976 7.2002 3.99976H8M20 7.99976V7.19667C20 6.07875 20 5.51896 19.7822 5.09155C19.5905 4.71523 19.2837 4.40949 18.9074 4.21774C18.4796 3.99976 17.9203 3.99976 16.8002 3.99976H16M16 1.99976V3.99976M16 3.99976H8M8 1.99976V3.99976" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
    </div>
    <div data-layer="Property 1=Active" class="Property1Active" style="width: 410px; padding: 4px; left: 20px; top: 92px; position: absolute; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-3, #547996) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
      <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
        <div data-layer="07/21/" class="21" style="flex: 1 1 0; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">07/21/</div>
        <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 7.99976H20M4 7.99976V16.8C4 17.9201 4 18.4798 4.21799 18.9076C4.40973 19.284 4.71547 19.5902 5.0918 19.782C5.5192 19.9998 6.07899 19.9998 7.19691 19.9998H16.8031C17.921 19.9998 18.48 19.9998 18.9074 19.782C19.2837 19.5902 19.5905 19.284 19.7822 18.9076C20 18.4802 20 17.9212 20 16.8033V7.99976M4 7.99976V7.19995C4 6.07985 4 5.51938 4.21799 5.09155C4.40973 4.71523 4.71547 4.40949 5.0918 4.21774C5.51962 3.99976 6.08009 3.99976 7.2002 3.99976H8M20 7.99976V7.19667C20 6.07875 20 5.51896 19.7822 5.09155C19.5905 4.71523 19.2837 4.40949 18.9074 4.21774C18.4796 3.99976 17.9203 3.99976 16.8002 3.99976H16M16 1.99976V3.99976M16 3.99976H8M8 1.99976V3.99976" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
    </div>
    <div data-layer="Property 1=Entered" class="Property1Entered" style="width: 410px; padding: 4px; left: 20px; top: 164px; position: absolute; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
      <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
        <div data-layer="07/21/24" class="2124" style="flex: 1 1 0; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">07/21/24</div>
        <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 7.99976H20M4 7.99976V16.8C4 17.9201 4 18.4798 4.21799 18.9076C4.40973 19.284 4.71547 19.5902 5.0918 19.782C5.5192 19.9998 6.07899 19.9998 7.19691 19.9998H16.8031C17.921 19.9998 18.48 19.9998 18.9074 19.782C19.2837 19.5902 19.5905 19.284 19.7822 18.9076C20 18.4802 20 17.9212 20 16.8033V7.99976M4 7.99976V7.19995C4 6.07985 4 5.51938 4.21799 5.09155C4.40973 4.71523 4.71547 4.40949 5.0918 4.21774C5.51962 3.99976 6.08009 3.99976 7.2002 3.99976H8M20 7.99976V7.19667C20 6.07875 20 5.51896 19.7822 5.09155C19.5905 4.71523 19.2837 4.40949 18.9074 4.21774C18.4796 3.99976 17.9203 3.99976 16.8002 3.99976H16M16 1.99976V3.99976M16 3.99976H8M8 1.99976V3.99976" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Calendar" class="Calendar" style="width: 410px; padding: 4px; left: 251.58px; top: 902.93px; position: absolute; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-3, #547996) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
    <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
      <div data-layer="07/21/24" class="2124" style="flex: 1 1 0; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">07/21/24</div>
      <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4.58398 8.93262H20.584M4.58398 8.93262V17.7328C4.58398 18.8529 4.58398 19.4127 4.80197 19.8405C4.99372 20.2168 5.29946 20.5231 5.67578 20.7148C6.10319 20.9326 6.66298 20.9326 7.78089 20.9326H17.3871C18.505 20.9326 19.064 20.9326 19.4914 20.7148C19.8677 20.5231 20.1745 20.2168 20.3662 19.8405C20.584 19.4131 20.584 18.8541 20.584 17.7362V8.93262M4.58398 8.93262V8.13281C4.58398 7.01271 4.58398 6.45224 4.80197 6.02441C4.99372 5.64809 5.29946 5.34235 5.67578 5.1506C6.1036 4.93262 6.66407 4.93262 7.78418 4.93262H8.58398M20.584 8.93262V8.12953C20.584 7.01161 20.584 6.45182 20.3662 6.02441C20.1745 5.64809 19.8677 5.34235 19.4914 5.1506C19.0636 4.93262 18.5043 4.93262 17.3842 4.93262H16.584M16.584 2.93262V4.93262M16.584 4.93262H8.58398M8.58398 2.93262V4.93262" stroke="var(--text-black, #17181A)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
  </div>
  <div data-layer="Calendar" class="Calendar" style="width: 410px; padding: 4px; left: 305px; top: 594.51px; position: absolute; background: white; overflow: hidden; border-radius: 10px; outline: 1px #D9E1E7 solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
    <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
      <div data-layer="Date of Service" class="DateOfService" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Date of Service</div>
      <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 8.5061H20M4 8.5061V17.3063C4 18.4264 4 18.9862 4.21799 19.414C4.40973 19.7903 4.71547 20.0966 5.0918 20.2883C5.5192 20.5061 6.07899 20.5061 7.19691 20.5061H16.8031C17.921 20.5061 18.48 20.5061 18.9074 20.2883C19.2837 20.0966 19.5905 19.7903 19.7822 19.414C20 18.9866 20 18.4276 20 17.3097V8.5061M4 8.5061V7.7063C4 6.58619 4 6.02572 4.21799 5.5979C4.40973 5.22158 4.71547 4.91584 5.0918 4.72409C5.51962 4.5061 6.08009 4.5061 7.2002 4.5061H8M20 8.5061V7.70301C20 6.5851 20 6.02531 19.7822 5.5979C19.5905 5.22158 19.2837 4.91584 18.9074 4.72409C18.4796 4.5061 17.9203 4.5061 16.8002 4.5061H16M16 2.5061V4.5061M16 4.5061H8M8 2.5061V4.5061" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
  </div>
  <div data-layer="Calendar" class="Calendar" style="width: 410px; padding: 4px; left: 251.58px; top: 830.93px; position: absolute; background: white; overflow: hidden; border-radius: 10px; outline: 1px #D9E1E7 solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
    <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
      <div data-layer="Date of Service" class="DateOfService" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Date of Service</div>
      <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4.58398 8.93262H20.584M4.58398 8.93262V17.7328C4.58398 18.8529 4.58398 19.4127 4.80197 19.8405C4.99372 20.2168 5.29946 20.5231 5.67578 20.7148C6.10319 20.9326 6.66298 20.9326 7.78089 20.9326H17.3871C18.505 20.9326 19.064 20.9326 19.4914 20.7148C19.8677 20.5231 20.1745 20.2168 20.3662 19.8405C20.584 19.4131 20.584 18.8541 20.584 17.7362V8.93262M4.58398 8.93262V8.13281C4.58398 7.01271 4.58398 6.45224 4.80197 6.02441C4.99372 5.64809 5.29946 5.34235 5.67578 5.1506C6.1036 4.93262 6.66407 4.93262 7.78418 4.93262H8.58398M20.584 8.93262V8.12953C20.584 7.01161 20.584 6.45182 20.3662 6.02441C20.1745 5.64809 19.8677 5.34235 19.4914 5.1506C19.0636 4.93262 18.5043 4.93262 17.3842 4.93262H16.584M16.584 2.93262V4.93262M16.584 4.93262H8.58398M8.58398 2.93262V4.93262" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
  </div>
  <div data-layer="Calendar" class="Calendar" style="width: 410px; padding: 4px; left: 251.58px; top: 974.93px; position: absolute; background: white; overflow: hidden; border-radius: 10px; outline: 1px #D9E1E7 solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
    <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
      <div data-layer="Date of Service" class="DateOfService" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Date of Service</div>
      <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4.58398 8.93262H20.584M4.58398 8.93262V17.7328C4.58398 18.8529 4.58398 19.4127 4.80197 19.8405C4.99372 20.2168 5.29946 20.5231 5.67578 20.7148C6.10319 20.9326 6.66298 20.9326 7.78089 20.9326H17.3871C18.505 20.9326 19.064 20.9326 19.4914 20.7148C19.8677 20.5231 20.1745 20.2168 20.3662 19.8405C20.584 19.4131 20.584 18.8541 20.584 17.7362V8.93262M4.58398 8.93262V8.13281C4.58398 7.01271 4.58398 6.45224 4.80197 6.02441C4.99372 5.64809 5.29946 5.34235 5.67578 5.1506C6.1036 4.93262 6.66407 4.93262 7.78418 4.93262H8.58398M20.584 8.93262V8.12953C20.584 7.01161 20.584 6.45182 20.3662 6.02441C20.1745 5.64809 19.8677 5.34235 19.4914 5.1506C19.0636 4.93262 18.5043 4.93262 17.3842 4.93262H16.584M16.584 2.93262V4.93262M16.584 4.93262H8.58398M8.58398 2.93262V4.93262" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
  </div>
  <div data-layer="Spacing:" class="Spacing" style="width: 83px; height: 15.69px; left: 64.37px; top: 534.32px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Spacing:</div>
  <div data-layer="Styling:" class="Styling" style="width: 83px; height: 15.69px; left: 71.48px; top: 760.89px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Styling:</div>
  <div data-layer="Rectangle 322" class="Rectangle322" style="width: 4px; height: 413.38px; left: 301.62px; top: 598.51px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="4px" class="Px" style="left: 258.84px; top: 593.40px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">4px</div>
  <div data-svg-wrapper data-layer="Rectangle 319" class="Rectangle319" style="left: 711px; top: 594.51px; position: absolute">
    <svg width="4" height="49" viewBox="0 0 4 49" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="0.506348" width="4" height="48" fill="#007D00" fill-opacity="0.25"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 280.84px; top: 594.57px; position: absolute">
    <svg width="21" height="6" viewBox="0 0 21 6" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.6184 0.572998L12.3382 0.572998V4.573L20.6184 4.573M12.26 2.573L0.837891 2.573" stroke="black"/>
    </svg>
  </div>
  <div data-layer="Rectangle 323" class="Rectangle323" style="width: 8px; height: 413.38px; left: 301.62px; top: 606.51px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 280.84px; top: 598.57px; position: absolute">
    <svg width="21" height="10" viewBox="0 0 21 10" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.6184 0.572998L12.3382 0.572998L12.3382 8.573L20.6184 8.573M12.26 4.573L0.837891 4.573" stroke="black"/>
    </svg>
  </div>
  <div data-layer="8px" class="Px" style="left: 258.84px; top: 603.57px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">8px</div>
  <div data-layer="Rectangle 316" class="Rectangle316" style="width: 8px; height: 413.38px; left: 301.62px; top: 638.51px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 317" class="Rectangle317" style="width: 4px; height: 413.38px; left: 301.62px; top: 642.51px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 301.80px; top: 643.56px; position: absolute">
    <svg width="6" height="21" viewBox="0 0 6 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.798827 0.56372L0.798828 8.84397L4.79883 8.84397L4.79883 0.56372M2.79883 8.92216L2.79883 20.3442" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 305.80px; top: 643.56px; position: absolute">
    <svg width="14" height="21" viewBox="0 0 14 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.798827 0.56372L0.798828 8.84397L12.7988 8.84397L12.7988 0.56372M6.79883 8.92216L6.79883 20.3442" stroke="black"/>
    </svg>
  </div>
  <div data-layer="4px" class="Px" style="left: 284.71px; top: 668.39px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">4px</div>
  <div data-layer="12px" class="Px" style="left: 310.74px; top: 668.39px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12px</div>
  <div data-svg-wrapper data-layer="Rectangle 320" class="Rectangle320" style="left: 305.62px; top: 598.51px; position: absolute">
    <svg width="13" height="41" viewBox="0 0 13 41" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.617188" y="0.506348" width="12" height="40" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 321" class="Rectangle321" style="left: 699px; top: 598.51px; position: absolute">
    <svg width="12" height="41" viewBox="0 0 12 41" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="0.506348" width="12" height="40" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 318" class="Rectangle318" style="left: 301.62px; top: 594.51px; position: absolute">
    <svg width="5" height="49" viewBox="0 0 5 49" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.617188" y="0.506348" width="4" height="48" fill="#007D00" fill-opacity="0.25"/>
    </svg>
  </div>
  <div data-layer="corner-radius: 10 border-style: solid  border-color: gray-2" class="CornerRadius10BorderStyleSolidBorderColorGray2" style="left: 886.93px; top: 807.93px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">corner-radius: 10<br/>border-style: solid  border-color: gray-2</div>
  <div data-layer="corner-radius: 10 border-style: solid  border-color: gray-3" class="CornerRadius10BorderStyleSolidBorderColorGray3" style="left: 886.93px; top: 895.93px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">corner-radius: 10<br/>border-style: solid  border-color: gray-3</div>
  <div data-layer="corner-radius: 10 border-style: solid  border-color: gray-3" class="CornerRadius10BorderStyleSolidBorderColorGray3" style="left: 886.93px; top: 967.93px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">corner-radius: 10<br/>border-style: solid  border-color: gray-3</div>
  <div data-layer="Font: Input text Color: gray-3" class="FontInputTextColorGray3" style="left: 126.01px; top: 838.79px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: Input text<br/>Color: gray-3</div>
  <div data-layer="Font: Input text Color: gray-3" class="FontInputTextColorGray3" style="left: 126.01px; top: 981.87px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: Input text<br/>Color: gray-3</div>
  <div data-layer="Font: Input text Color: text-black" class="FontInputTextColorTextBlack" style="left: 126.01px; top: 913.93px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Font: Input text<br/>Color: text-black</div>
  <div data-layer="Component: icon_down Color: text-black (when active)" class="ComponentIconDownColorTextBlackWhenActive" style="left: 684.36px; top: 914.93px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Component: icon_down<br/>Color: text-black (when active)</div>
  <div data-layer="Component: icon_right Color: gray 3" class="ComponentIconRightColorGray3" style="left: 684.36px; top: 847.93px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Component: icon_right<br/>Color: gray 3</div>
  <div data-layer="Component: icon_right Color: gray 3" class="ComponentIconRightColorGray3" style="left: 684.36px; top: 986.93px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Component: icon_right<br/>Color: gray 3</div>
  <div data-svg-wrapper data-layer="Vector 36" class="Vector36" style="left: 230.06px; top: 854.98px; position: absolute">
    <svg width="25" height="2" viewBox="0 0 25 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.0605469 0.976562L24.4619 0.976565" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 44" class="Vector44" style="left: 230.06px; top: 998.06px; position: absolute">
    <svg width="25" height="2" viewBox="0 0 25 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.0605469 1.05688L24.4619 1.05689" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 37" class="Vector37" style="left: 230.06px; top: 930.12px; position: absolute">
    <svg width="25" height="2" viewBox="0 0 25 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.0605469 1.12354L24.4619 1.12354" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 38" class="Vector38" style="left: 652.60px; top: 926.93px; position: absolute">
    <svg width="25" height="2" viewBox="0 0 25 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.595703 0.932861L24.9971 0.932863" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 39" class="Vector39" style="left: 652.60px; top: 859.93px; position: absolute">
    <svg width="25" height="2" viewBox="0 0 25 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.595703 0.932861L24.9971 0.932863" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 43" class="Vector43" style="left: 652.60px; top: 998.93px; position: absolute">
    <svg width="25" height="2" viewBox="0 0 25 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.595703 0.932861L24.9971 0.932863" stroke="black"/>
    </svg>
  </div>
</div>