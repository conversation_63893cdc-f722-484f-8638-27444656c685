# PDF Viewer Implementation Plan

## Overview

This document outlines the implementation plan for the PDF viewer component with advanced search functionality for the Clinical Quality UI project. The PDF viewer is a critical component that will allow users to view medical charts and perform advanced text searches across all pages of the document.

## PDF.js Library Recommendation

After evaluating the available options for PDF viewing in Angular applications, here are the recommended libraries:

1. **ngx-extended-pdf-viewer** (Recommended)
   - Pros:
     - Full-featured wrapper around Mozilla's PDF.js
     - Includes built-in search functionality
     - Active maintenance and good Angular integration
     - Supports text layer for text selection and search
     - Customizable UI
   - Cons:
     - Larger bundle size due to comprehensive features

2. **ng2-pdf-viewer** (Alternative)
   - Pros:
     - Lightweight wrapper around PDF.js
     - Smaller bundle size
     - Simple API
   - Cons:
     - Less built-in functionality
     - Would require more custom implementation for advanced search

3. **PDF.js directly** (Alternative for maximum customization)
   - Pros:
     - Complete control over implementation
     - No dependencies on third-party Angular wrappers
   - Cons:
     - Requires more development effort
     - Need to handle Angular integration manually

**Recommendation**: Use **ngx-extended-pdf-viewer** for its comprehensive features and built-in search capabilities that align with our requirements.

## Implementation Architecture

```mermaid
graph TD
    A[PDF Viewer Component] --> B[PDF Service]
    A --> C[Search Component]
    C --> D[Search Service]
    B --> E[PDF.js Library]
    D --> E
    B --> F[Storage Service]
    D --> F
    A --> G[Annotation Component]
    G --> H[Annotation Service]
    H --> F
```

## Component Structure

1. **PDF Viewer Component**
   - Container component for the PDF viewer
   - Manages the layout and integration of sub-components
   - Handles PDF loading and rendering

2. **Search Component**
   - Provides UI for search input and options
   - Displays search results and navigation controls
   - Communicates with Search Service

3. **Annotation Component**
   - Manages highlighting and annotations on the PDF
   - Provides UI for adding/editing annotations
   - Communicates with Annotation Service

## Service Structure

1. **PDF Service**
   - Handles PDF loading and rendering
   - Manages PDF.js integration
   - Provides methods for page navigation and zoom

2. **Search Service**
   - Implements advanced search functionality
   - Manages search state (current match, total matches)
   - Handles search across all pages

3. **Annotation Service**
   - Manages annotation data
   - Provides methods for creating/editing annotations
   - Handles persistence of annotations

## Implementation Steps

### 1. Set Up PDF Viewer Component

1. Install the recommended PDF.js library:
   ```bash
   npm install ngx-extended-pdf-viewer
   ```

2. Update the PDF Viewer Component:
   ```typescript
   // pdf-viewer.component.ts
   import { Component, Input, OnInit } from '@angular/core';
   import { PdfService } from '../../services/pdf.service';

   @Component({
     selector: 'app-pdf-viewer',
     templateUrl: './pdf-viewer.component.html',
     styleUrls: ['./pdf-viewer.component.scss']
   })
   export class PdfViewerComponent implements OnInit {
     @Input() pdfUrl: string;
     pdfDocument: any;
     currentPage = 1;
     totalPages = 0;
     zoom = 1.0;

     constructor(private pdfService: PdfService) {}

     ngOnInit(): void {
       if (this.pdfUrl) {
         this.loadPdf();
       }
     }

     loadPdf(): void {
       this.pdfService.loadPdf(this.pdfUrl).subscribe(
         (pdf) => {
           this.pdfDocument = pdf;
           this.totalPages = pdf.numPages;
         },
         (error) => console.error('Error loading PDF:', error)
       );
     }

     // Methods for page navigation, zoom, etc.
   }
   ```

3. Create the PDF Viewer Template:
   ```html
   <!-- pdf-viewer.component.html -->
   <div class="pdf-container">
     <div class="toolbar">
       <button (click)="previousPage()" [disabled]="currentPage === 1">Previous</button>
       <span>Page {{ currentPage }} of {{ totalPages }}</span>
       <button (click)="nextPage()" [disabled]="currentPage === totalPages">Next</button>
       <button (click)="zoomIn()">Zoom In</button>
       <button (click)="zoomOut()">Zoom Out</button>
     </div>
     
     <ngx-extended-pdf-viewer
       [src]="pdfUrl"
       [page]="currentPage"
       [zoom]="zoom"
       [showToolbar]="false"
       [showSidebarButton]="false"
       [showFindButton]="false"
       (pageChange)="currentPage = $event"
       (pdfLoaded)="onPdfLoaded($event)">
     </ngx-extended-pdf-viewer>
     
     <app-search *ngIf="pdfDocument"></app-search>
   </div>
   ```

### 2. Implement PDF Service

```typescript
// pdf.service.ts
import { Injectable } from '@angular/core';
import { Observable, from } from 'rxjs';
import { map } from 'rxjs/operators';
import * as pdfjsLib from 'pdfjs-dist';

@Injectable({
  providedIn: 'root'
})
export class PdfService {
  constructor() {
    // Set the worker source
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'assets/pdf.worker.js';
  }

  loadPdf(url: string): Observable<any> {
    return from(pdfjsLib.getDocument(url).promise);
  }

  getPage(pdf: any, pageNumber: number): Observable<any> {
    return from(pdf.getPage(pageNumber));
  }

  getTextContent(page: any): Observable<any> {
    return from(page.getTextContent());
  }

  // Additional methods for PDF manipulation
}
```

### 3. Implement Advanced Search Functionality

1. Create Search Component:
   ```typescript
   // search.component.ts
   import { Component, Input } from '@angular/core';
   import { SearchService } from '../../services/search.service';

   @Component({
     selector: 'app-search',
     templateUrl: './search.component.html',
     styleUrls: ['./search.component.scss']
   })
   export class SearchComponent {
     @Input() pdfDocument: any;
     searchText = '';
     caseSensitive = false;
     wholeWord = false;
     currentMatch = 0;
     totalMatches = 0;
     isSearching = false;

     constructor(private searchService: SearchService) {}

     search(): void {
       if (!this.searchText.trim()) return;
       
       this.isSearching = true;
       this.searchService.search(
         this.pdfDocument, 
         this.searchText, 
         {
           caseSensitive: this.caseSensitive,
           wholeWord: this.wholeWord
         }
       ).subscribe(
         (results) => {
           this.totalMatches = results.length;
           this.currentMatch = results.length > 0 ? 1 : 0;
           this.isSearching = false;
         },
         (error) => {
           console.error('Search error:', error);
           this.isSearching = false;
         }
       );
     }

     nextMatch(): void {
       this.searchService.navigateToMatch(this.currentMatch + 1);
       this.currentMatch++;
     }

     previousMatch(): void {
       this.searchService.navigateToMatch(this.currentMatch - 1);
       this.currentMatch--;
     }
   }
   ```

2. Create Search Service:
   ```typescript
   // search.service.ts
   import { Injectable } from '@angular/core';
   import { Observable, from, forkJoin } from 'rxjs';
   import { map, mergeMap, reduce } from 'rxjs/operators';
   import { PdfService } from './pdf.service';

   export interface SearchOptions {
     caseSensitive?: boolean;
     wholeWord?: boolean;
   }

   export interface SearchMatch {
     pageNumber: number;
     matchIndex: number;
     text: string;
     position: {
       left: number;
       top: number;
       right: number;
       bottom: number;
     };
   }

   @Injectable({
     providedIn: 'root'
   })
   export class SearchService {
     private matches: SearchMatch[] = [];
     private currentMatchIndex = -1;

     constructor(private pdfService: PdfService) {}

     search(pdf: any, searchText: string, options: SearchOptions = {}): Observable<SearchMatch[]> {
       this.matches = [];
       this.currentMatchIndex = -1;
       
       // Create an array of page numbers
       const pageNumbers = Array.from({ length: pdf.numPages }, (_, i) => i + 1);
       
       // Search each page
       return from(pageNumbers).pipe(
         mergeMap(pageNumber => this.searchPage(pdf, pageNumber, searchText, options)),
         reduce((allMatches, pageMatches) => [...allMatches, ...pageMatches], [] as SearchMatch[]),
         map(matches => {
           this.matches = matches;
           return matches;
         })
       );
     }

     private searchPage(pdf: any, pageNumber: number, searchText: string, options: SearchOptions): Observable<SearchMatch[]> {
       return this.pdfService.getPage(pdf, pageNumber).pipe(
         mergeMap(page => this.pdfService.getTextContent(page)),
         map(textContent => {
           const pageMatches: SearchMatch[] = [];
           const text = textContent.items.map((item: any) => item.str).join(' ');
           
           let searchRegex: RegExp;
           if (options.wholeWord) {
             const pattern = `\\b${this.escapeRegExp(searchText)}\\b`;
             searchRegex = options.caseSensitive ? new RegExp(pattern, 'g') : new RegExp(pattern, 'gi');
           } else {
             searchRegex = options.caseSensitive ? new RegExp(this.escapeRegExp(searchText), 'g') : new RegExp(this.escapeRegExp(searchText), 'gi');
           }
           
           let match;
           while ((match = searchRegex.exec(text)) !== null) {
             // For simplicity, we're not calculating exact positions here
             // In a real implementation, you would map the match to the exact position on the page
             pageMatches.push({
               pageNumber,
               matchIndex: match.index,
               text: match[0],
               position: { left: 0, top: 0, right: 0, bottom: 0 }
             });
           }
           
           return pageMatches;
         })
       );
     }

     navigateToMatch(index: number): void {
       if (index < 1 || index > this.matches.length) return;
       
       this.currentMatchIndex = index - 1;
       const match = this.matches[this.currentMatchIndex];
       
       // Navigate to the page and highlight the match
       // This would be implemented based on the PDF viewer library being used
     }

     private escapeRegExp(string: string): string {
       return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
     }
   }
   ```

### 4. Implement Storage for Search Results

1. Update the Storage Service to handle search results:
   ```typescript
   // storage.service.ts
   import { Injectable } from '@angular/core';
   import { openDB, DBSchema, IDBPDatabase } from 'idb';
   import { Observable, from } from 'rxjs';
   import { map } from 'rxjs/operators';
   import { v4 as uuidv4 } from 'uuid';

   interface SearchHistoryItem {
     id: string;
     chartId: string;
     searchText: string;
     timestamp: Date;
     options: {
       caseSensitive: boolean;
       wholeWord: boolean;
     };
   }

   interface ClinicalQualityDB extends DBSchema {
     'search_history': {
       key: string;
       value: SearchHistoryItem;
       indexes: { 'by_chart_id': string };
     };
     // Other object stores would be defined here
   }

   @Injectable({
     providedIn: 'root'
   })
   export class StorageService {
     private db: Promise<IDBPDatabase<ClinicalQualityDB>>;

     constructor() {
       this.initDatabase();
     }

     private async initDatabase() {
       this.db = openDB<ClinicalQualityDB>('clinical-quality-db', 1, {
         upgrade(db) {
           // Create search history store
           const searchHistoryStore = db.createObjectStore('search_history', { keyPath: 'id' });
           searchHistoryStore.createIndex('by_chart_id', 'chartId');
           
           // Other object stores would be created here
         }
       });
     }

     saveSearchHistory(chartId: string, searchText: string, options: any): Observable<string> {
       const searchHistory: SearchHistoryItem = {
         id: uuidv4(),
         chartId,
         searchText,
         timestamp: new Date(),
         options
       };

       return from(this.db.then(db => 
         db.add('search_history', searchHistory)
       )).pipe(
         map(() => searchHistory.id)
       );
     }

     getSearchHistoryByChart(chartId: string): Observable<SearchHistoryItem[]> {
       return from(this.db.then(async db => {
         const index = db.transaction('search_history').store.index('by_chart_id');
         return index.getAll(chartId);
       }));
     }

     // Additional methods for other data entities
   }
   ```

### 5. Implement Performance Optimizations for Large PDFs

1. Add pagination and lazy loading to the PDF Service:
   ```typescript
   // Add to pdf.service.ts
   
   // Cache for text content to avoid re-extracting
   private textContentCache = new Map<number, any>();
   
   // Get text content with caching
   getTextContentCached(pdf: any, pageNumber: number): Observable<any> {
     if (this.textContentCache.has(pageNumber)) {
       return new Observable(observer => {
         observer.next(this.textContentCache.get(pageNumber));
         observer.complete();
       });
     }
     
     return this.getPage(pdf, pageNumber).pipe(
       mergeMap(page => this.getTextContent(page)),
       map(textContent => {
         this.textContentCache.set(pageNumber, textContent);
         return textContent;
       })
     );
   }
   
   // Clear cache when loading a new PDF
   clearCache(): void {
     this.textContentCache.clear();
   }
   
   // Load PDF in chunks for large documents
   loadPdfInChunks(url: string, chunkSize: number = 10): Observable<any> {
     return from(pdfjsLib.getDocument(url).promise).pipe(
       map(pdf => {
         // Only load the first chunk of pages initially
         for (let i = 1; i <= Math.min(chunkSize, pdf.numPages); i++) {
           this.getPage(pdf, i).subscribe();
         }
         return pdf;
       })
     );
   }
   ```

2. Implement progressive search for large documents:
   ```typescript
   // Add to search.service.ts
   
   // Search with progress reporting
   searchWithProgress(pdf: any, searchText: string, options: SearchOptions = {}): Observable<{
     matches: SearchMatch[],
     progress: number,
     isComplete: boolean
   }> {
     this.matches = [];
     this.currentMatchIndex = -1;
     
     const totalPages = pdf.numPages;
     let processedPages = 0;
     
     return new Observable(observer => {
       // Process pages in batches of 5
       const batchSize = 5;
       let currentBatch = 1;
       
       const processNextBatch = () => {
         const endPage = Math.min(currentBatch * batchSize, totalPages);
         const startPage = (currentBatch - 1) * batchSize + 1;
         
         // Create array of page numbers for this batch
         const pageNumbers = Array.from(
           { length: endPage - startPage + 1 }, 
           (_, i) => startPage + i
         );
         
         // Process each page in the batch
         const pageObservables = pageNumbers.map(pageNumber => 
           this.searchPage(pdf, pageNumber, searchText, options)
         );
         
         forkJoin(pageObservables).subscribe(
           batchResults => {
             // Flatten batch results and add to matches
             const newMatches = batchResults.flat();
             this.matches = [...this.matches, ...newMatches];
             
             // Update progress
             processedPages += pageNumbers.length;
             const progress = processedPages / totalPages;
             
             // Emit current state
             observer.next({
               matches: this.matches,
               progress,
               isComplete: processedPages >= totalPages
             });
             
             // Process next batch or complete
             if (processedPages < totalPages) {
               currentBatch++;
               setTimeout(processNextBatch, 0); // Use setTimeout to avoid blocking UI
             } else {
               observer.complete();
             }
           },
           error => observer.error(error)
         );
       };
       
       // Start processing
       processNextBatch();
     });
   }
   ```

## Integration with Chart Review Page

Update the Chart Review Page Component to integrate the PDF viewer:

```typescript
// chart-review-page.component.ts
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-chart-review-page',
  templateUrl: './chart-review-page.component.html',
  styleUrls: ['./chart-review-page.component.scss']
})
export class ChartReviewPageComponent implements OnInit {
  chartId: string;
  pdfUrl: string;
  
  constructor(private route: ActivatedRoute) {}
  
  ngOnInit(): void {
    this.chartId = this.route.snapshot.paramMap.get('id') || '';
    // In a real app, you would fetch the chart data from a service
    // For now, we'll use a sample PDF
    this.pdfUrl = 'assets/sample-chart.pdf';
  }
}
```

```html
<!-- chart-review-page.component.html -->
<div class="chart-review-container">
  <div class="sidebar">
    <!-- AI findings would go here -->
    <h2>AI Findings</h2>
    <div class="findings-list">
      <!-- Placeholder for findings -->
    </div>
  </div>
  
  <div class="main-content">
    <app-pdf-viewer [pdfUrl]="pdfUrl"></app-pdf-viewer>
  </div>
</div>
```

## Testing Strategy

1. **Unit Tests**:
   - Test PDF Service methods
   - Test Search Service algorithms
   - Test component logic

2. **Integration Tests**:
   - Test PDF loading and rendering
   - Test search functionality across pages
   - Test navigation between search results

3. **Performance Tests**:
   - Test with large PDFs (100+ pages)
   - Measure search response time
   - Test memory usage during search operations

## Next Steps

1. Implement the PDF Viewer Component
2. Implement the PDF Service
3. Implement the Search Component and Service
4. Integrate with Storage Service
5. Implement performance optimizations
6. Add unit and integration tests
7. Integrate with Chart Review Page

## Conclusion

This implementation plan provides a comprehensive approach to building the PDF viewer with advanced search functionality. The plan focuses on using ngx-extended-pdf-viewer as the base library while implementing custom search functionality to meet the specific requirements outlined in the architecture document.

The implementation is designed to be performant even with large PDFs and includes features for searching across all pages, highlighting matches, and navigating between search results. The plan also includes integration with the storage service to persist search history and results.