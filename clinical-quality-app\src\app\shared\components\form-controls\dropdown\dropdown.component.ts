import { Component, Input, Output, EventEmitter, forwardRef, OnInit, HostListener, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

export interface DropdownOption {
  value: any;
  label: string;
  disabled?: boolean;
}

@Component({
  selector: 'app-dropdown',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dropdown.component.html',
  styleUrls: ['./dropdown.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DropdownComponent),
      multi: true
    }
  ]
})
export class DropdownComponent implements ControlValueAccessor, OnInit {
  @Input() options: DropdownOption[] = [];
  @Input() placeholder: string = 'Select';
  @Input() disabled: boolean = false;
  @Input() multiSelect: boolean = false;
  @Input() label: string = '';
  @Input() required: boolean = false;
  @Input() errorMessage: string = '';
  @Input() id: string = '';
  @Input() name: string = '';

  @Output() selectionChange = new EventEmitter<any>();

  @ViewChild('dropdownContainer', { static: true }) dropdownContainer!: ElementRef;

  isOpen: boolean = false;
  selectedValues: any[] = [];
  selectedValue: any = null;

  // ControlValueAccessor implementation
  onChange: any = () => {};
  onTouched: any = () => {};

  ngOnInit(): void {
    if (!this.id) {
      this.id = 'dropdown-' + Math.random().toString(36).substring(2, 9);
    }
  }

  writeValue(value: any): void {
    if (this.multiSelect) {
      this.selectedValues = Array.isArray(value) ? value : [];
    } else {
      this.selectedValue = value;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  toggleDropdown(): void {
    if (!this.disabled) {
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        this.onTouched();
      }
    }
  }

  selectOption(option: DropdownOption): void {
    if (option.disabled) return;

    if (this.multiSelect) {
      const index = this.selectedValues.findIndex(val => val === option.value);
      if (index > -1) {
        this.selectedValues.splice(index, 1);
      } else {
        this.selectedValues.push(option.value);
      }
      this.onChange([...this.selectedValues]);
      this.selectionChange.emit([...this.selectedValues]);
    } else {
      this.selectedValue = option.value;
      this.isOpen = false;
      this.onChange(this.selectedValue);
      this.selectionChange.emit(this.selectedValue);
    }
  }

  isSelected(option: DropdownOption): boolean {
    if (this.multiSelect) {
      return this.selectedValues.includes(option.value);
    } else {
      return this.selectedValue === option.value;
    }
  }

  getDisplayText(): string {
    if (this.multiSelect) {
      if (this.selectedValues.length === 0) {
        return this.placeholder;
      } else if (this.selectedValues.length === 1) {
        const option = this.options.find(opt => opt.value === this.selectedValues[0]);
        return option ? option.label : this.placeholder;
      } else {
        return `${this.selectedValues.length} selected`;
      }
    } else {
      if (this.selectedValue !== null && this.selectedValue !== undefined) {
        const option = this.options.find(opt => opt.value === this.selectedValue);
        return option ? option.label : this.placeholder;
      }
      return this.placeholder;
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (!this.dropdownContainer.nativeElement.contains(event.target as Node)) {
      this.isOpen = false;
    }
  }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (this.disabled) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        this.toggleDropdown();
        break;
      case 'Escape':
        this.isOpen = false;
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!this.isOpen) {
          this.isOpen = true;
        }
        // TODO: Add keyboard navigation through options
        break;
      case 'ArrowUp':
        event.preventDefault();
        // TODO: Add keyboard navigation through options
        break;
    }
  }

  trackByValue(index: number, option: DropdownOption): any {
    return option.value;
  }
}
