import { Component, OnInit, ChangeDetectorRef, ChangeDetectionStrategy, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { AssignedTableComponent } from '../../components/assigned-table/assigned-table.component';
import { MenuComponent, UserProfile, MenuItem } from '../../../../shared/components/menu/menu.component';
import { ButtonComponent } from '../../../../shared/components/buttons/button.component';
import { RefreshIconComponent } from '../../../../shared/components/icons/refresh-icon.component';
import { CsvDataService } from '../../../../core/data/services/csv-data.service';
import { AssignedChart, DataLoadingState } from '../../../../core/data/models/chart-data.models';
import { PageTitleComponent } from '@shared/components/page-title/page-title.component';

@Component({
  selector: 'app-dashboard-page',
  standalone: true,
  imports: [
    CommonModule,
    AssignedTableComponent,
    MenuComponent,
    ButtonComponent,
    RefreshIconComponent,
    PageTitleComponent
  ],
  templateUrl: './dashboard-page.component.html',
  styleUrls: ['./dashboard-page.component.scss'],
  // Try to prevent duplicate instances by using OnPush change detection
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardPageComponent implements OnInit, OnDestroy {
  // Data properties
  assignedCharts: AssignedChart[] = [];
  loadingState: DataLoadingState = {
    loading: false,
    error: null,
    lastUpdated: null
  };

  // Subscription management
  private destroy$ = new Subject<void>();

  // Search text for assigned charts
  assignedSearchText = '';

  // Navigation data
  userProfile: UserProfile = {
    name: 'Jane Chu',
    avatar: ''
  };

  menuItems: MenuItem[] = [
    { label: 'Dashboard', route: '/dashboard', icon: '🏠' },
    { label: 'Profile', route: '/profile', icon: '👤' },
    { label: 'Settings', route: '/settings', icon: '⚙️' },
    { label: 'Help', route: '/help', icon: '❓' },
    { label: 'Logout', action: () => this.logout(), icon: '🚪' }
  ];

  // Add a unique identifier to help debug multiple instances
  public instanceId = Math.random().toString(36).substring(2, 8);

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private cdRef: ChangeDetectorRef,
    private csvDataService: CsvDataService
  ) {
    console.log(`DashboardPageComponent created with ID: ${this.instanceId}`);
  }

  ngOnInit(): void {
    console.log(`DashboardPageComponent initialized with ID: ${this.instanceId}`);
    this.initializeData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialize data loading and subscriptions
   */
  private initializeData(): void {
    // Subscribe to loading state changes
    this.csvDataService.loadingState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.loadingState = state;
        this.cdRef.detectChanges();
      });

    // Subscribe to chart data changes
    this.csvDataService.charts$
      .pipe(takeUntil(this.destroy$))
      .subscribe(charts => {
        this.assignedCharts = charts;
        this.cdRef.detectChanges();
      });

    // Load initial data
    this.loadChartData();
  }

  /**
   * Load chart data from CSV
   */
  private loadChartData(): void {
    this.csvDataService.loadChartData()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Chart data loaded successfully:', response);
        },
        error: (error) => {
          console.error('Failed to load chart data:', error);
        }
      });
  }

  // Method to refresh charts - called by the refresh button
  refreshCharts(): void {
    console.log('Refreshing charts...');
    this.csvDataService.refreshChartData()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Chart data refreshed successfully:', response);
        },
        error: (error) => {
          console.error('Failed to refresh chart data:', error);
        }
      });
  }

  // Navigation event handlers
  onLogoClick(): void {
    console.log('Logo clicked');
    this.router.navigate(['/dashboard']);
  }

  onUserClick(): void {
    console.log('User clicked');
  }

  onDropdownToggle(isOpen: boolean): void {
    console.log('Dropdown toggled:', isOpen);
  }

  onMenuItemClick(item: MenuItem): void {
    console.log('Menu item clicked:', item);
    if (item.route) {
      this.router.navigate([item.route]);
    } else if (item.action) {
      item.action();
    }
  }

  logout(): void {
    console.log('Logout clicked');
    // Add logout logic here
    // For now, just navigate to login or home
    this.router.navigate(['/']);
  }
}
