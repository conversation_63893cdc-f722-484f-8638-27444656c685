import { Component, OnInit, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { AssignedTableComponent } from '../../components/assigned-table/assigned-table.component';
import { MenuComponent, UserProfile, MenuItem } from '../../../../shared/components/menu/menu.component';
import { ButtonComponent } from '../../../../shared/components/buttons/button.component';
import { RefreshIconComponent } from '../../../../shared/components/icons/refresh-icon.component';

@Component({
  selector: 'app-dashboard-page',
  standalone: true,
  imports: [
    CommonModule,
    AssignedTableComponent,
    MenuComponent,
    ButtonComponent,
    RefreshIconComponent
  ],
  templateUrl: './dashboard-page.component.html',
  styleUrls: ['./dashboard-page.component.scss'],
  // Try to prevent duplicate instances by using OnPush change detection
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardPageComponent implements OnInit {
  // Mock data for assigned charts - matching the dashboard mockup
  assignedCharts = [
    {
      memberId: '55820474',
      name: 'John Dey',
      dob: '01/05/1972',
      lob: 'MA HMO',
      measure: 'CBP',
      review1: 'Jane Chu',
      review2: '-',
      assigned: '04/15/25 1:30pm',
      status: 'Review' as const
    },
    {
      memberId: '302274401',
      name: 'Alma Anders',
      dob: '12/15/1953',
      lob: 'MA HMO',
      measure: 'CBP',
      review1: 'Jane Chu',
      review2: '-',
      assigned: '04/15/25 1:30pm',
      status: 'Inactive' as const
    },
    {
      memberId: '7729471914',
      name: 'Joanne Smith',
      dob: '06/30/1951',
      lob: 'MA HMO',
      measure: 'CBP',
      review1: 'Jane Chu',
      review2: '-',
      assigned: '04/15/25 1:30pm',
      status: 'Inactive' as const
    }
  ];

  // Search text for assigned charts
  assignedSearchText = '';

  // Navigation data
  userProfile: UserProfile = {
    name: 'Jane Chu',
    avatar: ''
  };

  menuItems: MenuItem[] = [
    { label: 'Dashboard', route: '/dashboard', icon: '🏠' },
    { label: 'Profile', route: '/profile', icon: '👤' },
    { label: 'Settings', route: '/settings', icon: '⚙️' },
    { label: 'Help', route: '/help', icon: '❓' },
    { label: 'Logout', action: () => this.logout(), icon: '🚪' }
  ];

  // Add a unique identifier to help debug multiple instances
  public instanceId = Math.random().toString(36).substring(2, 8);

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private cdRef: ChangeDetectorRef
  ) {
    console.log(`DashboardPageComponent created with ID: ${this.instanceId}`);
  }

  ngOnInit(): void {
    console.log(`DashboardPageComponent initialized with ID: ${this.instanceId}`);
  }

  // Method to refresh charts - called by the refresh button
  refreshCharts(): void {
    console.log('Refreshing charts...');
    // In a real application, this would call a service to fetch updated data
    // For now, we'll just log the action
    this.cdRef.detectChanges();
  }

  // Navigation event handlers
  onLogoClick(): void {
    console.log('Logo clicked');
    this.router.navigate(['/dashboard']);
  }

  onUserClick(): void {
    console.log('User clicked');
  }

  onDropdownToggle(isOpen: boolean): void {
    console.log('Dropdown toggled:', isOpen);
  }

  onMenuItemClick(item: MenuItem): void {
    console.log('Menu item clicked:', item);
    if (item.route) {
      this.router.navigate([item.route]);
    } else if (item.action) {
      item.action();
    }
  }

  logout(): void {
    console.log('Logout clicked');
    // Add logout logic here
    // For now, just navigate to login or home
    this.router.navigate(['/']);
  }
}
