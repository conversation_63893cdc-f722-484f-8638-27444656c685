<div class="chart-list-container">
  <div class="chart-list-header">
    <div *ngFor="let column of columns" 
         class="chart-list-header-cell"
         [ngStyle]="{'width': column.width}">
      {{ column.header }}
    </div>
  </div>
  
  <div class="chart-list-body">
    <ng-container *ngIf="filteredCharts.length > 0; else noCharts">
      <div *ngFor="let chart of filteredCharts" class="chart-list-row">
        <div *ngFor="let column of columns" 
             class="chart-list-cell"
             [ngStyle]="{'width': column.width}">
          <ng-container *ngIf="column.field !== 'status'; else statusTemplate">
            {{ chart[column.field] }}
          </ng-container>
          
          <ng-template #statusTemplate>
            <div class="status-badge"
                 [ngClass]="getStatusClass(chart.status)"
                 (click)="navigateToChartReview(chart)"
                 [class.clickable]="chart.status && chart.status.toLowerCase() === 'review'">
              {{ chart.status }}
            </div>
          </ng-template>
        </div>
      </div>
    </ng-container>
    
    <ng-template #noCharts>
      <div class="no-charts-message">
        No charts found matching your criteria.
      </div>
    </ng-template>
  </div>
</div>