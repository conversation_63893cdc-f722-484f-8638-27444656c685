import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface DemographicsData {
  measureTitle: string;
  measureSubtitle: string;
  memberId: string;
  memberName: string;
  dateOfBirth: string;
  gender: string;
  lob: string;
  providerName: string;
  npi: string;
}

@Component({
  selector: 'app-demographics',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './demographics.component.html',
  styleUrls: ['./demographics.component.scss']
})
export class DemographicsComponent {
  @Input() data: DemographicsData | null = null;
  @Input() showBackButton: boolean = true;
  @Input() backButtonText: string = 'Back';

  @Output() backClick = new EventEmitter<void>();

  onBackClick(): void {
    this.backClick.emit();
  }

  get defaultData(): DemographicsData {
    return {
      measureTitle: 'Controlling Blood Pressure (CBP)',
      measureSubtitle: 'Measure',
      memberId: '55820474',
      memberName: '<PERSON>',
      dateOfBirth: '01/05/1972',
      gender: 'M',
      lob: 'MAHMO',
      providerName: '<PERSON> <PERSON><PERSON> PA',
      npi: '882716229'
    };
  }

  get displayData(): DemographicsData {
    return this.data || this.defaultData;
  }
}
