import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Components
import { HeaderComponent } from './components/header/header.component';
import { ButtonComponent } from './components/buttons/button.component';
import { CheckboxComponent } from './components/form-controls/checkbox/checkbox.component';
import { CommentBoxComponent } from './components/form-controls/comment-box/comment-box.component';
import { StatusIndicatorComponent } from './components/status-indicator/status-indicator.component';

@NgModule({
  declarations: [
    HeaderComponent,
    ButtonComponent,
    CheckboxComponent,
    CommentBoxComponent,
    StatusIndicatorComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule
  ],
  exports: [
    // Modules
    CommonModule,
    FormsModule,
    ReactiveFormsModule,

    // Components
    HeaderComponent,
    ButtonComponent,
    CheckboxComponent,
    CommentBoxComponent,
    StatusIndicatorComponent
  ]
})
export class SharedModule { }