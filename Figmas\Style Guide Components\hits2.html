<div data-layer="Hits" class="Hits" style="width: 517px; padding: 20px; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
  <div data-layer="Frame 913" class="Frame913" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-end; display: flex">
    <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
      <div data-layer="stack" class="Stack" style="justify-content: flex-start; align-items: center; gap: 10px; display: flex">
        <div data-layer="label" class="Label" style="color: #17181A; font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Hits</div>
      </div>
    </div>
  </div>
  <div data-layer="table" class="Table" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="columns" class="Columns" style="flex: 1 1 0; justify-content: flex-start; align-items: flex-start; display: flex">
      <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
          <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">DoS</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">07/21/24</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">05/21/24</div>
          </div>
        </div>
      </div>
      <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
          <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Sys</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">136</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">140</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="width: 22px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="width: 72px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">150</div>
          </div>
        </div>
      </div>
      <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div data-layer="header item" class="HeaderItem" style="height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
          <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Dias</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="width: 16px; text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">82</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">90</div>
          </div>
        </div>
      </div>
      <div data-layer="column" class="Column" style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div data-layer="header item" class="HeaderItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
          <div data-layer="Table Item" class="TableItem" style="height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Page</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: rgba(255, 255, 255, 0.25); justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">2</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 10px; padding-bottom: 10px; background: white; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="icon text" class="IconText" style="background: white; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
            <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--link, #0071BC); font-size: 12px; font-family: Urbane; font-weight: 300; text-decoration: underline; line-height: 16px; word-wrap: break-word">7</div>
          </div>
        </div>
      </div>
      <div data-layer="column" class="Column" style="width: 216px; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
        <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Label" class="Label" style="color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Comment</div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-top: 2px; padding-bottom: 2px; background: white; justify-content: center; align-items: center; display: inline-flex">
          <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
            <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
            </div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
            <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
            </div>
          </div>
        </div>
        <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
          <div data-layer="Comment-box" data-property-1="Default" class="CommentBox" style="flex: 1 1 0; height: 30px; padding: 4px; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
            <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Comment" class="Comment" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 12px; word-wrap: break-word">Comment</div>
            </div>
          </div>
        </div>
      </div>
      <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
        <div data-layer="column" class="Column" style="width: 60px; flex-direction: column; justify-content: center; align-items: center; display: flex">
          <div data-layer="header item" class="HeaderItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; border-bottom: 1px #F1F5F7 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
            <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
              <div data-layer="Label" class="Label" style="width: 53.54px; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 500; line-height: 20px; word-wrap: break-word">Include</div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="align-self: stretch; height: 40px; padding-left: 8px; padding-right: 8px; padding-top: 18px; padding-bottom: 18px; background: white; justify-content: center; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="check" data-property-1="default" class="Check" style="width: 16px; height: 16px; position: relative">
              <div data-layer="Rectangle 2" class="Rectangle2" style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute; background: white; border-radius: 5px; border: 1px #D9E1E7 solid"></div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="check" data-property-1="default" class="Check" style="width: 16px; height: 16px; position: relative">
              <div data-layer="Rectangle 2" class="Rectangle2" style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute; background: white; border-radius: 5px; border: 1px var(--gray-2, #D9E1E7) solid"></div>
            </div>
          </div>
          <div data-layer="Table Item" class="TableItem" style="height: 40px; padding-left: 8px; padding-right: 8px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="check" data-property-1="default" class="Check" style="width: 16px; height: 16px; position: relative">
              <div data-layer="Rectangle 2" class="Rectangle2" style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute; background: white; border-radius: 5px; border: 1px var(--gray-2, #D9E1E7) solid"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>