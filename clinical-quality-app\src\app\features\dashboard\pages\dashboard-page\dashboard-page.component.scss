@use 'variables' as vars;
@use 'mixins' as mix;

.dashboard-container {
  padding: 20px;
  background-color: var(--light-background, #F9FBFC);
  min-height: calc(100vh - 80px); // Subtract header height
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dashboard-section {
  background-color: var(--white, white);
  border-radius: 8px;
  border: 1px solid var(--light-borders, #F1F5F7);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;

  h2 {
    font-size: 20px;
    font-weight: 600;
    line-height: 32px;
    color: var(--text-black, #17181A);
    margin: 0;
  }
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: flex-end;
}

