<div class="container">
  <!-- Menu -->
  <app-menu
    logoSrc="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024"
    logoAlt="Stellarus Logo"
    [user]="userProfile"
    [menuItems]="menuItems"
    (logoClick)="onLogoClick()"
    (userClick)="onUserClick()"
    (dropdownToggle)="onDropdownToggle($event)"
    (menuItemClick)="onMenuItemClick($event)">
  </app-menu>

  <!-- Page Title -->
  <app-page-title [title]="'Chart Review'"></app-page-title>

  <div class="main-content">
    <!-- Patient Demographics Header - spans full width -->
    <div class="demographics-section">
      <app-demographics
        [data]="demographicsData"
        [showBackButton]="true"
        backButtonText="Back"
        (backClick)="onDemographicsBackClick()">
      </app-demographics>
    </div>

    <!-- Two-column layout matching Figma mockup -->
    <div class="content-layout">
      <!-- Left Column: PDF Viewer -->
      <div class="pdf-column">
        <app-pdf-viewer></app-pdf-viewer>
      </div>

      <!-- Right Column: Hits and Results -->
      <div class="right-column">
        <!-- Hits Section -->
        <div class="hits-section">
          <app-hits
            title="Hits"
            [data]="hitsData"
            (dataChange)="onHitsDataChange($event)"
            (pageClick)="onHitsPageClick($event)"
            (commentChange)="onHitsCommentChange($event)"
            (includeChange)="onHitsIncludeChange($event)">
          </app-hits>
        </div>

        <!-- Results Section -->
        <div class="results-section">
          <app-results
            title="Results"
            [(ngModel)]="resultsData"
            (dataChange)="onResultsDataChange($event)">
          </app-results>
        </div>

        <!-- Submit Button -->
        <div class="submit-section">
          <app-button
            variant="primary"
            (buttonClick)="onCompleteReview()">
            Submit
          </app-button>
        </div>
      </div>
    </div>
  </div>
</div>
