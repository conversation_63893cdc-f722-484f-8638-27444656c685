@import "src/app/tokens.scss";

.property-1-none-found_1166-1182 {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.head_929-3425 {
  padding: 0px 0px 12px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: 100%;
}
.frame-929_929-3426 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.stack_929-3427 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  width: 100%;
}
.text-label_929-3428 {
  color: #17181a;
  @include h-1-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.right-corner_929-3429 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.menu_929-3430 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 41px;
  width: 100%;
}
.second-menu_929-3431 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: content-box;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-bottom-width: 1px;
  background: #ffffff;
  width: 100%;
}
.bradcrumb_929-3432 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  height: 40px;
}
.box_929-3433 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.menu_929-3434 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3435 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3436 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3437 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3439 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item-_929-3441 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  height: 100%;
}
.menu-item_929-3442 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3443 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3445 {
  color: $variable-collection-gray-3;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.menu-item_929-3447 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-color: $variable-collection-primary-blue;
  border-style: solid;
  border-bottom-width: 1px;
  height: 100%;
}
.menu-item_929-3448 {
  padding: 4px 8px 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 100%;
}
.frame-831_929-3449 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  border-radius: 6px;
}
.text-label_929-3451 {
  color: $variable-collection-primary-blue;
  @include h-2-text;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.frame-916_929-3453 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
}
.frame-942_929-3454 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-reasoning_929-3455 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.dropdown-none_929-3456 {
  height: 44px;
  width: 100%;
}

.frame-943_929-3457 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px;
  box-sizing: border-box;
  width: 100%;
}
.text-notes_929-3458 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.notes_929-3459 {
  width: 100%;
}
