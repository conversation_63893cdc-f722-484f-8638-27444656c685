import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';

import { ChartReviewRoutingModule } from './chart-review-routing.module';
import { PdfViewerComponent } from './components/pdf-viewer/pdf-viewer.component';
import { PdfViewerTestComponent } from './components/pdf-viewer-test/pdf-viewer-test.component';
import { AnnotationComponent } from './components/annotation/annotation.component';
import { ValidationComponent } from './components/validation/validation.component';
import { ChartReviewPageComponent } from './pages/chart-review-page/chart-review-page.component';

@NgModule({
  declarations: [
    // All components are now standalone, so no declarations needed
  ],
  imports: [
    CommonModule,
    FormsModule,
    NgxExtendedPdfViewerModule,
    ChartReviewRoutingModule,
    // Import all standalone components
    PdfViewerComponent,
    PdfViewerTestComponent,
    AnnotationComponent,
    ValidationComponent,
    ChartReviewPageComponent
  ],
  exports: [
    // No need to export components that are imported
  ]
})
export class ChartReviewModule { }
