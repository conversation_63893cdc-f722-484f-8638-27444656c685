import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, Subject, from, of } from 'rxjs';
import { catchError, map, shareReplay, switchMap, tap } from 'rxjs/operators';
import { isPlatformBrowser } from '@angular/common';

// We'll dynamically import PDF.js only in browser environments
// Type definition for PDF.js library
type PdfJsLib = typeof import('pdfjs-dist');
// Specific types from pdfjs-dist
type PDFDocumentProxy = import('pdfjs-dist').PDFDocumentProxy;
type PDFPageProxy = import('pdfjs-dist').PDFPageProxy;

// Define our own TextContent interface based on what pdfjs-dist provides
interface TextContent {
  items: Array<any>;
  styles: Record<string, any>;
}

// Define interfaces for PDF document and page
export interface PdfDocument {
  numPages: number;
  getPage: (pageNumber: number) => Promise<PDFPageProxy>;
}

export interface PdfSearchResult {
  pageNumber: number;
  matchIndex: number;
  text: string;
  position?: {
    left: number;
    top: number;
    right: number;
    bottom: number;
  };
}

/**
 * Interface for PDF text item
 * This is a custom interface to match the structure returned by PDF.js
 */
export interface TextItem {
  str: string;
  transform: number[];
  width: number;
  height: number;
  dir: string;
  fontName?: string;
}

/**
 * Interface for PDF text content
 */
export interface PdfTextContent {
  pageNumber: number;
  items: TextItem[];
  styles: object;
}

/**
 * Interface for highlight information
 */
export interface PdfHighlight {
  id: string;
  pageNumber: number;
  position: {
    left: number;
    top: number;
    right: number;
    bottom: number;
  };
  color: string;
  text: string;
}

/**
 * Service for handling PDF operations including loading, text extraction,
 * searching, and highlighting
 */
@Injectable({
  providedIn: 'root'
})
export class PdfService {
  private pdfDocument: PDFDocumentProxy | null = null;
  private pdfDocumentSubject = new BehaviorSubject<PDFDocumentProxy | null>(null);
  private pdfInfoSubject = new BehaviorSubject<PdfDocument | null>(null);
  private textContentCache = new Map<number, PdfTextContent>();
  private highlightsSubject = new BehaviorSubject<PdfHighlight[]>([]);
  private pdfBase64DataUrlSubject = new BehaviorSubject<string | null>(null); // New subject for base64 data URL
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new Subject<string>();
  private isBrowser: boolean;
  private pdfjsLib: PdfJsLib | null = null;
  private pdfJsLibPromise: Promise<PdfJsLib | null> | null = null; // Promise for PDF.js library loading

  /**
   * Observable for the current PDF document
   */
  public pdfDocument$ = this.pdfDocumentSubject.asObservable();

  /**
   * Observable for the PDF document information
   */
  public pdfInfo$ = this.pdfInfoSubject.asObservable();

  /**
   * Observable for the highlights
   */
  public highlights$ = this.highlightsSubject.asObservable();

  /**
   * Observable for the PDF data as a base64 data URL
   */
  public pdfBase64DataUrl$ = this.pdfBase64DataUrlSubject.asObservable();

  /**
   * Observable for loading state
   */
  public loading$ = this.loadingSubject.asObservable();

  /**
   * Observable for error messages
   */
  public error$ = this.errorSubject.asObservable();

  constructor(
    private http: HttpClient,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);

    // Initialize PDF.js only in browser environment
    if (this.isBrowser) {
      this.pdfJsLibPromise = this.loadPdfJsLibrary();
    }
  }

  /**
   * Dynamically loads the PDF.js library.
   * This ensures it's only loaded in browser environments.
   * @returns A promise that resolves with the PDF.js library or null if an error occurs.
   */
  private async loadPdfJsLibrary(): Promise<PdfJsLib | null> {
    try {
      console.log('[PdfService] Starting to load PDF.js library...');
      // Dynamic import of PDF.js
      const pdfjs = await import('pdfjs-dist');
      this.pdfjsLib = pdfjs;

      // Set the worker source path
      this.pdfjsLib.GlobalWorkerOptions.workerSrc = 'assets/pdf.worker.mjs';
      console.log('[PdfService] PDF.js library loaded and workerSrc set.');
      return this.pdfjsLib;
    } catch (error) {
      console.error('[PdfService] Error loading PDF.js library:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.errorSubject.next(`Critical: PDF.js library failed to load: ${errorMessage}`);
      this.pdfjsLib = null;
      return null;
    }
  }

  /**
   * Ensures PDF.js library is loaded before proceeding.
   * @returns A promise that resolves when PDF.js is ready, or rejects if it fails to load.
   */
  private async ensurePdfJsLibLoaded(): Promise<void> {
    if (this.pdfjsLib) {
      return Promise.resolve(); // Already loaded
    }
    if (this.pdfJsLibPromise) {
      await this.pdfJsLibPromise; // Wait for the loading attempt to complete
      if (!this.pdfjsLib) { // Check if it was successfully loaded and set
        const message = 'PDF.js library was not available after loading attempt.';
        console.error(`[PdfService] ${message}`);
        this.errorSubject.next(`Critical: ${message}`);
        throw new Error(message);
      }
      return Promise.resolve();
    }
    // Should not happen if constructor logic is correct, but as a fallback:
    console.error('[PdfService] PDF.js library initialization not started.');
    throw new Error('PDF.js library initialization not started.');
  }

  /**
   * Clears the text content cache
   */
  public clearCache(): void {
    this.textContentCache.clear();
    this.pdfBase64DataUrlSubject.next(null); // Clear base64 data as well
  }

  /**
   * Loads a PDF document from a URL
   * @param url URL of the PDF document
   * @returns Observable of the PDF document
   */
  loadPdf(url: string): Observable<PdfDocument> {
    console.log('[PdfService] loadPdf called with URL:', url);

    if (!this.isBrowser) {
      console.log('[PdfService] Not in browser environment, returning empty document');
      return of({ numPages: 0, getPage: () => Promise.reject('PDF.js not available in server environment') });
    }

    this.loadingSubject.next(true);
    this.clearCache();

    return from(this.ensurePdfJsLibLoaded()).pipe(
      switchMap(() => {
        // At this point, this.pdfjsLib should be loaded by ensurePdfJsLibLoaded
        if (!this.pdfjsLib) {
          // This case should ideally not be reached if ensurePdfJsLibLoaded works correctly.
          console.error('[PdfService] loadPdf: PDF.js library still not loaded after ensurePdfJsLibLoaded resolved.');
          throw new Error('PDF.js library failed to initialize properly.');
        }
        console.log('[PdfService] Attempting to fetch PDF from URL:', url);
        return this.http.get(url, { responseType: 'arraybuffer' }).pipe(
          tap(arrayBuffer => {
            // Convert ArrayBuffer to base64 data URL and emit
            const dataUrl = this.arrayBufferToDataUrl(arrayBuffer);
            this.pdfBase64DataUrlSubject.next(dataUrl);
          })
        );
      }),
      switchMap(data => { // data here is the ArrayBuffer
        // this.pdfjsLib is guaranteed to be non-null here if the previous switchMap didn't throw.
        try {
          return from(this.pdfjsLib!.getDocument({ data }).promise);
        } catch (error) {
          console.error('[PdfService] Error creating PDF document in getDocument:', error);
          this.errorSubject.next('Failed to process PDF data');
          throw error; // Rethrow to be caught by outer catchError
        }
      }), // Emits PDFDocumentProxy
      map((pdfDocProxy: PDFDocumentProxy) => { // Renamed for clarity
        this.pdfDocument = pdfDocProxy; // Set the internal full proxy
        this.pdfDocumentSubject.next(pdfDocProxy);

        // Create a simplified PdfDocument object that matches the interface
        const documentInfo: PdfDocument = {
          numPages: pdfDocProxy.numPages,
          getPage: (pageNumber: number) => pdfDocProxy.getPage(pageNumber)
        };

        this.pdfInfoSubject.next(documentInfo);
        this.loadingSubject.next(false);
        return documentInfo; // This is the transformed value for the observable stream
      }),
      catchError((error: Error) => {
        console.error('Error loading PDF:', error);
        this.errorSubject.next(`Failed to load PDF: ${error.message}`);
        this.loadingSubject.next(false);
        // Rethrow the error to allow components to handle it
        throw error;
      }),
      // Share the PDF document among multiple subscribers
      shareReplay(1)
    );
  }

  /**
   * Loads a PDF document from a base64 data URL
   * @param dataUrl Base64 data URL containing the PDF
   * @returns Observable of the PDF document
   */
  loadPdfFromDataUrl(dataUrl: string): Observable<PdfDocument> {
    if (!this.isBrowser) {
      return of({ numPages: 0, getPage: () => Promise.reject('PDF.js not available in server environment') });
    }

    this.loadingSubject.next(true);
    this.clearCache();

    // Set the base64 data URL immediately
    this.pdfBase64DataUrlSubject.next(dataUrl);

    return from(this.ensurePdfJsLibLoaded()).pipe(
      switchMap(() => {
        if (!this.pdfjsLib) {
          console.error('[PdfService] loadPdfFromDataUrl: PDF.js library still not loaded after ensurePdfJsLibLoaded resolved.');
          throw new Error('PDF.js library failed to initialize properly.');
        }

        // Convert data URL to ArrayBuffer
        const base64Data = dataUrl.split(',')[1];
        const binaryString = window.atob(base64Data);
        const arrayBuffer = new ArrayBuffer(binaryString.length);
        const uint8Array = new Uint8Array(arrayBuffer);

        for (let i = 0; i < binaryString.length; i++) {
          uint8Array[i] = binaryString.charCodeAt(i);
        }

        console.log('[PdfService] Calling pdfjsLib.getDocument... (from data URL)');
        return from(this.pdfjsLib!.getDocument({ data: arrayBuffer }).promise);
      }),
      map((pdfDocument: PDFDocumentProxy) => {
        console.log('[PdfService] PDF loaded from data URL with', pdfDocument.numPages, 'pages');
        this.pdfDocument = pdfDocument;
        this.pdfDocumentSubject.next(pdfDocument);

        // Create a simplified PdfDocument object that matches the interface
        const documentInfo: PdfDocument = {
          numPages: pdfDocument.numPages,
          getPage: (pageNumber: number) => pdfDocument.getPage(pageNumber)
        };

        this.pdfInfoSubject.next(documentInfo);
        this.loadingSubject.next(false);
        return documentInfo;
      }),
      catchError((error: Error) => {
        console.error('[PdfService] Error loading PDF from data URL:', error);
        this.errorSubject.next(`Failed to load PDF: ${error.message}`);
        this.loadingSubject.next(false);
        throw error;
      }),
      shareReplay(1)
    );
  }

  /**
   * Loads a PDF document from a File object
   * @param file File object containing the PDF
   * @returns Observable of the PDF document
   */
  loadPdfFromFile(file: File): Observable<PdfDocument> {
    if (!this.isBrowser) {
      return of({ numPages: 0, getPage: () => Promise.reject('PDF.js not available in server environment') });
    }

    this.loadingSubject.next(true);
    this.clearCache();

    return from(this.ensurePdfJsLibLoaded()).pipe(
      switchMap(() => {
        // At this point, this.pdfjsLib should be loaded by ensurePdfJsLibLoaded
        if (!this.pdfjsLib) {
          console.error('[PdfService] loadPdfFromFile: PDF.js library still not loaded after ensurePdfJsLibLoaded resolved.');
          throw new Error('PDF.js library failed to initialize properly.');
        }
        return new Observable<PdfDocument>(observer => {
          const fileReader = new FileReader();

          fileReader.onload = () => {
            console.log('[PdfService] FileReader onload triggered for loadPdfFromFile.');
            const arrayBuffer = fileReader.result;

            if (!(arrayBuffer instanceof ArrayBuffer)) {
              console.error('[PdfService] FileReader result is not an ArrayBuffer.');
              observer.error(new Error('File read result was not an ArrayBuffer.'));
              return;
            }

            // Convert ArrayBuffer to base64 data URL and emit
            const dataUrl = this.arrayBufferToDataUrl(arrayBuffer);
            this.pdfBase64DataUrlSubject.next(dataUrl);

            // this.pdfjsLib is guaranteed to be non-null here.
            console.log('[PdfService] Calling pdfjsLib.getDocument... (from file)');
            this.pdfjsLib!.getDocument({ data: arrayBuffer }).promise
              .then((pdfDocument: PDFDocumentProxy) => {
                console.log('[PdfService] pdfjsLib.getDocument resolved successfully.'); // Added log
                console.log(`[PdfService] PDF loaded with ${pdfDocument.numPages} pages.`); // Added log
                this.pdfDocument = pdfDocument;
                this.pdfDocumentSubject.next(pdfDocument);

                // Create a simplified PdfDocument object that matches the interface
                const documentInfo: PdfDocument = {
                  numPages: pdfDocument.numPages,
                  getPage: (pageNumber: number) => pdfDocument.getPage(pageNumber)
                };

                this.pdfInfoSubject.next(documentInfo);
                this.loadingSubject.next(false);

                observer.next(documentInfo);
                observer.complete();
              })
              .catch((error: Error) => {
                console.error('[PdfService] Error loading PDF from file:', error); // Modified log
                this.errorSubject.next(`Failed to load PDF: ${error.message}`);
                this.loadingSubject.next(false);
                observer.error(error);
              });
          };

          fileReader.onerror = (event) => {
            console.error('[PdfService] Error reading file:', event); // Modified log
            this.errorSubject.next('Failed to read the file');
            this.loadingSubject.next(false);
            observer.error(new Error('Failed to read the file'));
          };

          // Read the file as an ArrayBuffer
          fileReader.readAsArrayBuffer(file);
        });
      }),
      // Share the PDF document among multiple subscribers
      shareReplay(1)
    );
  }

  /**
   * Gets text content for a specific page
   * @param pageNumber Page number (1-based)
   * @returns Observable of the text content
   */
  getTextContent(pageNumber: number): Observable<PdfTextContent> {
    if (!this.isBrowser) {
      return of({ pageNumber, items: [], styles: {} }); // Align with PdfTextContent.styles: object
    }

    // Check if the text content is already cached
    if (this.textContentCache.has(pageNumber)) {
      return of(this.textContentCache.get(pageNumber)!);
    }

    return from(this.ensurePdfJsLibLoaded()).pipe(
      switchMap(() => {
        if (!this.pdfjsLib) {
           console.error('[PdfService] getTextContent: PDF.js library not loaded.');
           throw new Error('PDF.js library not loaded for getTextContent');
        }
        if (!this.pdfDocument) {
          console.error('[PdfService] getTextContent: PDF document not loaded.');
          // Throw an error to be caught by the main catchError, which returns the empty state
          throw new Error('PDF document not loaded for getTextContent');
        }
        return from(this.pdfDocument.getPage(pageNumber));
      }), // This switchMap now correctly expects Observable<PDFPageProxy>
      switchMap((page: PDFPageProxy) => { // Type page as PDFPageProxy
        // if (!page) return of({ pageNumber, items: [], styles: {} }); // This check might be redundant if getPage always resolves or rejects
        return from(page.getTextContent());
      }),
      map((textContent: TextContent) => { // Type textContent from pdfjs-dist
        const result: PdfTextContent = {
          pageNumber,
          items: textContent.items as TextItem[],
          styles: textContent.styles
        };
        this.textContentCache.set(pageNumber, result);
        return result;
      }),
      catchError((error: Error) => {
        console.error(`[PdfService] Error getting text content for page ${pageNumber}:`, error);
        this.errorSubject.next(`Failed to extract text from page ${pageNumber}: ${error.message}`);
        return of({ pageNumber, items: [], styles: {} });
      })
    );
  }

  /**
   * Searches for text across all pages of the PDF
   * @param searchText Text to search for
   * @param options Search options (case sensitivity, etc.)
   * @returns Observable of search results
   */
  searchText(searchText: string, options: { caseSensitive?: boolean, wholeWord?: boolean } = {}): Observable<PdfSearchResult[]> {
    if (!this.isBrowser || !searchText.trim()) {
      return of([]);
    }

    return from(this.ensurePdfJsLibLoaded()).pipe(
      switchMap(() => {
        if (!this.pdfjsLib) {
          console.error('[PdfService] searchText: PDF.js library not loaded.');
          throw new Error('PDF.js library not loaded for searchText');
        }
        if (!this.pdfDocument) {
          console.error('[PdfService] searchText: PDF document not loaded.');
          return of([]); // Or throw an error
        }

        const results: PdfSearchResult[] = [];
        const searchTextRegex = this.createSearchRegex(searchText, options.caseSensitive, options.wholeWord);
        const pagePromises: Promise<void>[] = [];

        for (let i = 1; i <= this.pdfDocument.numPages; i++) {
          const pageNum = i;
          // Pass the loaded pdfjsLib instance to searchInPage
          const pagePromise = this.searchInPage(pageNum, searchTextRegex) // Removed pdfjsLib and pdfDocument params
            .then(pageResults => {
              results.push(...pageResults);
            })
            .catch((error: Error) => {
              console.error(`[PdfService] Error searching in page ${pageNum}:`, error);
            });
          pagePromises.push(pagePromise);
        }
        return from(Promise.all(pagePromises).then(() => results));
      }),
      catchError((error: Error) => {
        console.error('[PdfService] Error in searchText observable chain:', error);
        this.errorSubject.next(`Failed to search text: ${error.message}`);
        return of([]);
      })
    );
  }

  /**
   * Creates a regular expression for searching
   * @param searchText The text to search for
   * @param caseSensitive Whether the search is case-sensitive
   * @param wholeWord Whether to match whole words only
   * @returns A RegExp object
   */
  private createSearchRegex(searchText: string, caseSensitive?: boolean, wholeWord?: boolean): RegExp {
    let regexPattern = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Escape special characters

    if (wholeWord) {
      regexPattern = `\\b${regexPattern}\\b`;
    }

    return new RegExp(regexPattern, caseSensitive ? 'g' : 'gi');
  }

  /**
   * Searches for text in a specific page
   * @param pageNumber The page number (1-based)
   * @param searchRegex The regular expression to search with
   * @returns A Promise of search results
   */
  private async searchInPage(
    pageNumber: number,
    searchRegex: RegExp
  ): Promise<PdfSearchResult[]> {
    // Use this.pdfjsLib and this.pdfDocument directly
    if (!this.pdfjsLib || !this.pdfDocument) {
      console.error('[PdfService] searchInPage: PDF.js library or document not loaded.');
      throw new Error('PDF.js library or document not loaded for searchInPage');
    }
    const pageResults: PdfSearchResult[] = [];
    try {
      // Get the text content for the page
      let textContent: PdfTextContent;
      if (this.textContentCache.has(pageNumber)) {
        textContent = this.textContentCache.get(pageNumber)!;
      } else {
        const pageForText = await this.pdfDocument.getPage(pageNumber);
        const content = await pageForText.getTextContent();
        textContent = {
          pageNumber,
          items: content.items as TextItem[],
          styles: content.styles
        };
        this.textContentCache.set(pageNumber, textContent);
      }

      // Get the viewport for the page to calculate positions
      const pageForViewport = await this.pdfDocument.getPage(pageNumber);
      const viewport = pageForViewport.getViewport({ scale: 1.0 });

      // Combine text items to form lines of text
      const lines: { text: string; items: TextItem[] }[] = [];
      let currentLine: TextItem[] = [];
      let lastY: number | null = null;

      for (const item of textContent.items) {
        // 'str' in item check is redundant as TextItem guarantees 'str'
        if (item.str.trim() === '') continue;
        if (lastY === null || Math.abs(item.transform[5] - lastY) < 2) {
          // Same line
          currentLine.push(item);
        } else {
          // New line
          if (currentLine.length > 0) {
            lines.push({
              text: currentLine.map(i => i.str).join(''),
              items: [...currentLine]
            });
          }
          currentLine = [item]; // Always start the new line with the current item
        }
        lastY = item.transform[5];
      }

      // Add the last line
      if (currentLine.length > 0) {
        lines.push({
          text: currentLine.map(i => i.str).join(''),
          items: [...currentLine]
        });
      }

      // Search in each line
      let matchIndex = 0;
      for (const line of lines) {
        let match: RegExpExecArray | null;
        searchRegex.lastIndex = 0; // Reset regex state

        while ((match = searchRegex.exec(line.text)) !== null) {
          const startIndex = match.index;
          const endIndex = startIndex + match[0].length;

          // Find the text items that contain the match
          let currentPos = 0;
          let startItem: TextItem | null = null;
          let endItem: TextItem | null = null;

          for (const item of line.items) {
            const itemStart = currentPos;
            const itemEnd = currentPos + item.str.length;

            if (startItem === null && startIndex < itemEnd) {
              startItem = item;
            }

            if (endItem === null && endIndex <= itemEnd) {
              endItem = item;
            }

            if (startItem !== null && endItem !== null) {
              break;
            }

            currentPos = itemEnd;
          }

          if (startItem && endItem) {
            // Calculate the position of the match
            const position = {
              left: startItem.transform[4],
              top: viewport.height - startItem.transform[5] - startItem.height,
              right: endItem.transform[4] + endItem.width,
              bottom: viewport.height - endItem.transform[5]
            };

            pageResults.push({
              pageNumber,
              matchIndex: matchIndex++,
              text: match[0],
              position
            });
          }
        }
      }

      return pageResults;
    } catch (error) {
      console.error(`[PdfService] Error in searchInPage for page ${pageNumber}:`, error);
      return [];
    }
  }

  /**
   * Adds a highlight to the PDF
   * @param pageNumber The page number (1-based)
   * @param position The position of the highlight
   * @param text The text being highlighted
   * @param color The color of the highlight
   * @returns The ID of the highlight
   */
  addHighlight(pageNumber: number, position: { left: number; top: number; right: number; bottom: number }, text: string, color: string = 'rgba(255, 255, 0, 0.3)'): string {
    const id = `highlight-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    const highlight: PdfHighlight = {
      id,
      pageNumber,
      position,
      color,
      text
    };

    const currentHighlights = this.highlightsSubject.value;
    this.highlightsSubject.next([...currentHighlights, highlight]);

    return id;
  }

  /**
   * Removes a highlight from the PDF
   * @param id The ID of the highlight to remove
   */
  removeHighlight(id: string): void {
    const currentHighlights = this.highlightsSubject.value;
    this.highlightsSubject.next(currentHighlights.filter(h => h.id !== id));
  }

  /**
   * Gets all highlights for a specific page
   * @param pageNumber The page number (1-based)
   * @returns An Observable of highlights for the page
   */
  getPageHighlights(pageNumber: number): Observable<PdfHighlight[]> {
    return this.highlights$.pipe(
      map(highlights => highlights.filter(h => h.pageNumber === pageNumber))
    );
  }

  /**
   * Clears all highlights
   */
  clearHighlights(): void {
    this.highlightsSubject.next([]);
  }

  /**
   * Closes the current PDF document
   */
  closePdf(): void {
    if (this.pdfDocument) {
      this.pdfDocument.destroy();
      this.pdfDocument = null;
    }
    this.pdfDocumentSubject.next(null);
    this.pdfInfoSubject.next(null);
    this.highlightsSubject.next([]); // Ensure highlights are cleared
    this.pdfBase64DataUrlSubject.next(null); // Clear base64 data as well
    this.clearCache(); // Clears text content cache and also pdfBase64DataUrlSubject
    this.loadingSubject.next(false); // Reset loading state
    // this.errorSubject.next(''); // Optionally clear errors, or let them persist
    console.log('[PdfService] PDF closed and resources released.');
  }

  /**
   * Converts an ArrayBuffer to a base64 data URL string.
   * @param buffer The ArrayBuffer to convert.
   * @returns A string representing the base64 data URL.
   */
  private arrayBufferToDataUrl(buffer: ArrayBuffer): string {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    const base64 = window.btoa(binary);
    return `data:application/pdf;base64,${base64}`;
  }
}
