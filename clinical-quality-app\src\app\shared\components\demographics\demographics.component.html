<div class="demographics-header">
  <button class="back-btn" *ngIf="showBackButton" (click)="onBackClick()">
    <span class="icon-arrow-left"></span>
    {{ backButtonText }}
  </button>
  <div class="demographic-item member-id">
    <div class="value">{{ displayData.memberId }}</div>
    <div class="label">Member ID</div>
  </div>
  <div class="demographic-item member-name">
    <div class="value">{{ displayData.memberName }}</div>
    <div class="label">Member</div>
  </div>
  <div class="demographic-item dob">
    <div class="value">{{ displayData.dateOfBirth }}</div>
    <div class="label">DOB</div>
  </div>
  <div class="demographic-item gender">
    <div class="value">{{ displayData.gender }}</div>
    <div class="label">Gender</div>
  </div>
  <div class="demographic-item lob">
    <div class="value">{{ displayData.lob }}</div>
    <div class="label">LOB</div>
  </div>
  <div class="demographic-item provider-name">
    <div class="value">{{ displayData.providerName }}</div>
    <div class="label">Provider</div>
  </div>
  <div class="demographic-item npi">
    <div class="value">{{ displayData.npi }}</div>
    <div class="label">NPI</div>
  </div>
</div>
