<div class="demographics-container">
  <div class="demographics-content">
    
    <!-- Left Section: Back Button and Measure Info -->
    <div class="left-section">
      <div class="header-section">
        
        <!-- Back But<PERSON> -->
        <div *ngIf="showBackButton" class="back-button" (click)="onBackClick()">
          <div class="back-icon">
            <svg width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4.5 1L1 4.5L4.5 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M1 4.5H11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <span class="back-text">{{ backButtonText }}</span>
        </div>

        <!-- Measure Information -->
        <div class="measure-info">
          <div class="measure-title">{{ displayData.measureTitle }}</div>
          <div class="measure-subtitle">{{ displayData.measureSubtitle }}</div>
        </div>
      </div>
    </div>

    <!-- Right Section: Demographics Data -->
    <div class="right-section">
      
      <!-- Primary Demographics -->
      <div class="demographics-group primary-group">
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.memberId }}</div>
          <div class="demographic-label">Member ID</div>
        </div>
        
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.memberName }}</div>
          <div class="demographic-label">Member</div>
        </div>
        
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.dateOfBirth }}</div>
          <div class="demographic-label">DOB</div>
        </div>
        
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.gender }}</div>
          <div class="demographic-label">Gender</div>
        </div>
        
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.lob }}</div>
          <div class="demographic-label">LOB</div>
        </div>
      </div>

      <!-- Provider Information -->
      <div class="demographics-group provider-group">
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.providerName }}</div>
          <div class="demographic-label">Provider</div>
        </div>
        
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.npi }}</div>
          <div class="demographic-label">NPI</div>
        </div>
      </div>
    </div>
  </div>
</div>
