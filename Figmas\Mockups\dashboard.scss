.content_1134-1188 {
  padding: 20px 0px 20px 0px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  width: -webkit-fill-available;
  height: 100%;
}
.content_1134-1189 {
  padding: 0px 30px 0px 30px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  background: #ffffff;
  width: -webkit-fill-available;
}
.text-dashboard_1134-1190 {
  color: #17181a;
  font-size: 24px;
  font-family: Urbane;
  line-height: 32px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.assigned-table_1134-1191 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 20px;
  box-sizing: border-box;
  width: 1380px;
}
.table_1134-1192 {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: #f1f5f7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  width: -webkit-fill-available;
}
.head_1134-1193 {
  padding: 0px 0px 20px 0px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.stack_1134-1194 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.text-label_1134-1195 {
  color: #17181a;
  font-size: 20px;
  font-family: Urbane;
  line-height: 160.0000023841858%;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
}

.right-corner_1134-1196 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: -webkit-fill-available;
  height: 100%;
}

.button_1134-1197 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  border-radius: 8px;
}
.x--button-base_1134-1198 {
  padding: 8px 14px 8px 14px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 8px;
  border-color: #d9e1e7;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
}
.icon-arrow_1134-1199 {
  position: relative;
  box-sizing: border-box;
  display: flex;
}
.vector_1134-1200 {
  top: 2px;
  left: 2px;
  position: absolute;
  display: flex;
  display: flex;
}

.group_1134-1201 {
  top: 6px;
  left: 6px;
  position: absolute;
  display: flex;
  width: 8px;
  height: 9px;
}
.vector_1134-1202 {
  display: flex;
}

.vector_1134-1203 {
  display: flex;
}

.vector_1134-1204 {
  display: flex;
}

.vector_1134-1205 {
  display: flex;
}

.vector_1134-1206 {
  top: 20px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-text_1134-1207 {
  color: #17181a;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table_1134-1208 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.columns_1134-1209 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.column_1134-1210 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: -webkit-fill-available;
}
.header-item_1134-1211 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1134-1212 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1214 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1215 {
  padding: 13px 12px 13px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1216 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1217 {
  padding: 13px 12px 13px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1218 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1219 {
  padding: 13px 12px 13px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1220 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1235-930 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 107px;
}
.header-item_1235-931 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1235-932 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1235-934 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1235-939 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-940 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1235-941 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1235-942 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-943 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1235-944 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1235-945 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-946 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1235-10624 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1134-1235 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 106px;
}
.header-item_1134-1236 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1134-1237 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1239 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1240 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1241 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1242 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1243 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1244 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1245 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 78px;
}

.table-item_1134-1246 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1247 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1248 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1235-969 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 102px;
}
.header-item_1235-970 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1235-971 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1235-973 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1235-974 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-975 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}

.table-item_1235-977 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-978 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1235-979 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 78px;
}

.table-item_1235-980 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1235-981 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}

.column_1134-1270 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 99px;
}
.header-item_1134-1271 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1134-1272 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1274 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1275 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1276 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1277 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1278 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1279 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1280 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1281 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1282 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1283 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1134-1305 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 100px;
}
.header-item_1134-1306 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1134-1307 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1309 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: wrap;
  width: 27px;
}

.table-item_1134-1310 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1311 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1312 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1313 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1314 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1315 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1316 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
}
.icon-text_1134-1317 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1318 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1134-1340 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 93px;
}
.header-item_1134-1341 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1134-1342 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1344 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1345 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
  width: -webkit-fill-available;
}
.icon-text_1134-1346 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1347 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 25px;
}

.table-item_1134-1348 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
  width: -webkit-fill-available;
}
.icon-text_1134-1349 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1350 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 25px;
}

.table-item_1134-1351 {
  padding: 10px 12px 10px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
  height: 68px;
  width: -webkit-fill-available;
}
.icon-text_1134-1352 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  background: #ffffff;
}
.text-label_1134-1353 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: wrap;
  width: 25px;
}

.column_1134-1375 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 140px;
}
.header-item_1134-1376 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1134-1377 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1379 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1380 {
  padding: 13px 12px 13px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1381 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1382 {
  padding: 13px 12px 13px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1383 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1384 {
  padding: 13px 12px 13px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1385 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1134-1400 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 140px;
}
.header-item_1134-1401 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1134-1402 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1404 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1405 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 110px;
  height: 68px;
}
.text-label_1134-1406 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1407 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 110px;
  height: 68px;
}
.text-label_1134-1408 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1409 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 110px;
  height: 68px;
}
.text-label_1134-1410 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1134-1425 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 170px;
}
.header-item_1134-1426 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1134-1427 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1429 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1430 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 110px;
  height: 68px;
}
.text-label_1134-1431 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1432 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 110px;
  height: 68px;
}
.text-label_1134-1433 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1434 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  width: 110px;
  height: 68px;
}
.text-label_1134-1435 {
  color: #17181a;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
}

.column_1134-1450 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 118px;
}
.header-item_1134-1451 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 10px;
  box-sizing: border-box;
  border-color: #f1f5f7;
  border-style: solid;
  border-bottom-width: 1px;
  width: -webkit-fill-available;
}
.table-item_1134-1452 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
}
.text-label_1134-1454 {
  color: #384455;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 500;
  text-align: left;
  text-wrap: nowrap;
}

.table-item_1134-1455 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
  width: -webkit-fill-available;
}
.sumbit-button_1134-1462 {
  width: -webkit-fill-available;
}

.table-item_1134-1463 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
  width: -webkit-fill-available;
}
.sumbit-button_1134-1470 {
  width: -webkit-fill-available;
}

.table-item_1134-1471 {
  padding: 0px 12px 0px 12px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 12px;
  box-sizing: border-box;
  height: 68px;
  width: -webkit-fill-available;
}
.sumbit-button_1134-1478 {
  width: -webkit-fill-available;
}
