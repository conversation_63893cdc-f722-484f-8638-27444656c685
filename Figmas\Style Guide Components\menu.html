<div data-layer="Menu" class="Menu" style="width: 1831px; height: 923px; position: relative; background: white; overflow: hidden">
  <div data-layer="Rectangle 275" class="Rectangle275" style="width: 1478.31px; height: 123.01px; left: 45.26px; top: 224.16px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Rectangle 290" class="Rectangle290" style="width: 1478.31px; height: 123.01px; left: 45.26px; top: 678.32px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Rectangle 276" class="Rectangle276" style="width: 1559.95px; height: 123.01px; left: 45.26px; top: 424.93px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="menu" class="Menu" style="width: 1440px; left: 63.74px; top: 247.97px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="Topbar" class="Topbar" style="width: 1440px; height: 80px; background: var(--white, white); border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
      <div data-layer="menu" class="Menu" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: center; display: inline-flex">
        <div data-layer="box" class="Box" style="flex: 1 1 0; align-self: stretch; padding-left: 30px; padding-right: 30px; padding-top: 12px; padding-bottom: 12px; justify-content: flex-start; align-items: center; display: flex">
          <div data-layer="frame" class="Frame" style="flex: 1 1 0; height: 46px; justify-content: space-between; align-items: center; display: flex">
            <div data-layer="Frame 840" class="Frame840" style="flex: 1 1 0; justify-content: flex-start; align-items: center; gap: 20px; display: flex">
              <div data-layer="logo" class="Logo" style="width: 240px; padding-left: 20px; padding-right: 20px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <img data-layer="stellarus-logo" class="StellarusLogo" style="width: 150px; height: 37.40px" src="data:image/png;base64,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" />
              </div>
            </div>
            <div data-layer="Frame 839" class="Frame839" style="justify-content: flex-end; align-items: center; gap: 16px; display: flex">
              <div data-layer="Menu Item" class="MenuItem" style="padding-left: 20px; padding-right: 20px; padding-top: 12px; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: flex">
                <div data-layer="Frame 838" class="Frame838" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                  <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
                  <div data-layer="base" class="Base" style="padding: 8px; background: rgba(56, 112, 184, 0.20); overflow: hidden; border-radius: 120px; justify-content: center; align-items: center; gap: 8px; display: flex">
                    <div data-svg-wrapper data-layer="icon_user" data-property-1="Profile_Circle" class="IconUser" style="position: relative">
                      <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.8421 11.6222C10.7838 11.6138 10.7088 11.6138 10.6421 11.6222C9.17546 11.5722 8.00879 10.3722 8.00879 8.89715C8.00879 7.38882 9.22546 6.16382 10.7421 6.16382C12.2505 6.16382 13.4755 7.38882 13.4755 8.89715C13.4671 10.3722 12.3088 11.5722 10.8421 11.6222Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M16.3593 17.1222C14.876 18.4805 12.9093 19.3056 10.7426 19.3056C8.57598 19.3056 6.60931 18.4805 5.12598 17.1222C5.20931 16.3389 5.70931 15.5722 6.60098 14.9722C8.88431 13.4556 12.6176 13.4556 14.8843 14.9722C15.776 15.5722 16.276 16.3389 16.3593 17.1222Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M10.7425 19.3056C15.3449 19.3056 19.0758 15.5746 19.0758 10.9722C19.0758 6.36988 15.3449 2.63892 10.7425 2.63892C6.14014 2.63892 2.40918 6.36988 2.40918 10.9722C2.40918 15.5746 6.14014 19.3056 10.7425 19.3056Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <div data-svg-wrapper data-layer="icon_arrow_down" class="IconArrowDown" style="position: relative">
                    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16.7422 10.9722L12.7422 14.9722L8.74219 10.9722" stroke="var(--light-primary, #809FB8)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="menu" class="Menu" style="width: 1440px; left: 63.74px; top: 702.13px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="Topbar" class="Topbar" style="width: 1440px; height: 80px; background: var(--white, white); border-bottom: 1px var(--light-borders, #F1F5F7) solid; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
      <div data-layer="menu" class="Menu" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: center; display: inline-flex">
        <div data-layer="box" class="Box" style="flex: 1 1 0; align-self: stretch; padding-left: 30px; padding-right: 30px; padding-top: 12px; padding-bottom: 12px; justify-content: flex-start; align-items: center; display: flex">
          <div data-layer="frame" class="Frame" style="flex: 1 1 0; height: 46px; justify-content: space-between; align-items: center; display: flex">
            <div data-layer="Frame 840" class="Frame840" style="flex: 1 1 0; justify-content: flex-start; align-items: center; gap: 20px; display: flex">
              <div data-layer="logo" class="Logo" style="width: 240px; padding-left: 20px; padding-right: 20px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <img data-layer="stellarus-logo" class="StellarusLogo" style="width: 150px; height: 37.40px" src="data:image/png;base64,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" />
              </div>
            </div>
            <div data-layer="Frame 839" class="Frame839" style="justify-content: flex-end; align-items: center; gap: 16px; display: flex">
              <div data-layer="Menu Item" class="MenuItem" style="padding-left: 20px; padding-right: 20px; padding-top: 12px; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: flex">
                <div data-layer="Frame 838" class="Frame838" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                  <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">Jane Chu</div>
                  <div data-layer="base" class="Base" style="padding: 8px; background: rgba(56, 112, 184, 0.20); overflow: hidden; border-radius: 120px; justify-content: center; align-items: center; gap: 8px; display: flex">
                    <div data-svg-wrapper data-layer="icon_user" data-property-1="Profile_Circle" class="IconUser" style="position: relative">
                      <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.8421 10.7828C10.7838 10.7745 10.7088 10.7745 10.6421 10.7828C9.17546 10.7328 8.00879 9.5328 8.00879 8.0578C8.00879 6.54946 9.22546 5.32446 10.7421 5.32446C12.2505 5.32446 13.4755 6.54946 13.4755 8.0578C13.4671 9.5328 12.3088 10.7328 10.8421 10.7828Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M16.3593 16.2829C14.876 17.6412 12.9093 18.4662 10.7426 18.4662C8.57598 18.4662 6.60931 17.6412 5.12598 16.2829C5.20931 15.4995 5.70931 14.7329 6.60098 14.1329C8.88431 12.6162 12.6176 12.6162 14.8843 14.1329C15.776 14.7329 16.276 15.4995 16.3593 16.2829Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M10.7425 18.4662C15.3449 18.4662 19.0758 14.7353 19.0758 10.1329C19.0758 5.53052 15.3449 1.79956 10.7425 1.79956C6.14014 1.79956 2.40918 5.53052 2.40918 10.1329C2.40918 14.7353 6.14014 18.4662 10.7425 18.4662Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <div data-svg-wrapper data-layer="icon_arrow" class="IconArrow" style="position: relative">
                    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16.7422 10.1328L12.7422 14.1328L8.74219 10.1328" stroke="var(--light-primary, #809FB8)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="menu" class="Menu" style="width: 1440px; left: 132.41px; top: 448.74px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="Topbar" class="Topbar" style="width: 1440px; height: 80px; background: var(--white, white); border-bottom: 1px var(--gray-2, #D9E1E7) solid; flex-direction: column; justify-content: center; align-items: flex-start; display: flex">
      <div data-layer="menu" class="Menu" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: center; display: inline-flex">
        <div data-layer="box" class="Box" style="flex: 1 1 0; align-self: stretch; padding-left: 30px; padding-right: 30px; padding-top: 12px; padding-bottom: 12px; justify-content: flex-start; align-items: center; display: flex">
          <div data-layer="frame" class="Frame" style="flex: 1 1 0; justify-content: space-between; align-items: center; display: flex">
            <div data-layer="Frame 840" class="Frame840" style="flex: 1 1 0; justify-content: flex-start; align-items: center; gap: 20px; display: flex">
              <div data-layer="logo" class="Logo" style="padding-left: 20px; padding-right: 20px; padding-top: 10px; padding-bottom: 10px; background: var(--white, white); justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                <img data-layer="stellarus-logo" class="StellarusLogo" style="width: 150px; height: 37.40px" src="data:image/png;base64,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" />
              </div>
            </div>
            <div data-layer="Frame 839" class="Frame839" style="justify-content: flex-end; align-items: center; gap: 16px; display: flex">
              <div data-layer="Menu Item" class="MenuItem" style="padding-left: 20px; padding-right: 20px; padding-top: 12px; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: flex">
                <div data-layer="Frame 838" class="Frame838" style="justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                  <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--text-black, #17181A); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Jane Chu</div>
                  <div data-layer="base" class="Base" style="padding: 8px; background: rgba(56, 112, 184, 0.20); overflow: hidden; border-radius: 120px; justify-content: center; align-items: center; gap: 8px; display: flex">
                    <div data-svg-wrapper data-layer="icon_user" data-property-1="Profile_Circle" class="IconUser" style="position: relative">
                      <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.5091 11.3936C10.4508 11.3853 10.3758 11.3853 10.3091 11.3936C8.84245 11.3436 7.67578 10.1436 7.67578 8.66864C7.67578 7.1603 8.89245 5.9353 10.4091 5.9353C11.9174 5.9353 13.1424 7.1603 13.1424 8.66864C13.1341 10.1436 11.9758 11.3436 10.5091 11.3936Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M16.0263 16.8937C14.543 18.252 12.5763 19.077 10.4096 19.077C8.24297 19.077 6.2763 18.252 4.79297 16.8937C4.8763 16.1104 5.3763 15.3437 6.26797 14.7437C8.5513 13.227 12.2846 13.227 14.5513 14.7437C15.443 15.3437 15.943 16.1104 16.0263 16.8937Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M10.4095 19.0771C15.0119 19.0771 18.7428 15.3461 18.7428 10.7437C18.7428 6.14136 15.0119 2.4104 10.4095 2.4104C5.80713 2.4104 2.07617 6.14136 2.07617 10.7437C2.07617 15.3461 5.80713 19.0771 10.4095 19.0771Z" stroke="#3870B8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <div data-svg-wrapper data-layer="icon_arrow_down" class="IconArrowDown" style="position: relative">
                    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16.4092 10.7437L12.4092 14.7437L8.40918 10.7437" stroke="var(--light-primary, #809FB8)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Spacing:" class="Spacing" style="width: 83px; height: 15.69px; left: 45.26px; top: 390.57px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Spacing:</div>
  <div data-layer="Styling:" class="Styling" style="width: 83px; height: 15.69px; left: 45.26px; top: 646.84px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Styling:</div>
  <div data-layer="Behavior: Sticky, does not scroll with the page." class="BehaviorStickyDoesNotScrollWithThePage" style="width: 1039.94px; height: 16px; left: 44.43px; top: 190.53px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Behavior: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Sticky, does not scroll with the page.</span></div>
  <div data-layer="Usage: Global menu that is full width and is used on all pages. Clicking the logo links back to the dashboard page, and current state clicking the name/icon/dropdown does nothing." class="UsageGlobalMenuThatIsFullWidthAndIsUsedOnAllPagesClickingTheLogoLinksBackToTheDashboardPageAndCurrentStateClickingTheNameIconDropdownDoesNothing" style="width: 1478.31px; left: 45.26px; top: 128.91px; position: absolute"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Usage: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; word-wrap: break-word">Global menu that is full width and is used on all pages. Clicking the logo links back to the dashboard page, and current state clicking the name/icon/dropdown does nothing.</span></div>
  <div data-layer="Menu" class="Menu" style="left: 45.26px; top: 62.80px; position: absolute; color: black; font-size: 36px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Menu</div>
  <div data-layer="Rectangle 248" class="Rectangle248" style="width: 30px; height: 79.63px; left: 1542.07px; top: 449.11px; position: absolute; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 252" class="Rectangle252" style="width: 30px; height: 79.97px; left: 132.76px; top: 448.77px; position: absolute; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 249" class="Rectangle249" style="width: 12px; height: 1440px; left: 132.41px; top: 460.74px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 277" class="Rectangle277" style="width: 10px; height: 189.65px; left: 162.76px; top: 470.74px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-svg-wrapper data-layer="Rectangle 279" class="Rectangle279" style="left: 162.76px; top: 460.74px; position: absolute">
    <svg width="21" height="57" viewBox="0 0 21 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.761719" y="0.743652" width="20" height="55.9849" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 280" class="Rectangle280" style="left: 332.41px; top: 460.74px; position: absolute">
    <svg width="21" height="57" viewBox="0 0 21 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.40918" y="0.743652" width="20" height="55.9849" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-layer="Rectangle 278" class="Rectangle278" style="width: 10px; height: 189.65px; left: 162.76px; top: 516.73px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 250" class="Rectangle250" style="width: 12px; height: 1440px; left: 132.41px; top: 528.71px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 103.51px; top: 449.61px; position: absolute">
    <svg width="25" height="82" viewBox="0 0 25 82" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M24.9717 0.606201H14.7334V80.6062H24.9717M14.6367 40.6062H0.513672" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 132.76px; top: 579.95px; position: absolute">
    <svg width="1441" height="26" viewBox="0 0 1441 26" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.761718 0.949463L0.761718 11.1877L1440.07 11.1877L1440.07 0.9494M720.416 11.2844L720.416 25.4074" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1576.41px; top: 448.74px; position: absolute">
    <svg width="25" height="14" viewBox="0 0 25 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.40918 12.7437L10.6475 12.7437L10.6475 0.743651L0.409181 0.743651M10.7441 6.74365L24.8672 6.74365" stroke="black"/>
    </svg>
  </div>
  <div data-layer="12px" class="Px" style="left: 1605.21px; top: 454.74px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12px</div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 132.76px; top: 532.52px; position: absolute">
    <svg width="32" height="21" viewBox="0 0 32 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.761718 0.519531L0.761718 8.79978L30.7617 8.79978L30.7617 0.519529M15.7617 8.87797L15.7617 20.3" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 332.62px; top: 518.74px; position: absolute">
    <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.617187 0.743652L0.617187 9.02391L20.6172 9.0239L20.6172 0.743651M10.6172 9.1021L10.6172 20.5242" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 354.91px; top: 506.73px; position: absolute">
    <svg width="21" height="12" viewBox="0 0 21 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.908935 10.7285L9.18919 10.7285L9.18919 0.728517L0.908934 0.728517M9.26738 5.72851L20.6895 5.72851" stroke="black"/>
    </svg>
  </div>
  <div data-layer="80px" class="Px" style="left: 65.81px; top: 489.61px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">80px</div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1632.21px; top: 463.41px; position: absolute">
    <svg width="25" height="51" viewBox="0 0 25 51" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.207031 49.7285L10.4453 49.7285L10.4453 1.41015L0.207035 1.41015M10.542 25.5693L24.665 25.5693" stroke="black"/>
    </svg>
  </div>
  <div data-layer="items aligned center" class="ItemsAlignedCenter" style="left: 1661.07px; top: 487.73px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">items aligned center</div>
  <div data-layer="width: full window" class="WidthFullWindow" style="left: 797.42px; top: 615.19px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">width: full window</div>
  <div data-layer="30px" class="Px" style="left: 131.76px; top: 560.16px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">30px</div>
  <div data-layer="20px" class="Px" style="left: 328.15px; top: 546.38px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">20px</div>
  <div data-layer="10px" class="Px" style="left: 379.48px; top: 511.73px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">10px</div>
  <div data-layer="Rectangle 281" class="Rectangle281" style="width: 10px; height: 191.65px; left: 1350.67px; top: 470.74px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-svg-wrapper data-layer="Rectangle 282" class="Rectangle282" style="left: 1350.67px; top: 460.74px; position: absolute">
    <svg width="21" height="57" viewBox="0 0 21 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.666992" y="0.743652" width="20" height="55.9849" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 285" class="Rectangle285" style="left: 1438.41px; top: 460.74px; position: absolute">
    <svg width="13" height="57" viewBox="0 0 13 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.40918" y="0.743652" width="12" height="55.9849" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 286" class="Rectangle286" style="left: 1486.41px; top: 460.74px; position: absolute">
    <svg width="13" height="57" viewBox="0 0 13 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.40918" y="0.743652" width="12" height="55.9849" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 283" class="Rectangle283" style="left: 1522.31px; top: 460.74px; position: absolute">
    <svg width="21" height="57" viewBox="0 0 21 57" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.314453" y="0.743652" width="20" height="55.9849" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-layer="Rectangle 284" class="Rectangle284" style="width: 10px; height: 191.65px; left: 1350.67px; top: 516.73px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1522.52px; top: 518.74px; position: absolute">
    <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.52246 0.743652L0.52246 9.02391L20.5225 9.0239L20.5225 0.743651M10.5225 9.1021L10.5225 20.5242" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1438.88px; top: 518.74px; position: absolute">
    <svg width="14" height="21" viewBox="0 0 14 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.880859 0.743652L0.880859 9.02391L12.8809 9.0239L12.8809 0.743651M6.88086 9.1021L6.88086 20.5242" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1486.88px; top: 518.74px; position: absolute">
    <svg width="14" height="21" viewBox="0 0 14 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.880859 0.743652L0.880859 9.02391L12.8809 9.0239L12.8809 0.743651M6.88086 9.1021L6.88086 20.5242" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 1544.81px; top: 506.73px; position: absolute">
    <svg width="21" height="12" viewBox="0 0 21 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.814208 10.7285L9.09446 10.7285L9.09446 0.728517L0.814208 0.728517M9.17265 5.72851L20.5947 5.72851" stroke="black"/>
    </svg>
  </div>
  <div data-layer="20px" class="Px" style="left: 1518.05px; top: 546.38px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">20px</div>
  <div data-layer="12px" class="Px" style="left: 1431.41px; top: 546.38px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12px</div>
  <div data-layer="12px" class="Px" style="left: 1479.41px; top: 546.38px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12px</div>
  <div data-layer="10px" class="Px" style="left: 1569.38px; top: 511.73px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">10px</div>
  <div data-layer="body" class="Body" style="left: 1325.24px; top: 813.73px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">body</div>
  <div data-layer="component: icon_arrow_down" class="ComponentIconArrowDown" style="left: 1518.30px; top: 733.32px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">component:<br/>icon_arrow_down</div>
  <div data-layer="component: base icon_user" class="ComponentBaseIconUser" style="left: 1368.43px; top: 826.12px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">component: <br/>base<br/>icon_user</div>
  <div data-svg-wrapper data-layer="Vector 17" class="Vector17" style="left: 1341.24px; top: 754.13px; position: absolute">
    <svg width="2" height="49" viewBox="0 0 2 49" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.24219 48.8694L1.24219 0.132812" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 19" class="Vector19" style="left: 1463.37px; top: 742.13px; position: absolute">
    <svg width="50" height="2" viewBox="0 0 50 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M49.1106 1.13281L0.374023 1.13281" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 18" class="Vector18" style="left: 1399.74px; top: 769.53px; position: absolute">
    <svg width="2" height="50" viewBox="0 0 2 50" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.74219 49.2654L0.742188 0.528809" stroke="black"/>
    </svg>
  </div>
</div>