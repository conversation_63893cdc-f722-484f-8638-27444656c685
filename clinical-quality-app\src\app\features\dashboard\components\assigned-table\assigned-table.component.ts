import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { AssignedChart, TableColumn, DEFAULT_TABLE_COLUMNS } from '../../../../core/data/models/chart-data.models';
import { ButtonComponent } from '../../../../shared/components/buttons/button.component';

@Component({
  selector: 'app-assigned-table',
  standalone: true,
  imports: [CommonModule, RouterModule, ButtonComponent],
  templateUrl: './assigned-table.component.html',
  styleUrl: './assigned-table.component.scss'
})
export class AssignedTableComponent {
  @Input() charts: AssignedChart[] = [];
  @Input() searchText: string = '';

  // Column definitions with separate name fields from CSV data
  columns = [
    { field: 'memberId', header: 'Member ID', width: '140px' },
    { field: 'firstName', header: 'First name', width: '120px' },
    { field: 'lastName', header: 'Last name', width: '120px' },
    { field: 'middleName', header: 'Middle name', width: '120px' },
    { field: 'dob', header: 'DOB', width: '120px' },
    { field: 'lob', header: 'LOB', width: '100px' },
    { field: 'measure', header: 'Measure', width: '120px' },
    { field: 'review1', header: 'Review 1', width: '140px' },
    { field: 'review2', header: 'Review 2', width: '140px' },
    { field: 'assigned', header: 'Assigned', width: '160px' },
    { field: 'status', header: 'Status', width: '140px' }
  ];

  constructor(private router: Router) { }

  // Method to get field value safely
  getFieldValue(chart: AssignedChart, field: string): string {
    return (chart as any)[field] || '';
  }

  // Method to navigate to chart review page
  navigateToChartReview(chart: AssignedChart): void {
    if (chart && chart.status && chart.status.toLowerCase() === 'review' && chart.memberId) {
      this.router.navigate(['/chart-review', chart.memberId]);
    } else if (chart && chart.status && chart.status.toLowerCase() === 'review' && !chart.memberId) {
      console.error('Chart data is missing memberId for navigation but status is review', chart);
    }
  }

  // Method to filter charts based on search text
  get filteredCharts() {
    if (!this.searchText) {
      return this.charts;
    }

    const searchLower = this.searchText.toLowerCase();
    return this.charts.filter(chart => {
      return Object.values(chart).some(value =>
        value && typeof value === 'string' ?
          value.toLowerCase().includes(searchLower) :
          String(value).toLowerCase().includes(searchLower)
      );
    });
  }


}
