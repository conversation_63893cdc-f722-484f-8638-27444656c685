<div class="inclusions-tab-content" [formGroup]="formGroup">

  <!-- Telehealth Checkbox -->
  <div class="frame-942">
    <app-checkbox
      label="Telehealth"
      formControlName="telehealth"
      [disabled]="disabled"
      (change)="onFormFieldChange()">
    </app-checkbox>
  </div>

  <!-- Form Fields Section -->
  <div class="frame-936">

    <!-- Sys, Dias, and Date of Service in same row -->
    <div class="frame-939">
      <div class="frame-940">
        <span class="text-sys">Sys</span>
        <input
          type="text"
          class="dropdown-inclusion"
          placeholder="Value"
          formControlName="sys"
          [disabled]="disabled"
          (input)="onFormFieldChange()">
      </div>

      <div class="frame-941">
        <span class="text-dias">Dias</span>
        <input
          type="text"
          class="dropdown-inclusion"
          placeholder="Value"
          formControlName="dias"
          [disabled]="disabled"
          (input)="onFormFieldChange()">
      </div>

      <div class="frame-937">
        <span class="text-date-of-service">Date of Service</span>
        <app-calendar
          placeholder="MM/DD/YY"
          formControlName="dateOfService"
          [disabled]="disabled"
          class="calendar"
          (dateChange)="onFormFieldChange()">
        </app-calendar>
      </div>
    </div>

  </div>

  <!-- Notes Field (separate section) -->
  <div class="frame-938">
    <span class="text-notes">Notes</span>
    <app-notes
      label=""
      placeholder="Notes"
      formControlName="notes"
      [disabled]="disabled"
      class="notes"
      (notesChange)="onFormFieldChange()">
    </app-notes>
  </div>

</div>
