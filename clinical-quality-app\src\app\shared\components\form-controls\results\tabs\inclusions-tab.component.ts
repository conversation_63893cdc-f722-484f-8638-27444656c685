import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CalendarComponent } from '../../calendar/calendar.component';
import { NotesComponent } from '../../../notes/notes.component';
import { CheckboxComponent } from '../../checkbox/checkbox.component';

@Component({
  selector: 'app-inclusions-tab',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CalendarComponent,
    NotesComponent,
    CheckboxComponent
  ],
  templateUrl: './inclusions-tab.component.html',
  styleUrls: ['./inclusions-tab.component.scss']
})
export class InclusionsTabComponent implements OnInit {
  @Input() formGroup!: FormGroup;
  @Input() disabled: boolean = false;
  @Input() id: string = '';

  @Output() fieldChange = new EventEmitter<void>();

  constructor() {}

  ngOnInit(): void {
    // Component initialization
  }

  onFormFieldChange(): void {
    this.fieldChange.emit();
  }
}
