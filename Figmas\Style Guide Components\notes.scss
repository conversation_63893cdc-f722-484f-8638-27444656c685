@import "src/app/tokens.scss";

.notes_506-14219 {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  background: #ffffff;
  width: 1094px;
  height: 1682px;
}
.rectangle-325_506-14296 {
  top: 303px;
  left: 279px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-315_506-14220 {
  top: 844px;
  left: 284px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-328_506-14312 {
  top: 1082px;
  left: 284px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-329_506-14331 {
  top: 1211px;
  left: 284px;
  position: absolute;
  display: flex;
  display: flex;
}

.table_506-14300 {
  top: 858px;
  left: 301px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 410px;
}
.text-area-field_506-14301 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.input-field_506-14302 {
  padding: 8px 12px 4px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 88px;
  width: 100%;
}
.text_506-14303 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.text-input-text_506-14304 {
  color: $variable-collection-gray-3;
  @include body-text;
  font-weight: 300;
  text-align: left;
  height: 11px;
  width: 100%;
}

.lead-icon---text_506-14305 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.text_506-14306 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.text-input-text_506-14307 {
  color: $variable-collection-gray-3;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: right;
  text-wrap: wrap;
  width: 100%;
}

.table_506-14313 {
  top: 1097px;
  left: 301px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 410px;
}
.text-area-field_506-14314 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.input-field_506-14315 {
  padding: 8px 12px 4px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 88px;
  width: 100%;
}
.text_506-14316 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.text-input-text_506-14317 {
  color: $variable-collection-gray-3;
  @include body-text;
  font-weight: 300;
  text-align: left;
  height: 11px;
  width: 100%;
}

.lead-icon---text_506-14318 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.text_506-14319 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.table_506-14332 {
  top: 1226px;
  left: 301px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
  width: 410px;
}
.text-area-field_506-14333 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.input-field_506-14334 {
  padding: 8px 12px 4px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: $variable-collection-gray-3;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 88px;
  width: 100%;
}
.text_506-14335 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.text-input-text_506-14336 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  height: 11px;
  width: 100%;
}

.lead-icon---text_506-14337 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.text_506-14338 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state--allows-copy-paste-_506-14223 {
  top: 181px;
  left: 47px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  width: 1040px;
  height: 16px;
}

.text-behavior--scrolls-with-page--interactive-in-both-default-and-entered-state--allows-copy-paste-_506-14224 {
  top: 181px;
  left: 47px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  width: 1040px;
  height: 16px;
}

.text-entry--date-entry-is-manual--done-by-typing-or-copy-paste-_506-14225 {
  top: 225px;
  left: 47px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  width: 1040px;
  height: 16px;
}

.text-usage--text-entry-for-notes-related-to-chart-review-_506-14226 {
  top: 137px;
  left: 48px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  letter-spacing: 0%;
  text-decoration: none;
  text-align: left;
  text-wrap: wrap;
  width: 1478px;
  height: 22px;
}

.text-notes_506-14227 {
  top: 63px;
  left: 47px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 36px;
  font-family: Urbane;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  text-wrap: nowrap;
  width: 112px;
  height: 43px;
}

.text-default--no-text-entered_506-14229 {
  top: 350px;
  left: 757px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 142px;
  height: 4px;
}

.vector-40_506-14232 {
  top: 350px;
  left: 716px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-spacing-_506-14260 {
  top: 798px;
  left: 64px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.text-styling-_506-14261 {
  top: 1020px;
  left: 71px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 18px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 600;
  text-align: left;
  width: 83px;
  height: 16px;
}

.rectangle-322_506-14262 {
  top: 866px;
  left: 301px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-326_506-14308 {
  top: 946px;
  left: 301px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_506-14267 {
  top: 858px;
  left: 281px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector_506-14310 {
  top: 960px;
  left: 301px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-8px_506-14268 {
  top: 862px;
  left: 259px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 20px;
  height: 4px;
}

.text-12px_506-14311 {
  top: 965px;
  left: 296px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 10px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 22px;
  height: 4px;
}

.rectangle-318_506-14277 {
  top: 858px;
  left: 301px;
  position: absolute;
  display: flex;
  display: flex;
}

.rectangle-327_506-14309 {
  top: 858px;
  left: 699px;
  position: absolute;
  display: flex;
  display: flex;
}

.text-corner-radius--8-border-style--solid-border-color--gray-1_506-14278 {
  top: 1076px;
  left: 738px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 117px;
  height: 60px;
}

.text-font--input-text-color--gray-3_506-14279 {
  top: 1098px;
  left: 178px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 90px;
  height: 24px;
}

.text-font--input-text-color--text-black--when-typing-or-typed-_506-14341 {
  top: 1232px;
  left: 134px;
  position: absolute;
  display: flex;
  color: #000000;
  font-size: 12px;
  font-family: Urbane;
  line-height: 20px;
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300;
  text-align: left;
  text-wrap: nowrap;
  width: 140px;
  height: 44px;
}

.vector-36_506-14283 {
  top: 1114px;
  left: 282px;
  position: absolute;
  display: flex;
  display: flex;
}

.vector-47_506-14343 {
  top: 1243px;
  left: 243px;
  position: absolute;
  display: flex;
  display: flex;
}

.notes_506-14507 {
  top: 303px;
  left: 278px;
  position: absolute;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 5px;
  border-color: #9747ff;
  border-style: dashed;
  border-width: 1px;
}
.property-1-active_506-14506 {
  top: 129px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.text-area-field_506-14499 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.input-field_506-14500 {
  padding: 8px 12px 4px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: $variable-collection-gray-3;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 88px;
  width: 100%;
}
.text_506-14501 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.text-input-text_506-14502 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  height: 11px;
  width: 100%;
}

.lead-icon---text_506-14503 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.text_506-14504 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.property-1-exclusions-active_749-9276 {
  top: 355px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.text-area-field_749-9277 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.input-field_749-9278 {
  padding: 8px 12px 4px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: $variable-collection-gray-3;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 88px;
  width: 100%;
}
.text_749-9279 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.text-input-text_749-9280 {
  color: $variable-collection-text-black;
  @include body-text;
  font-weight: 300;
  text-align: left;
  height: 11px;
  width: 100%;
}

.lead-icon---text_749-9281 {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.text_749-9282 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.property-1-default_506-14505 {
  top: 20px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.text-area-field_506-14288 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.input-field_506-14289 {
  padding: 8px 12px 4px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 88px;
  width: 100%;
}
.text_506-14290 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.text-input-text_506-14291 {
  color: $variable-collection-gray-3;
  @include body-text;
  font-weight: 300;
  text-align: left;
  height: 11px;
  width: 100%;
}

.property-1-exclusions-default_749-9283 {
  top: 242px;
  left: 20px;
  position: absolute;
  display: flex;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 0px;
  box-sizing: border-box;
}
.text-area-field_749-9284 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}
.input-field_749-9285 {
  padding: 8px 12px 4px 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 16px;
  box-sizing: border-box;
  border-radius: 8px;
  border-color: $variable-collection-gray-2;
  border-style: solid;
  border-width: 1px;
  background: #ffffff;
  height: 88px;
  width: 100%;
}
.text_749-9286 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 2px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.text-input-text_749-9287 {
  color: $variable-collection-gray-3;
  @include body-text;
  font-weight: 300;
  text-align: left;
  height: 11px;
  width: 100%;
}
