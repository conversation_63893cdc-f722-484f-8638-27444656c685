import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, Inject, PLATFORM_ID, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { NgxExtendedPdfViewerModule, NgxExtendedPdfViewerService, pdfDefaultOptions } from 'ngx-extended-pdf-viewer';
import { FormsModule } from '@angular/forms';
import { PdfService } from '../../services/pdf.service'; // Removed PdfSearchResult as it's not used here
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-pdf-viewer',
  standalone: true,
  imports: [CommonModule, NgxExtendedPdfViewerModule, FormsModule],
  templateUrl: './pdf-viewer.component.html',
  styleUrl: './pdf-viewer.component.scss',
  schemas: [NO_ERRORS_SCHEMA] // Add this to suppress property binding errors
})
export class PdfViewerComponent implements OnInit, AfterViewInit, OnDestroy {
  // PDF viewer state
  currentPage: number = 1;
  totalPages: number = 0;
  zoom: string | number = 'page-fit'; // Set to page-fit for default zoom

  // Search state
  searchText: string = '';
  caseSensitive: boolean = false;
  wholeWord: boolean = false;
  // searchResults: PdfSearchResult[] = []; // Commented out as search functionality is not fully implemented here
  // currentSearchResult: number = -1; // Commented out
  isSearching: boolean = false;

  // File state
  currentFileName: string = '';

  private subscriptions: Subscription = new Subscription();
  public isBrowser: boolean = false; // Initialize with a default value
  public pdfBase64Source: string | null = null; // Store the base64 source

  // Configuration options
  public scrollMode: number = 0; // 0 = vertical (default), 1 = horizontal, 2 = wrapped
  public minZoom: number = 0.5; // Already in decimal format (50%)
  public maxZoom: number = 2.0; // Already in decimal format (200%)
  public renderTextMode: number = 2; // 0 = disabled, 1 = enabled, 2 = enhanced
  public renderInteractiveForms: boolean = true;
  public viewerPositionTop: number = 0;
  public viewerHeight: string = '100%';

  constructor(
    private extPdfViewerService: NgxExtendedPdfViewerService, // Renamed to avoid conflict if PdfService is also named pdfService
    private pdfServiceInstance: PdfService, // Injected PdfService
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);

    // Configure global PDF.js options
    if (this.isBrowser) {
      pdfDefaultOptions.disableStream = false;
      pdfDefaultOptions.disableAutoFetch = false;
      pdfDefaultOptions.defaultZoomValue = 'page-fit'; // Set default zoom to page-fit

      // Configure annotation colors using type assertion to bypass TypeScript checking
      // since annotationEditorParams might not be in the type definition but is supported at runtime
      const annotationParams = {
        // Text editor color (for adding text annotations)
        defaultTextColor: '#FF0000',  // Red text

        // Highlight color
        defaultHighlightColor: '#FFFF00',  // Yellow highlight

        // Drawing/pen color
        defaultDrawColor: '#0000FF',  // Blue pen

        // Ink thickness for drawing
        defaultInkThickness: 3,

        // Ink opacity (0-1)
        defaultInkOpacity: 0.8
      };

      (pdfDefaultOptions as any).annotationEditorParams = annotationParams;

      console.log('[PdfViewer] Configured annotation colors:', annotationParams);
      console.log('[PdfViewer] Set default zoom to page-fit');
    }
  }

  ngOnInit(): void {
    console.log('[PdfViewer] ngOnInit called');
    console.log('[PdfViewer] Browser environment:', this.isBrowser ? 'Browser' : 'Server');
    console.log('[PdfViewer] Initial zoom value:', this.zoom, '(decimal format)');
    console.log('[PdfViewer] Scroll mode:', this.getScrollModeName(this.scrollMode));
    console.log('[PdfViewer] Forms enabled:', this.renderInteractiveForms);

    // Check if PDF.js worker is configured
    if (this.isBrowser) {
      console.log('[PdfViewer] PDF worker configuration:', (window as any).pdfWorkerSrc);

      // Subscribe to the base64 data URL from PdfService
      this.subscriptions.add(
        this.pdfServiceInstance.pdfBase64DataUrl$.subscribe(dataUrl => {
          if (dataUrl) {
            console.log('[PdfViewer] Received base64 data URL from PdfService.');
            this.pdfBase64Source = dataUrl;
            // Extract file name if possible (optional, might need more robust parsing)
            // For now, let's set a generic name or leave it as is if onFileSelected handles it.
            if (!this.currentFileName && dataUrl.startsWith('data:application/pdf;base64,')) {
                this.currentFileName = "loaded_from_service.pdf";
            }
          } else {
            // Potentially clear the viewer if dataUrl is null (e.g., on pdfService.closePdf())
            // However, ngx-extended-pdf-viewer might handle null src by showing nothing.
            // If explicit clearing is needed:
            // this.pdfBase64Source = null;
            // this.currentFileName = '';
            // this.totalPages = 0;
            console.log('[PdfViewer] Received null base64 data URL from PdfService.');
          }
        })
      );
    }

    // Log the version of ngx-extended-pdf-viewer being used
    console.log('[PdfViewer] Using ngx-extended-pdf-viewer version 23.1.1');
  }

  ngAfterViewInit(): void {
    console.log('[PdfViewer] ngAfterViewInit called');
    console.log('[PdfViewer] PDF viewer ready for rendering');

    // Add a small delay to check if the viewer is properly initialized
    setTimeout(() => {
      console.log('[PdfViewer] Delayed check - Zoom value:', this.zoom, '(decimal format)');
      console.log('[PdfViewer] Viewer container dimensions:', this.getViewerContainerDimensions());

      // Set default colors for annotation tools by directly manipulating the color inputs
      this.setDefaultAnnotationColors();
    }, 1000);
  }

  /**
   * Sets the default colors for annotation tools by directly manipulating the color inputs
   */
  private setDefaultAnnotationColors(): void {
    if (!this.isBrowser) return;

    try {
      // Text color
      const textColorInput = document.getElementById('editorFreeTextColor') as HTMLInputElement;
      if (textColorInput) {
        textColorInput.value = '#FF0000'; // Red
        console.log('[PdfViewer] Set text color input to:', textColorInput.value);

        // Trigger change event to ensure the PDF viewer recognizes the change
        textColorInput.dispatchEvent(new Event('input', { bubbles: true }));
      } else {
        console.warn('[PdfViewer] Text color input element not found');
      }

      // Draw/ink color
      const drawColorInput = document.getElementById('editorInkColor') as HTMLInputElement;
      if (drawColorInput) {
        drawColorInput.value = '#0000FF'; // Blue
        console.log('[PdfViewer] Set draw color input to:', drawColorInput.value);

        // Trigger change event
        drawColorInput.dispatchEvent(new Event('input', { bubbles: true }));
      } else {
        console.warn('[PdfViewer] Draw color input element not found');
      }

      // Highlight color
      const highlightColorInput = document.getElementById('editorHighlightColor') as HTMLInputElement;
      if (highlightColorInput) {
        highlightColorInput.value = '#FFFF00'; // Yellow
        console.log('[PdfViewer] Set highlight color input to:', highlightColorInput.value);

        // Trigger change event
        highlightColorInput.dispatchEvent(new Event('input', { bubbles: true }));
      } else {
        console.warn('[PdfViewer] Highlight color input element not found');
      }
    } catch (error) {
      console.error('[PdfViewer] Error setting default annotation colors:', error);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Handles file selection from the file input
   * @param event The file input change event
   */
  onFileSelected(event: Event): void {
    // Ensure this only runs in the browser
    if (!this.isBrowser) {
      console.error('[PdfViewer] PDF loading attempted during server-side rendering');
      return;
    }

    console.log('[PdfViewer] File selection event triggered');

    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      console.log(`[PdfViewer] File selected: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);

      if (file.type === 'application/pdf') {
        this.currentFileName = file.name;
        this.pdfBase64Source = null; // Reset while loading
        console.log('[PdfViewer] Valid PDF file selected, beginning conversion to base64');

        // Read file as base64
        const reader = new FileReader();

        reader.onloadstart = () => {
          console.log('[PdfViewer] FileReader: Started loading file');
        };

        reader.onprogress = (event) => {
          if (event.lengthComputable) {
            const percentLoaded = Math.round((event.loaded / event.total) * 100);
            console.log(`[PdfViewer] FileReader: Loading progress: ${percentLoaded}%`);
          }
        };

        reader.onload = (e) => {
          console.log('[PdfViewer] FileReader: File successfully loaded');
          const rawResult = e.target?.result as string;

          this.pdfBase64Source = rawResult;

          // Log the first 100 chars and length of the assigned base64 string
          const base64Preview = this.pdfBase64Source?.substring(0, 100) + '...';
          console.log(`[PdfViewer] Assigned pdfBase64Source length: ${this.pdfBase64Source?.length ?? 'null'}`);
          console.log(`[PdfViewer] Assigned pdfBase64Source (preview): ${base64Preview}`);

          // Check if it starts with the correct data URL prefix for PDFs
          if (this.pdfBase64Source?.startsWith('data:application/pdf;base64,')) {
            console.log('[PdfViewer] Base64 data has correct PDF MIME type prefix');
          } else {
            console.warn('[PdfViewer] Base64 data does not have expected PDF MIME type prefix');
          }

          console.log('[PdfViewer] PDF base64 source set, viewer should update');
        };

        reader.onerror = (error) => {
          console.error('[PdfViewer] FileReader error:', error);
          this.pdfBase64Source = null;
          this.currentFileName = '';
          this.totalPages = 0;
          alert('Error reading the selected PDF file.');
        };

        console.log('[PdfViewer] Starting FileReader.readAsDataURL...');
        reader.readAsDataURL(file);
      } else {
        this.pdfBase64Source = null;
        this.currentFileName = '';
        this.totalPages = 0;
        console.error(`[PdfViewer] Invalid file type: ${file.type}, expected application/pdf`);
        alert('Please select a valid PDF file.');
      }
    } else {
      console.warn('[PdfViewer] No file selected or file input event without files');
      this.pdfBase64Source = null;
      this.currentFileName = '';
      this.totalPages = 0;
    }
  }

  /**
   * Loads a PDF from a File object
   * @param file The PDF file to load
   */
  /**
   * Handles PDF loaded event
   * @param pdf The loaded PDF document
   */
  onPdfLoaded(pdf: any): void {
    console.log('[PdfViewer] PDF loaded event triggered');
    console.log('[PdfViewer] PDF document:', pdf);

    if (pdf && pdf.numPages) {
      this.totalPages = pdf.numPages;
      console.log(`[PdfViewer] PDF loaded with ${this.totalPages} pages`);
    }

    // Log viewer dimensions after PDF is loaded
    setTimeout(() => {
      console.log('[PdfViewer] Viewer dimensions after PDF load:', this.getViewerContainerDimensions());
      console.log('[PdfViewer] Current zoom after PDF load:', this.zoom, '(decimal format)');
    }, 500);
  }

  /**
   * Handles page change events
   * @param pageNumber The new page number
   */
  onPageChange(pageNumber: number): void {
    console.log(`[PdfViewer] Page changed to ${pageNumber}`);
    this.currentPage = pageNumber;
  }

  /**
   * Handles zoom change events from the PDF viewer
   * @param newZoom New zoom level (as a decimal, e.g., 1.0 for 100%)
   */
  onZoomChange(newZoom: string | number): void {
    // We still need to update our internal zoom property for the [zoom] binding
    // but we no longer display it in the UI or provide external controls
    const zoomValue = typeof newZoom === 'string' ? parseFloat(newZoom) : newZoom;
    console.log(`[PdfViewer] PDF viewer zoom changed to ${zoomValue}`);

    // Store the zoom value without multiplying by 100
    // This prevents the scaling issue where 1.9 became 190%
    this.zoom = zoomValue;
  }

  /**
   * Performs a search across the PDF
   */

  /**
   * Handles PDF viewer errors
   * @param error The error object from the PDF viewer
   */
  /**
   * Handles PDF viewer errors
   * @param error The error object from the PDF viewer
   */
  onPdfViewerError(error: any): void {
    console.error('[PdfViewer] PDF Viewer Error:', error);
    // Log additional context that might help diagnose the issue
    console.error('[PdfViewer] PDF Viewer Context:', {
      hasBase64Source: !!this.pdfBase64Source,
      base64SourceLength: this.pdfBase64Source ? this.pdfBase64Source.length : 0,
      base64SourcePrefix: this.pdfBase64Source ? this.pdfBase64Source.substring(0, 50) : 'N/A',
      fileName: this.currentFileName,
      totalPages: this.totalPages,
      workerSrc: this.isBrowser ? (window as any).pdfWorkerSrc : 'N/A',
      viewerDimensions: this.getViewerContainerDimensions(),
      scrollMode: this.scrollMode,
      scrollModeName: this.getScrollModeName(this.scrollMode),
      renderInteractiveForms: this.renderInteractiveForms,
      ngxExtendedPdfViewerVersion: '23.1.1'
    });
  }

  /**
   * Gets the name of the scroll mode
   * @param mode The scroll mode number
   * @returns The name of the scroll mode
   */
  private getScrollModeName(mode: number): string {
    switch (mode) {
      case 0: return 'Vertical';
      case 1: return 'Horizontal';
      case 2: return 'Wrapped';
      default: return 'Unknown';
    }
  }

  /**
   * Gets the dimensions of the viewer container
   * @returns An object with the width and height of the viewer container
   */
  private getViewerContainerDimensions(): { width: string, height: string } {
    if (!this.isBrowser) {
      return { width: 'N/A', height: 'N/A' };
    }

    const container = document.querySelector('.pdf-viewer-wrapper') as HTMLElement;
    if (!container) {
      return { width: 'Container not found', height: 'Container not found' };
    }

    return {
      width: `${container.clientWidth}px`,
      height: `${container.clientHeight}px`
    };
  }
}
