<div data-layer="Results V2" class="ResultsV2" style="width: 1082px; height: 2606px; position: relative; background: white; overflow: hidden">
  <div data-layer="Rectangle 325" class="Rectangle325" style="width: 556.04px; height: 392.87px; left: 236.95px; top: 1545.34px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Rectangle 343" class="Rectangle343" style="width: 556.04px; height: 392.87px; left: 236.95px; top: 1545.34px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Notes" class="Notes" style="width: 517px; padding: 20px; left: 256.47px; top: 1559.27px; position: absolute; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
    <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
      <div data-layer="Frame 929" class="Frame929" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
          <div data-layer="label" class="Label" style="color: #17181A; font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Results</div>
          <div data-layer="right corner" class="RightCorner" style="flex: 1 1 0; align-self: stretch; position: relative; justify-content: flex-end; align-items: center; gap: 12px; display: flex">
            <div data-layer="8px" class="Px" style="left: 95.27px; top: 28px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">8px</div>
            <div data-layer="16px" class="Px" style="left: 109.75px; top: 15px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">16px</div>
            <div data-layer="8px" class="Px" style="left: 126.25px; top: 28px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">8px</div>
          </div>
        </div>
        <div data-layer="menu" class="Menu" style="align-self: stretch; height: 41px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="second menu" class="SecondMenu" style="align-self: stretch; background: var(--white, white); border-bottom: 1px var(--gray-2, #D9E1E7) solid; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="bradcrumb" class="Bradcrumb" style="height: 40px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
              <div data-layer="box" class="Box" style="flex: 1 1 0; justify-content: flex-start; align-items: center; display: inline-flex">
                <div data-layer="menu" class="Menu" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 16px; display: flex">
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; border-bottom: 1px var(--primary-blue, #3870B8) solid; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--primary-blue, #3870B8); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Inclusions</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Exclusions</div>
                      </div>
                    </div>
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">None found</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div data-layer="Frame 916" class="Frame916" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: flex">
      <div data-layer="Frame 942" class="Frame942" style="justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
        <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
          <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="0.96875" y="0.975586" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
          </svg>
        </div>
        <div data-layer="Teleheath" class="Teleheath" style="color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Teleheath</div>
      </div>
      <div data-layer="Frame 936" class="Frame936" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Frame 939" class="Frame939" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
          <div data-layer="Frame 940" class="Frame940" style="width: 127px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
            <div data-layer="Sys" class="Sys" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Sys</div>
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="width: 127px; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Value" class="Value" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Value</div>
              </div>
            </div>
          </div>
          <div data-layer="Frame 941" class="Frame941" style="width: 127px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
            <div data-layer="Dias" class="Dias" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Dias</div>
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="align-self: stretch; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Value" class="Value" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Value</div>
              </div>
            </div>
          </div>
          <div data-layer="Frame 937" class="Frame937" style="width: 215px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
            <div data-layer="Date of Service" class="DateOfService" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Date of Service</div>
            <div data-layer="Calendar" data-property-1="Default" class="Calendar" style="align-self: stretch; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Date of Service" class="DateOfService" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MM/DD/YY</div>
                <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
                  <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.46875 8.47559H20.4688M4.46875 8.47559V17.2758C4.46875 18.3959 4.46875 18.9556 4.68674 19.3835C4.87848 19.7598 5.18422 20.0661 5.56055 20.2578C5.98795 20.4756 6.54774 20.4756 7.66566 20.4756H17.2718C18.3898 20.4756 18.9487 20.4756 19.3761 20.2578C19.7525 20.0661 20.0592 19.7598 20.251 19.3835C20.4688 18.9561 20.4688 18.3971 20.4688 17.2792V8.47559M4.46875 8.47559V7.67578C4.46875 6.55568 4.46875 5.99521 4.68674 5.56738C4.87848 5.19106 5.18422 4.88532 5.56055 4.69357C5.98837 4.47559 6.54884 4.47559 7.66895 4.47559H8.46875M20.4688 8.47559V7.6725C20.4688 6.55458 20.4688 5.99479 20.251 5.56738C20.0592 5.19106 19.7525 4.88532 19.3761 4.69357C18.9483 4.47559 18.3891 4.47559 17.2689 4.47559H16.4688M16.4688 2.47559V4.47559M16.4688 4.47559H8.46875M8.46875 2.47559V4.47559" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-layer="Frame 938" class="Frame938" style="align-self: stretch; position: relative; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Notes" class="Notes" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Notes</div>
        <div data-layer="notes" data-property-1="Default" class="Notes" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="Text Area Field" class="TextAreaField" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
            <div data-layer="Input Field" class="InputField" style="align-self: stretch; height: 88px; padding-top: 8px; padding-bottom: 4px; padding-left: 12px; padding-right: 12px; background: white; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
              <div data-layer="Text" class="Text" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
                <div data-layer="input text" class="InputText" style="flex: 1 1 0; height: 11.18px; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">Notes</div>
              </div>
            </div>
          </div>
        </div>
        <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 126.88px; top: -10.92px; position: absolute">
          <svg width="6" height="21" viewBox="0 0 6 21" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.35351 0.555908L1.35352 8.83616L5.35352 8.83616L5.35351 0.555907M3.35352 8.91435L3.35352 20.3364" stroke="black"/>
          </svg>
        </div>
        <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 257.99px; top: -10.92px; position: absolute">
          <svg width="6" height="21" viewBox="0 0 6 21" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.45703 0.555908L1.45703 8.83616L5.45703 8.83616L5.45703 0.555907M3.45703 8.91435L3.45703 20.3364" stroke="black"/>
          </svg>
        </div>
        <div data-layer="4px" class="Px" style="left: 117.38px; top: 12.86px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">4px</div>
        <div data-layer="4px" class="Px" style="left: 248.49px; top: 12.86px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">4px</div>
      </div>
    </div>
  </div>
  <div data-layer="Rectangle 337" class="Rectangle337" style="width: 553.99px; height: 407.50px; left: 239px; top: 2136.18px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Notes" class="Notes" style="width: 517px; padding: 20px; left: 257.49px; top: 2157.43px; position: absolute; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
    <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
      <div data-layer="Frame 929" class="Frame929" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
          <div data-layer="label" class="Label" style="color: #17181A; font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Results</div>
          <div data-layer="right corner" class="RightCorner" style="flex: 1 1 0; align-self: stretch"></div>
        </div>
        <div data-layer="menu" class="Menu" style="align-self: stretch; height: 41px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="second menu" class="SecondMenu" style="align-self: stretch; background: var(--white, white); border-bottom: 1px var(--gray-2, #D9E1E7) solid; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="bradcrumb" class="Bradcrumb" style="height: 40px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
              <div data-layer="box" class="Box" style="flex: 1 1 0; justify-content: flex-start; align-items: center; display: inline-flex">
                <div data-layer="menu" class="Menu" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 16px; display: flex">
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; border-bottom: 1px var(--primary-blue, #3870B8) solid; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--primary-blue, #3870B8); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Inclusions</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Exclusions</div>
                      </div>
                    </div>
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">None found</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div data-layer="Frame 916" class="Frame916" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: flex">
      <div data-layer="Frame 942" class="Frame942" style="justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
        <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
          <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="0.994141" y="1.13086" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
          </svg>
        </div>
        <div data-layer="Teleheath" class="Teleheath" style="color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Teleheath</div>
      </div>
      <div data-layer="Frame 936" class="Frame936" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Frame 939" class="Frame939" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
          <div data-layer="Frame 940" class="Frame940" style="width: 127px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
            <div data-layer="Sys" class="Sys" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Sys</div>
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="width: 127px; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Value" class="Value" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Value</div>
              </div>
            </div>
          </div>
          <div data-layer="Frame 941" class="Frame941" style="width: 127px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
            <div data-layer="Dias" class="Dias" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Dias</div>
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="align-self: stretch; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Value" class="Value" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Value</div>
              </div>
            </div>
          </div>
          <div data-layer="Frame 937" class="Frame937" style="width: 215px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
            <div data-layer="Date of Service" class="DateOfService" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Date of Service</div>
            <div data-layer="Calendar" data-property-1="Default" class="Calendar" style="align-self: stretch; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Date of Service" class="DateOfService" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MM/DD/YY</div>
                <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
                  <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.49414 8.63086H20.4941M4.49414 8.63086V17.4311C4.49414 18.5512 4.49414 19.1109 4.71213 19.5387C4.90387 19.9151 5.20961 20.2213 5.58594 20.4131C6.01334 20.6309 6.57313 20.6309 7.69105 20.6309H17.2972C18.4151 20.6309 18.9741 20.6309 19.4015 20.4131C19.7779 20.2213 20.0846 19.9151 20.2764 19.5387C20.4941 19.1113 20.4941 18.5524 20.4941 17.4344V8.63086M4.49414 8.63086V7.83105C4.49414 6.71095 4.49414 6.15048 4.71213 5.72266C4.90387 5.34633 5.20961 5.04059 5.58594 4.84885C6.01376 4.63086 6.57423 4.63086 7.69434 4.63086H8.49414M20.4941 8.63086V7.82777C20.4941 6.70985 20.4941 6.15006 20.2764 5.72266C20.0846 5.34633 19.7779 5.04059 19.4015 4.84885C18.9737 4.63086 18.4144 4.63086 17.2943 4.63086H16.4941M16.4941 2.63086V4.63086M16.4941 4.63086H8.49414M8.49414 2.63086V4.63086" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-layer="Frame 938" class="Frame938" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Notes" class="Notes" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Notes</div>
        <div data-layer="notes" data-property-1="Default" class="Notes" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="Text Area Field" class="TextAreaField" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
            <div data-layer="Input Field" class="InputField" style="align-self: stretch; height: 88px; padding-top: 8px; padding-bottom: 4px; padding-left: 12px; padding-right: 12px; background: white; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
              <div data-layer="Text" class="Text" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
                <div data-layer="input text" class="InputText" style="flex: 1 1 0; height: 11.18px; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">Notes</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Rectangle 315" class="Rectangle315" style="width: 557.69px; height: 1171.66px; left: 235.07px; top: 325.73px; position: absolute; background: #F2F2F2"></div>
  <div data-layer="Results Container V2" class="ResultsContainerV2" style="left: 47px; top: 62.80px; position: absolute; color: black; font-size: 36px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Results Container V2</div>
  <div data-layer="Behavior: Scrolls with page. Interactive in both default and entered state. Allows copy/paste." class="BehaviorScrollsWithPageInteractiveInBothDefaultAndEnteredStateAllowsCopyPaste" style="width: 1039.94px; left: 47px; top: 212.20px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Behavior: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Scrolls with page. Interactive in both default and entered state. Allows copy/paste.</span></div>
  <div data-layer="Behavior: Scrolls with page. Interactive in both default and entered state. Allows copy/paste." class="BehaviorScrollsWithPageInteractiveInBothDefaultAndEnteredStateAllowsCopyPaste" style="width: 1039.94px; left: 47px; top: 212.20px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Behavior: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Scrolls with page. Interactive in both default and entered state. Allows copy/paste.</span></div>
  <div data-layer="Entry: Has a dropdown (Reasoning), date entry (Date of Service), and text entry (Notes)" class="EntryHasADropdownReasoningDateEntryDateOfServiceAndTextEntryNotes" style="width: 1039.94px; left: 47px; top: 255.73px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Entry: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Has a dropdown (Reasoning), date entry (Date of Service), and text entry (Notes)</span></div>
  <div data-layer="Usage: Enter findings after finding them in the chart and validating them. There are three versions: Inclusions, Exclusions, and None found." class="UsageEnterFindingsAfterFindingThemInTheChartAndValidatingThemThereAreThreeVersionsInclusionsExclusionsAndNoneFound" style="width: 974.97px; left: 47.83px; top: 137px; position: absolute"><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 600; word-wrap: break-word">Usage: </span><span style="color: black; font-size: 18px; font-family: Urbane; font-weight: 300; word-wrap: break-word">Enter findings after finding them in the chart and validating them. There are three versions: Inclusions, Exclusions, and None found.</span></div>
  <div data-layer="Default: inactive, nothing entered" class="DefaultInactiveNothingEntered" style="left: 817.55px; top: 483px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Default: inactive, nothing entered</div>
  <div data-layer="Text-entry: Sys, Dias, and DoS auto-populate when a measure is included and allow for copy/paste" class="TextEntrySysDiasAndDosAutoPopulateWhenAMeasureIsIncludedAndAllowForCopyPaste" style="width: 208.90px; left: 827.02px; top: 545.59px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Text-entry: Sys, Dias, and DoS auto-populate when a measure is included and allow for copy/paste</div>
  <div data-layer="Inclusions: Reasoning, Date of Service, and Notes shown" class="InclusionsReasoningDateOfServiceAndNotesShown" style="width: 226.06px; left: 817.55px; top: 432.50px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Inclusions: Reasoning, Date of Service, and Notes shown</div>
  <div data-layer="Categories are in tabs" class="CategoriesAreInTabs" style="width: 140.11px; left: 68.34px; top: 432.50px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Categories are in tabs</div>
  <div data-layer="Exclusions: Reasoning, Date of Service, and Notes shown" class="ExclusionsReasoningDateOfServiceAndNotesShown" style="width: 226.06px; left: 822.73px; top: 807.50px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Exclusions: Reasoning, Date of Service, and Notes shown</div>
  <div data-layer="Component: Dropdown-exclusion" class="ComponentDropdownExclusion" style="width: 226.06px; left: 822.73px; top: 894.78px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Component: Dropdown-exclusion</div>
  <div data-layer="All fields have labels above Font: Urbane light font-size: 10px font-color: gray3 line-spacing: 20px" class="AllFieldsHaveLabelsAboveFontUrbaneLightFontSize10pxFontColorGray3LineSpacing20px" style="width: 168.44px; left: 36.67px; top: 854px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">All fields have labels above<br/>Font: Urbane light<br/>font-size: 10px<br/>font-color: gray3<br/>line-spacing: 20px</div>
  <div data-layer="Component: Dropdown-none" class="ComponentDropdownNone" style="width: 226.06px; left: 822.73px; top: 1313.89px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Component: Dropdown-none</div>
  <div data-layer="None found: Reasoning, Notes shown" class="NoneFoundReasoningNotesShown" style="width: 226.06px; left: 824.38px; top: 1237.50px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">None found: Reasoning, Notes shown</div>
  <div data-layer="Second menu: stroke bottom Size: 1px Color: gray-1" class="SecondMenuStrokeBottomSize1pxColorGray1" style="width: 226.06px; left: 808.63px; top: 2233.18px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Second menu: stroke bottom<br/>Size: 1px<br/>Color: gray-1</div>
  <div data-layer="Menu item (active) Font: H2 Color: primary-blue Stroke: bottom, 1px, primary-blue" class="MenuItemActiveFontH2ColorPrimaryBlueStrokeBottom1pxPrimaryBlue" style="width: 123.03px; left: 88.82px; top: 2213.18px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Menu item (active)<br/>Font: H2<br/>Color: primary-blue<br/>Stroke: bottom, 1px, primary-blue</div>
  <div data-layer="Menu item (inactive) Font: H2 Color: gray-3 Stroke: none" class="MenuItemInactiveFontH2ColorGray3StrokeNone" style="width: 130.50px; left: 388.49px; top: 2051.62px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Menu item (inactive)<br/>Font: H2<br/>Color: gray-3<br/>Stroke: none</div>
  <div data-svg-wrapper data-layer="Vector 40" class="Vector40" style="left: 710.90px; top: 483px; position: absolute">
    <svg width="36" height="2" viewBox="0 0 36 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.898438 1.20337H35.0811" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 42" class="Vector42" style="left: 710.90px; top: 432.50px; position: absolute">
    <svg width="36" height="2" viewBox="0 0 36 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.898438 0.703369H35.0811" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 45" class="Vector45" style="left: 213.60px; top: 432.50px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.597656 0.703369H34.7803" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 43" class="Vector43" style="left: 781.93px; top: 807.50px; position: absolute">
    <svg width="36" height="2" viewBox="0 0 36 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.931641 0.703369H35.1143" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 53" class="Vector53" style="left: 781.93px; top: 894px; position: absolute">
    <svg width="36" height="2" viewBox="0 0 36 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.931641 1.20337H35.1143" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 55" class="Vector55" style="left: 217.98px; top: 853px; position: absolute">
    <svg width="36" height="2" viewBox="0 0 36 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.980469 1.20337H35.1631" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 54" class="Vector54" style="left: 781.93px; top: 1313.11px; position: absolute">
    <svg width="36" height="2" viewBox="0 0 36 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.931641 1.31519H35.1143" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 52" class="Vector52" style="left: 781.93px; top: 551.59px; position: absolute">
    <svg width="36" height="2" viewBox="0 0 36 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.931641 0.792236H35.1143" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 44" class="Vector44" style="left: 779.80px; top: 1237.50px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.798828 0.703369H34.9814" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 49" class="Vector49" style="left: 767.83px; top: 2264.18px; position: absolute">
    <svg width="35" height="2" viewBox="0 0 35 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.826172 1.38086H35.0088" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 50" class="Vector50" style="left: 220.31px; top: 2246.68px; position: absolute">
    <svg width="47" height="2" viewBox="0 0 47 2" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.3125 0.880859L46.9668 0.880855" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector 51" class="Vector51" style="left: 435.99px; top: 2124.39px; position: absolute">
    <svg width="2" height="97" viewBox="0 0 2 97" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.994141 96.9414L0.994136 0.591798" stroke="black"/>
    </svg>
  </div>
  <div data-layer="Spacing:" class="Spacing" style="width: 83px; height: 15.69px; left: 68.34px; top: 1504.66px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Spacing:</div>
  <div data-layer="Styling:" class="Styling" style="width: 83px; height: 15.69px; left: 68.34px; top: 1995.87px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 18px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Styling:</div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 258.41px; top: 1906.57px; position: absolute">
    <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1.4082 0.770263L1.4082 9.05052L21.4082 9.05052L21.4082 0.770262M11.4082 9.12871L11.4082 20.5508" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 236.53px; top: 1886.57px; position: absolute">
    <svg width="21" height="22" viewBox="0 0 21 22" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.3098 0.770508L12.0296 0.770508L12.0296 20.7705H20.3098M11.9514 10.7705L0.529297 10.7705" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 234.90px; top: 1780.27px; position: absolute">
    <svg width="42" height="14" viewBox="0 0 42 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M41.9141 1.47559L24.7471 1.47559L24.7471 13.4756L41.9141 13.4756M24.585 7.47559L0.904297 7.47559" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 234.90px; top: 1727.57px; position: absolute">
    <svg width="42" height="6" viewBox="0 0 42 6" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M41.9141 0.770508L24.7471 0.770508V4.77051L41.9141 4.77051M24.585 2.77051L0.904297 2.77051" stroke="black"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Vector" class="Vector" style="left: 234.90px; top: 1811.56px; position: absolute">
    <svg width="42" height="6" viewBox="0 0 42 6" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M41.9141 0.767578L24.7471 0.767578V4.76758L41.9141 4.76758M24.585 2.76758L0.904297 2.76758" stroke="black"/>
    </svg>
  </div>
  <div data-layer="20px" class="Px" style="left: 253.94px; top: 1934.21px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">20px</div>
  <div data-layer="20px" class="Px" style="left: 202.90px; top: 1896.57px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">20px</div>
  <div data-layer="12px" class="Px" style="left: 204.54px; top: 1786.27px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">12px</div>
  <div data-layer="4px" class="Px" style="left: 204.54px; top: 1730.27px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">4px</div>
  <div data-layer="4px" class="Px" style="left: 204.54px; top: 1814.27px; position: absolute; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">4px</div>
  <div data-layer="Rectangle 293" class="Rectangle293" style="width: 20px; height: 517px; left: 256.47px; top: 1579.67px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 326" class="Rectangle326" style="width: 20px; height: 517px; left: 256.47px; top: 1924.27px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 295" class="Rectangle295" style="width: 20px; height: 358.71px; left: 276.47px; top: 1924.27px; position: absolute; transform: rotate(180deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 327" class="Rectangle327" style="width: 20px; height: 364.60px; left: 773.47px; top: 1924.27px; position: absolute; transform: rotate(180deg); transform-origin: top left; background: rgba(0, 125, 0, 0.25)"></div>
  <div data-layer="Rectangle 297" class="Rectangle297" style="width: 12px; height: 477.23px; left: 276.24px; top: 1629.57px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 334" class="Rectangle334" style="width: 4px; height: 477.23px; left: 276.24px; top: 1732.27px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-svg-wrapper data-layer="Rectangle 341" class="Rectangle341" style="left: 403.47px; top: 1732.23px; position: absolute">
    <svg width="5" height="49" viewBox="0 0 5 49" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.46875" y="0.433105" width="4" height="48.0425" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 342" class="Rectangle342" style="left: 534.47px; top: 1732.23px; position: absolute">
    <svg width="5" height="49" viewBox="0 0 5 49" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="4.46875" y="48.4756" width="4" height="48.0425" transform="rotate(-180 4.46875 48.4756)" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-layer="Rectangle 340" class="Rectangle340" style="width: 4px; height: 477.23px; left: 276.24px; top: 1816.27px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 339" class="Rectangle339" style="width: 12px; height: 477.23px; left: 276.24px; top: 1707.42px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 338" class="Rectangle338" style="width: 12px; height: 477.23px; left: 276.24px; top: 1676.27px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-layer="Rectangle 336" class="Rectangle336" style="width: 12px; height: 477.23px; left: 276.24px; top: 1792.27px; position: absolute; transform: rotate(-90deg); transform-origin: top left; background: rgba(197.12, 69.62, 0, 0.30)"></div>
  <div data-svg-wrapper data-layer="Rectangle 300" class="Rectangle300" style="left: 276.24px; top: 1617.57px; position: absolute">
    <svg width="9" height="47" viewBox="0 0 9 47" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.236328" y="0.770508" width="8" height="45.7051" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 328" class="Rectangle328" style="left: 359.94px; top: 1617.57px; position: absolute">
    <svg width="9" height="47" viewBox="0 0 9 47" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.9375" y="0.770508" width="8" height="45.7051" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 331" class="Rectangle331" style="left: 470.94px; top: 1617.57px; position: absolute">
    <svg width="9" height="47" viewBox="0 0 9 47" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.9375" y="0.770508" width="8" height="45.7051" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 330" class="Rectangle330" style="left: 367.94px; top: 1617.57px; position: absolute">
    <svg width="17" height="47" viewBox="0 0 17 47" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.9375" y="0.770508" width="16" height="45.7051" fill="#12AFF0" fill-opacity="0.4"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 332" class="Rectangle332" style="left: 478.94px; top: 1617.57px; position: absolute">
    <svg width="17" height="47" viewBox="0 0 17 47" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.9375" y="0.770508" width="16" height="45.7051" fill="#12AFF0" fill-opacity="0.4"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 329" class="Rectangle329" style="left: 383.94px; top: 1617.57px; position: absolute">
    <svg width="9" height="47" viewBox="0 0 9 47" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.9375" y="0.770508" width="8" height="45.7051" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-svg-wrapper data-layer="Rectangle 333" class="Rectangle333" style="left: 494.94px; top: 1617.57px; position: absolute">
    <svg width="9" height="47" viewBox="0 0 9 47" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.9375" y="0.770508" width="8" height="45.7051" fill="#C54600" fill-opacity="0.3"/>
    </svg>
  </div>
  <div data-layer="corner-radius: 8 border-style: solid  border-color: gray-1" class="CornerRadius8BorderStyleSolidBorderColorGray1" style="left: 802.01px; top: 2136.18px; position: absolute; color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">corner-radius: 8<br/>border-style: solid  border-color: gray-1</div>
  <div data-layer="Notes" class="Notes" style="width: 517px; padding: 20px; left: 254.03px; top: 354.59px; position: absolute; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
    <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
      <div data-layer="Frame 929" class="Frame929" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
          <div data-layer="label" class="Label" style="color: #17181A; font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Results</div>
          <div data-layer="right corner" class="RightCorner" style="flex: 1 1 0; align-self: stretch"></div>
        </div>
        <div data-layer="menu" class="Menu" style="align-self: stretch; height: 41px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="second menu" class="SecondMenu" style="align-self: stretch; background: var(--white, white); border-bottom: 1px var(--gray-2, #D9E1E7) solid; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="bradcrumb" class="Bradcrumb" style="height: 40px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
              <div data-layer="box" class="Box" style="flex: 1 1 0; justify-content: flex-start; align-items: center; display: inline-flex">
                <div data-layer="menu" class="Menu" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 16px; display: flex">
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; border-bottom: 1px var(--primary-blue, #3870B8) solid; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--primary-blue, #3870B8); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Inclusions</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Exclusions</div>
                      </div>
                    </div>
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">None found</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div data-layer="Frame 916" class="Frame916" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: flex">
      <div data-layer="Frame 942" class="Frame942" style="justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
        <div data-svg-wrapper data-layer="check" data-property-1="default" class="Check" style="position: relative">
          <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="0.525391" y="1.29224" width="15" height="15" rx="4.5" fill="white" stroke="var(--gray-2, #D9E1E7)"/>
          </svg>
        </div>
        <div data-layer="Teleheath" class="Teleheath" style="color: black; font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Teleheath</div>
      </div>
      <div data-layer="Frame 936" class="Frame936" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Frame 939" class="Frame939" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
          <div data-layer="Frame 940" class="Frame940" style="width: 127px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
            <div data-layer="Sys" class="Sys" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Sys</div>
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="width: 127px; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Value" class="Value" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Value</div>
              </div>
            </div>
          </div>
          <div data-layer="Frame 941" class="Frame941" style="width: 127px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
            <div data-layer="Dias" class="Dias" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Dias</div>
            <div data-layer="Dropdown_inclusion" class="DropdownInclusion" style="align-self: stretch; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Value" class="Value" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Value</div>
              </div>
            </div>
          </div>
          <div data-layer="Frame 937" class="Frame937" style="width: 215px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
            <div data-layer="Date of Service" class="DateOfService" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Date of Service</div>
            <div data-layer="Calendar" data-property-1="Default" class="Calendar" style="align-self: stretch; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
              <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
                <div data-layer="Date of Service" class="DateOfService" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MM/DD/YY</div>
                <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
                  <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.02539 8.79199H20.0254M4.02539 8.79199V17.5922C4.02539 18.7123 4.02539 19.2721 4.24338 19.6999C4.43512 20.0762 4.74086 20.3825 5.11719 20.5742C5.54459 20.792 6.10438 20.792 7.2223 20.792H16.8285C17.9464 20.792 18.5054 20.792 18.9328 20.5742C19.3091 20.3825 19.6159 20.0762 19.8076 19.6999C20.0254 19.2725 20.0254 18.7135 20.0254 17.5956V8.79199M4.02539 8.79199V7.99219C4.02539 6.87208 4.02539 6.31161 4.24338 5.88379C4.43512 5.50746 4.74086 5.20173 5.11719 5.00998C5.54501 4.79199 6.10548 4.79199 7.22559 4.79199H8.02539M20.0254 8.79199V7.9889C20.0254 6.87099 20.0254 6.31119 19.8076 5.88379C19.6159 5.50746 19.3091 5.20173 18.9328 5.00998C18.505 4.79199 17.9457 4.79199 16.8256 4.79199H16.0254M16.0254 2.79199V4.79199M16.0254 4.79199H8.02539M8.02539 2.79199V4.79199" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-layer="Frame 938" class="Frame938" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Notes" class="Notes" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Notes</div>
        <div data-layer="notes" data-property-1="Default" class="Notes" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="Text Area Field" class="TextAreaField" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
            <div data-layer="Input Field" class="InputField" style="align-self: stretch; height: 88px; padding-top: 8px; padding-bottom: 4px; padding-left: 12px; padding-right: 12px; background: white; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
              <div data-layer="Text" class="Text" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
                <div data-layer="input text" class="InputText" style="flex: 1 1 0; height: 11.18px; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">Notes</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Notes" class="Notes" style="width: 517px; padding: 20px; left: 255px; top: 726px; position: absolute; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
    <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
      <div data-layer="Frame 929" class="Frame929" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
          <div data-layer="label" class="Label" style="color: var(--text-black, #17181A); font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Findings</div>
          <div data-layer="right corner" class="RightCorner" style="flex: 1 1 0; align-self: stretch"></div>
        </div>
        <div data-layer="menu" class="Menu" style="align-self: stretch; height: 41px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="second menu" class="SecondMenu" style="align-self: stretch; background: var(--white, white); border-bottom: 1px var(--gray-2, #D9E1E7) solid; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="bradcrumb" class="Bradcrumb" style="height: 40px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
              <div data-layer="box" class="Box" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: center; display: inline-flex">
                <div data-layer="menu" class="Menu" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 16px; display: flex">
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Inclusions</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; border-bottom: 1px var(--primary-blue, #3870B8) solid; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--primary-blue, #3870B8); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Exclusions</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">None found</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div data-layer="Frame 916" class="Frame916" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: flex">
      <div data-layer="Frame 939" class="Frame939" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Reasoning" class="Reasoning" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Reasoning</div>
        <div data-layer="Dropdown-exclusion" data-property-1="Default" class="DropdownExclusion" style="align-self: stretch; height: 44px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
          <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Select" class="Select" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Select</div>
            <div data-svg-wrapper data-layer="icon_right" class="IconRight" style="position: relative">
              <svg width="8" height="15" viewBox="0 0 8 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M0.939453 12.9797L6.69945 7.20337L0.939453 1.42701" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div data-layer="Frame 940" class="Frame940" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Date of service" class="DateOfService" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Date of service</div>
        <div data-layer="Calendar" data-property-1="Default" class="Calendar" style="align-self: stretch; height: 48px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
          <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Date of Service" class="DateOfService" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">MM/DD/YY</div>
            <div data-svg-wrapper data-layer="icon_interface" class="IconInterface" style="position: relative">
              <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4 8.20312H20M4 8.20312V17.0033C4 18.1234 4 18.6832 4.21799 19.111C4.40973 19.4873 4.71547 19.7936 5.0918 19.9854C5.5192 20.2031 6.07899 20.2031 7.19691 20.2031H16.8031C17.921 20.2031 18.48 20.2031 18.9074 19.9854C19.2837 19.7936 19.5905 19.4873 19.7822 19.111C20 18.6836 20 18.1246 20 17.0067V8.20312M4 8.20312V7.40332C4 6.28322 4 5.72275 4.21799 5.29492C4.40973 4.9186 4.71547 4.61286 5.0918 4.42111C5.51962 4.20312 6.08009 4.20312 7.2002 4.20312H8M20 8.20312V7.40003C20 6.28212 20 5.72233 19.7822 5.29492C19.5905 4.9186 19.2837 4.61286 18.9074 4.42111C18.4796 4.20312 17.9203 4.20312 16.8002 4.20312H16M16 2.20312V4.20312M16 4.20312H8M8 2.20312V4.20312" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div data-layer="Frame 941" class="Frame941" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Notes" class="Notes" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Notes</div>
        <div data-layer="notes" data-property-1="exclusions-default" class="Notes" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="Text Area Field" class="TextAreaField" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
            <div data-layer="Input Field" class="InputField" style="align-self: stretch; height: 88px; padding-top: 8px; padding-bottom: 4px; padding-left: 12px; padding-right: 12px; background: white; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
              <div data-layer="Text" class="Text" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
                <div data-layer="input text" class="InputText" style="flex: 1 1 0; height: 11.18px; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">Notes</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Notes" class="Notes" style="width: 517px; padding: 20px; left: 255px; top: 1151px; position: absolute; background: white; border-radius: 8px; outline: 1px #F1F5F7 solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
    <div data-layer="head" class="Head" style="align-self: stretch; padding-bottom: 12px; justify-content: flex-start; align-items: center; gap: 20px; display: inline-flex">
      <div data-layer="Frame 929" class="Frame929" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: inline-flex">
        <div data-layer="stack" class="Stack" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
          <div data-layer="label" class="Label" style="color: var(--text-black, #17181A); font-size: 20px; font-family: Urbane; font-weight: 600; line-height: 32px; word-wrap: break-word">Findings</div>
          <div data-layer="right corner" class="RightCorner" style="flex: 1 1 0; align-self: stretch"></div>
        </div>
        <div data-layer="menu" class="Menu" style="align-self: stretch; height: 41px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="second menu" class="SecondMenu" style="align-self: stretch; background: var(--white, white); border-bottom: 1px var(--gray-2, #D9E1E7) solid; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="bradcrumb" class="Bradcrumb" style="height: 40px; flex-direction: column; justify-content: center; align-items: flex-start; display: inline-flex">
              <div data-layer="box" class="Box" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: center; display: inline-flex">
                <div data-layer="menu" class="Menu" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 16px; display: flex">
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Inclusions</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--gray-3, #547996); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">Exclusions</div>
                      </div>
                    </div>
                  </div>
                  <div data-layer="menu item" class="MenuItem" style="align-self: stretch; border-bottom: 1px var(--primary-blue, #3870B8) solid; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
                    <div data-layer="Menu Item" class="MenuItem" style="align-self: stretch; padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                      <div data-layer="Frame 831" class="Frame831" style="border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: flex">
                        <div data-layer="Label" class="Label" style="text-box-trim: trim-both; text-box-edge: cap alphabetic; color: var(--primary-blue, #3870B8); font-size: 14px; font-family: Urbane; font-weight: 600; line-height: 20px; word-wrap: break-word">None found</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div data-layer="Frame 916" class="Frame916" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 12px; display: flex">
      <div data-layer="Frame 942" class="Frame942" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Reasoning" class="Reasoning" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Reasoning</div>
        <div data-layer="Dropdown_none" data-property-1="default" class="DropdownNone" style="align-self: stretch; height: 44px; padding: 4px; background: white; overflow: hidden; border-radius: 10px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: center; align-items: center; display: flex">
          <div data-layer="row" class="Row" style="align-self: stretch; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; background: white; border-radius: 6px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex">
            <div data-layer="Select" class="Select" style="flex: 1 1 0; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Select</div>
            <div data-svg-wrapper data-layer="icon_right" class="IconRight" style="position: relative">
              <svg width="8" height="15" viewBox="0 0 8 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M0.939453 12.9797L6.69945 7.20337L0.939453 1.42701" stroke="var(--gray-3, #547996)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div data-layer="Frame 943" class="Frame943" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
        <div data-layer="Notes" class="Notes" style="color: var(--gray-3, #547996); font-size: 10px; font-family: Urbane; font-weight: 300; line-height: 20px; word-wrap: break-word">Notes</div>
        <div data-layer="notes" data-property-1="Default" class="Notes" style="align-self: stretch; justify-content: flex-start; align-items: flex-start; display: inline-flex">
          <div data-layer="Text Area Field" class="TextAreaField" style="flex: 1 1 0; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
            <div data-layer="Input Field" class="InputField" style="align-self: stretch; height: 88px; padding-top: 8px; padding-bottom: 4px; padding-left: 12px; padding-right: 12px; background: white; border-radius: 8px; outline: 1px var(--gray-2, #D9E1E7) solid; outline-offset: -1px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
              <div data-layer="Text" class="Text" style="align-self: stretch; flex: 1 1 0; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
                <div data-layer="input text" class="InputText" style="flex: 1 1 0; height: 11.18px; text-box-trim: trim-both; text-box-edge: cap alphabetic; justify-content: flex-end; display: flex; flex-direction: column; color: var(--gray-3, #547996); font-size: 12px; font-family: Urbane; font-weight: 300; line-height: 16px; word-wrap: break-word">Notes</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>