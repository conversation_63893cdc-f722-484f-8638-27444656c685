@use 'variables' as variables;
@use 'mixins' as mix;

.search-filter {
  width: 320px;
}

.search-input-container {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid var(--light-content, #D9E1E7);
  background-color: var(--white, white);
  gap: 8px;
}

.search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 11px;
  font-weight: 300;
  line-height: 20px;
  color: #384455;
  width: 100%;
  
  &::placeholder {
    color: #384455;
  }
}