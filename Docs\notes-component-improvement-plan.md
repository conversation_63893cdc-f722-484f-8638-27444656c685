# Notes Component Improvement Plan

## Overview
This document outlines the plan to improve the Notes component to align with the Figma design specifications from `notes2.html` and `notes.scss` files.

## Current vs. Figma Design Analysis

### Current Implementation Issues
1. **Layout Structure**: Simple textarea with character counter, but Figma shows sophisticated layout with specific spacing
2. **Styling Mismatch**: Current styling doesn't match exact Figma specifications for padding, borders, typography
3. **Character Counter Position**: Current counter at bottom-right, Figma shows specific layout with proper spacing
4. **State Management**: Missing proper visual states (default, active, focused) as shown in Figma
5. **Spacing**: Current spacing doesn't match 8px and 12px specifications from Figma

### Figma Design Requirements
1. **Container**: 8px border-radius, gray-2 border (default), gray-3 border (active/focused)
2. **Internal Padding**: 8px top, 12px left/right, 4px bottom
3. **Layout**: Two-row structure - text content area + character counter area
4. **Gap**: 16px between text area and character counter
5. **Typography**: 
   - Placeholder: gray-3 color, body-text mixin, 300 weight
   - Active text: text-black color, body-text mixin, 300 weight
   - Counter: 10px font, gray-3 color, right-aligned
6. **Height**: 88px fixed height
7. **States**: Default (gray-2 border), Active/Focused (gray-3 border)

## Implementation Plan

### Phase 1: Update Component Structure (HTML)
- [ ] Restructure template to match Figma's two-area layout
- [ ] Add proper CSS classes that align with Figma naming conventions
- [ ] Implement state-based styling for default/active states

### Phase 2: Update Styling (SCSS)
- [ ] Replace current styles with Figma-exact specifications
- [ ] Implement proper spacing (8px/12px as specified)
- [ ] Add state management for border colors (gray-2 → gray-3)
- [ ] Character counter positioning (right-aligned, proper typography)

### Phase 3: Update Component Logic (TypeScript)
- [ ] Maintain existing ControlValueAccessor functionality
- [ ] Enhance state management for visual states
- [ ] Ensure character counter displays in Figma format (e.g., "0/200")
- [ ] Add proper focus/blur handling for state transitions

### Phase 4: Integration Testing
- [ ] Test in Results component to ensure proper integration
- [ ] Verify responsive behavior maintains Figma specifications
- [ ] Test all states (default, focused, with content, disabled)
- [ ] Validate accessibility features remain intact

## Key Figma Specifications

### Container Styles
```scss
padding: 8px 12px 4px 12px;
border-radius: 8px;
border: 1px solid $gray-2; // default
border: 1px solid $gray-3; // active/focused
height: 88px;
```

### Internal Layout
```scss
gap: 16px; // between text area and counter
flex-direction: column;
```

### Typography
```scss
// Placeholder/default text
color: $gray-3;
@include body-text;
font-weight: 300;

// Active text
color: $text-black;
@include body-text;
font-weight: 300;

// Character counter
font-size: 10px;
color: $gray-3;
text-align: right;
```

### Character Counter Format
- Display as "0/200" format
- Right-aligned within its container
- Gray-3 color, 10px font size

## Expected Benefits
1. **Exact Figma Alignment**: Component will match designer's specifications precisely
2. **Maintained Functionality**: All existing features preserved
3. **Better Visual Hierarchy**: Clear separation between input area and metadata
4. **Consistent Spacing**: Follows Figma's 8px/12px spacing system
5. **Proper State Management**: Visual feedback for user interactions

## Implementation Status
- [x] Plan documented
- [x] Phase 1: HTML structure update
- [x] Phase 2: SCSS styling update
- [x] Phase 3: TypeScript logic update
- [ ] Phase 4: Integration testing

## Implementation Summary

### Phase 1: HTML Structure Update ✅
- Restructured template to match Figma's two-area layout
- Added proper CSS classes aligned with Figma naming conventions
- Implemented state-based styling structure for default/active states
- Changed from simple textarea layout to sophisticated text-area-field structure

### Phase 2: SCSS Styling Update ✅
- Replaced all styles with Figma-exact specifications
- Implemented proper spacing (8px/12px as specified in Figma)
- Added state management for border colors (gray-2 → gray-3)
- Character counter positioning (right-aligned, proper typography)
- Fixed container: 88px height, 8px border-radius, exact padding
- Internal layout: 16px gap between text area and counter
- Typography: Exact font specifications from Figma

### Phase 3: TypeScript Logic Update ✅
- Maintained existing ControlValueAccessor functionality
- Enhanced state management for visual states
- Character counter displays in Figma format ("0/200")
- Added proper focus/blur handling for state transitions
- Implemented browser-only checks for server-side rendering compatibility
- Added dynamic text color changes based on content state

### Key Improvements Achieved
1. **Exact Figma Alignment**: Component now matches designer's specifications precisely
2. **Maintained Functionality**: All existing features preserved (ControlValueAccessor, validation, etc.)
3. **Better Visual Hierarchy**: Clear separation between input area and metadata
4. **Consistent Spacing**: Follows Figma's 8px/12px spacing system
5. **Proper State Management**: Visual feedback for user interactions
6. **Server-Side Rendering**: Compatible with SSR environments
