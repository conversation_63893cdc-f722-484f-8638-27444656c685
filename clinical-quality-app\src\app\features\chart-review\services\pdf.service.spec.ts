import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { PdfService, PdfSearchResult, TextItem } from './pdf.service';

describe('PdfService', () => {
  let service: PdfService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [PdfService]
    });
    service = TestBed.inject(PdfService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Highlighting', () => {
    it('should add and retrieve highlights', (done) => {
      const position = { left: 10, top: 10, right: 50, bottom: 30 };
      const text = 'Highlighted text';
      
      // Add a highlight
      const highlightId = service.addHighlight(1, position, text);
      
      // Get highlights for the page
      service.getPageHighlights(1).subscribe(highlights => {
        expect(highlights).toBeTruthy();
        expect(highlights.length).toBe(1);
        expect(highlights[0].id).toBe(highlightId);
        expect(highlights[0].text).toBe(text);
        expect(highlights[0].position).toEqual(position);
        done();
      });
    });
    
    it('should remove highlights', (done) => {
      const position = { left: 10, top: 10, right: 50, bottom: 30 };
      
      // Add two highlights
      const id1 = service.addHighlight(1, position, 'Text 1');
      const id2 = service.addHighlight(1, position, 'Text 2');
      
      // Remove one highlight
      service.removeHighlight(id1);
      
      // Get highlights for the page
      service.getPageHighlights(1).subscribe(highlights => {
        expect(highlights.length).toBe(1);
        expect(highlights[0].id).toBe(id2);
        done();
      });
    });
    
    it('should clear all highlights', (done) => {
      const position = { left: 10, top: 10, right: 50, bottom: 30 };
      
      // Add some highlights
      service.addHighlight(1, position, 'Text 1');
      service.addHighlight(2, position, 'Text 2');
      
      // Clear all highlights
      service.clearHighlights();
      
      // Get highlights for a page
      service.getPageHighlights(1).subscribe(highlights => {
        expect(highlights.length).toBe(0);
        done();
      });
    });
  });
  
  describe('Cache Management', () => {
    it('should clear the text content cache', () => {
      // Add some mock data to the cache
      (service as any).textContentCache.set(1, { items: [], styles: {} });
      
      // Clear the cache
      service.clearCache();
      
      // Verify the cache was cleared
      expect((service as any).textContentCache.size).toBe(0);
    });
  });
  
  // Note: More complex tests for PDF loading, text extraction, and searching
  // would require extensive mocking of the PDF.js library, which is beyond
  // the scope of this basic test suite. In a real-world scenario, these would
  // be implemented with proper mocks or integration tests.
});
