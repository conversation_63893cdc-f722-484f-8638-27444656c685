import { Component, Input, Output, EventEmitter, forwardRef, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-notes',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './notes.component.html',
  styleUrls: ['./notes.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => NotesComponent),
      multi: true
    }
  ]
})
export class NotesComponent implements ControlValueAccessor, OnInit {
  @Input() label: string = 'Notes';
  @Input() placeholder: string = 'Notes';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() errorMessage: string = '';
  @Input() id: string = '';
  @Input() name: string = '';
  @Input() maxLength: number = 200;
  @Input() showCharacterCount: boolean = true;

  @Output() notesChange = new EventEmitter<string>();

  value: string = '';
  isFocused: boolean = false;

  // ControlValueAccessor implementation
  onChange: any = () => {};
  onTouched: any = () => {};

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  ngOnInit(): void {
    if (!this.id) {
      this.id = 'notes-' + Math.random().toString(36).substring(2, 9);
    }
  }

  writeValue(value: string): void {
    this.value = value || '';
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onInput(event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    this.value = target.value;
    this.onChange(this.value);
    this.notesChange.emit(this.value);
  }

  onFocus(): void {
    this.isFocused = true;
  }

  onBlur(): void {
    this.isFocused = false;
    this.onTouched();
  }

  get characterCount(): string {
    return `${this.value.length}/${this.maxLength}`;
  }

  get isNearLimit(): boolean {
    return this.value.length > this.maxLength * 0.8;
  }

  get isOverLimit(): boolean {
    return this.value.length > this.maxLength;
  }
}
