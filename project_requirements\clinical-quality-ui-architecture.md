# Clinical Quality UI - Angular Architecture Plan

## Phased Approach for POC/MVP

### Phase 1: MVP with Local Storage (PostgreSQL-Ready Design)

#### 1. Technology Stack (Phase 1)

- **Frontend**:
  - Angular 17
  - Angular Material
  - NgRx (lightweight implementation)
  - SCSS with Angular Material theming
  - Jasmine/Karma for testing

- **Data Storage**:
  - <PERSON><PERSON><PERSON>'s IndexedDB for structured data
  - LocalStorage for small configuration data
  - File system API for PDF storage (if needed)

#### 2. Application Architecture (Phase 1)

```mermaid
graph TD
    A[App Module] --> B[Core Module]
    A --> C[Shared Module]
    A --> D[Feature Modules]
    D --> E[Dashboard Module]
    D --> F[Chart Review Module]
    B --> G[Storage Service]
    B --> H[Error Handling]
    G --> I[Storage Interface]
    I --> J[IndexedDB Implementation]
    I -.-> K[Future PostgreSQL Implementation]
    C --> L[UI Components]
    C --> M[Pipes/Directives]
    E --> N[Dashboard Components]
    F --> O[Chart Viewer Components]
    F --> P[Validation Components]
    F --> Q[Comment/Highlight Components]
    F --> R[Advanced Search Service]
    F --> S[Annotation Service]
    F --> T[PDF Export Service]
```

#### 3. Core Features Implementation

- **Dashboard View**:
  - Display list of charts/members requiring review
  - Show status indicators (reviewed, pending, etc.)
  - Allow filtering and sorting
  - Implement basic search functionality

- **Chart Review View**:
  - PDF viewer integration for displaying medical charts
  - **Advanced Ctrl+F Search Functionality** (CRITICAL FEATURE)
    - Text search across ALL pages of the PDF, not just visible page
    - Optimized for performance with large PDFs (100+ pages)
    - Highlight all matches in the document
    - Navigate between search results across pages
    - Case-sensitive and whole word options
    - Search result count and current match indicator
  - Split-screen layout with AI findings on one side, chart on the other
  - Highlighting functionality for relevant sections
  - Comment/annotation capabilities
  - Validation controls (confirm/reject findings)
  - **Save Annotated Charts** (CRITICAL FEATURE)
    - Save user annotations and highlights locally
    - Persist comments and validation decisions
    - Export annotated charts as PDFs with embedded annotations

#### 4. PostgreSQL-Ready Data Architecture

To ensure a smooth migration path to PostgreSQL in Phase 2, we'll implement the following design patterns:

- **Repository Pattern**:
  - Create abstract repository interfaces for each data entity
  - Implement IndexedDB repositories in Phase 1
  - Design for easy swapping with API-based repositories in Phase 2
  - Example:
    ```typescript
    // Abstract repository interface
    interface ChartRepository {
      getAll(): Observable<Chart[]>;
      getById(id: string): Observable<Chart>;
      save(chart: Chart): Observable<Chart>;
      delete(id: string): Observable<void>;
    }
    
    // Phase 1: IndexedDB implementation
    class IndexedDBChartRepository implements ChartRepository {
      // Implementation using IndexedDB
    }
    
    // Phase 2: API implementation (future)
    class ApiChartRepository implements ChartRepository {
      // Implementation using HTTP calls to backend API
    }
    ```

- **Data Models**:
  - Design entity models to match future PostgreSQL schema
  - Include all fields needed for database persistence
  - Use proper types and validation rules
  - Example:
    ```typescript
    interface Annotation {
      id: string;              // UUID for database compatibility
      chartId: string;         // Foreign key reference
      userId: string;          // Foreign key reference (can be hardcoded for POC)
      pageNumber: number;
      annotationType: string;
      coordinates: {           // Stored as JSONB in PostgreSQL
        x: number;
        y: number;
        width: number;
        height: number;
      };
      content: string;
      createdAt: Date;
      updatedAt: Date;
    }
    ```

- **Service Layer**:
  - Implement services that use repositories through dependency injection
  - Keep business logic independent of storage mechanism
  - Design for easy switching between storage implementations
  - Example:
    ```typescript
    @Injectable({
      providedIn: 'root'
    })
    class AnnotationService {
      constructor(private repository: AnnotationRepository) {}
      
      getAnnotationsForChart(chartId: string): Observable<Annotation[]> {
        return this.repository.findByChartId(chartId);
      }
      
      // Other methods...
    }
    ```

- **Data Access Abstraction**:
  - Create a storage module with provider configuration
  - Use environment configuration to determine storage implementation
  - Allow for runtime switching between implementations
  - Example:
    ```typescript
    @NgModule({
      providers: [
        {
          provide: 'StorageImplementation',
          useClass: environment.useApi ? ApiStorageService : IndexedDBStorageService
        }
      ]
    })
    export class StorageModule { }
    ```

#### 5. IndexedDB Schema Design (PostgreSQL-Compatible)

Design the IndexedDB database schema to mirror the future PostgreSQL schema:

```typescript
// IndexedDB database schema
const DB_VERSION = 1;
const DB_NAME = 'clinical-quality-db';

const OBJECT_STORES = [
  {
    name: 'users',
    keyPath: 'id',
    indexes: [
      { name: 'username', keyPath: 'username', unique: true }
    ]
  },
  {
    name: 'charts',
    keyPath: 'id',
    indexes: [
      { name: 'filename', keyPath: 'filename', unique: false }
    ]
  },
  {
    name: 'annotations',
    keyPath: 'id',
    indexes: [
      { name: 'chartId', keyPath: 'chartId', unique: false },
      { name: 'userId', keyPath: 'userId', unique: false }
    ]
  },
  {
    name: 'ai_findings',
    keyPath: 'id',
    indexes: [
      { name: 'chartId', keyPath: 'chartId', unique: false }
    ]
  },
  {
    name: 'validations',
    keyPath: 'id',
    indexes: [
      { name: 'findingId', keyPath: 'findingId', unique: false },
      { name: 'userId', keyPath: 'userId', unique: false }
    ]
  }
];
```

#### 6. Migration-Friendly Data Operations

- **UUID Generation**:
  - Use UUID v4 for all entity IDs to ensure compatibility with PostgreSQL
  - Maintain consistent ID generation across both phases

- **Timestamps**:
  - Include `createdAt` and `updatedAt` fields in all entities
  - Use consistent date formatting and timezone handling

- **Relationships**:
  - Model relationships using foreign keys similar to PostgreSQL
  - Implement join operations in the repository layer

- **Transactions**:
  - Use IndexedDB transactions for data integrity
  - Design transaction patterns that can be adapted to PostgreSQL transactions

- **Data Export/Import**:
  - Implement functionality to export data as JSON
  - Support importing data from JSON files
  - Ensure exported data format is compatible with future PostgreSQL import

#### 7. Development Phases (Phase 1)

1. **Project Setup**:
   - Initialize Angular project
   - Configure routing and core services
   - Set up development environment

2. **Storage Architecture Implementation**:
   - Design and implement repository interfaces
   - Create IndexedDB service with PostgreSQL-compatible schema
   - Implement abstract storage service layer
   - Set up dependency injection for storage implementations

3. **Dashboard Implementation**:
   - Create dashboard components
   - Implement chart listing and filtering
   - Connect to storage service

4. **Chart Viewer Implementation**:
   - Integrate PDF.js with text layer support
   - Create basic viewer layout
   - Implement page navigation
   - Implement advanced cross-page search functionality

5. **Annotation Implementation**:
   - Create annotation service
   - Implement highlighting functionality
   - Add comment/note capabilities
   - Connect to storage service for saving/retrieving annotations

6. **PDF Export Implementation**:
   - Research and integrate PDF modification libraries
   - Develop service to embed annotations in PDFs
   - Implement export and download functionality

7. **Testing and Refinement**:
   - Unit testing of components
   - Integration testing of workflows
   - Performance testing with large documents
   - UI/UX refinements based on feedback

### Phase 2: Future Migration to PostgreSQL Backend

The migration to PostgreSQL will be significantly simplified by the PostgreSQL-ready design in Phase 1. The primary changes will be:

1. **Implement API Repositories**:
   - Create API-based implementations of the repository interfaces
   - Connect to PostgreSQL through RESTful endpoints

2. **Update Provider Configuration**:
   - Switch the storage implementation from IndexedDB to API
   - No changes needed to services or components that use the repositories

3. **Data Migration**:
   - Export data from IndexedDB in a format compatible with PostgreSQL
   - Import data into PostgreSQL database
   - Validate data integrity after migration

4. **Authentication Integration** (if needed in the future):
   - Implement authentication service
   - Connect to Azure AD or other authentication provider
   - Add authorization checks to API endpoints