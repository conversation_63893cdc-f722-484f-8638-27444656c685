import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ChartReviewPageComponent } from '@features/chart-review/pages/chart-review-page/chart-review-page.component';
import { PdfViewerTestComponent } from '@features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component';

const routes: Routes = [
  {
    path: ':id', // Changed from '' to ':id' to accept a route parameter
    component: ChartReviewPageComponent
  },
  {
    path: 'pdf-test',
    component: PdfViewerTestComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ChartReviewRoutingModule { }
