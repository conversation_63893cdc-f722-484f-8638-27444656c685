import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from '@app/app.config';
import { AppComponent } from '@app/app.component';

// Configure the PDF worker source for ngx-extended-pdf-viewer (only in browser)
if (typeof window !== 'undefined') {
  console.log('Setting PDF worker source path to: /assets/pdf.worker.mjs');
  (window as any).pdfWorkerSrc = '/assets/pdf.worker.mjs';

  // Add a check to verify the worker path was set correctly
  setTimeout(() => {
    console.log('Verifying PDF worker configuration:');
    console.log('window.pdfWorkerSrc =', (window as any).pdfWorkerSrc);

    // Check if the file exists by creating a test request
    const testRequest = new XMLHttpRequest();
    testRequest.open('HEAD', '/assets/pdf.worker.mjs', true);
    testRequest.onreadystatechange = function() {
      if (this.readyState === this.DONE) {
        console.log('PDF worker file check:', this.status);
        if (this.status === 200) {
          console.log('PDF worker file exists and is accessible');
        } else {
          console.error('PDF worker file not found or not accessible!');
        }
      }
    };
    testRequest.send();
  }, 1000);
}

bootstrapApplication(AppComponent, appConfig)
  .catch((err) => console.error(err));
