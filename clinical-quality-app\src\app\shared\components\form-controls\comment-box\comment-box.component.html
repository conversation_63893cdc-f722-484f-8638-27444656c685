<!-- Comment Box Component - Based on Figma comment-box.html -->
<div class="comment-box" 
     [class.default]="currentState === 'Default'"
     [class.active]="currentState === 'Active'"
     [class.entered]="currentState === 'Entered'"
     [class.disabled]="disabled">
  <div class="row">
    <input
      type="text"
      class="comment-input"
      [value]="value"
      [placeholder]="placeholder"
      [disabled]="disabled"
      (input)="onInput($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
    />
  </div>
</div>
