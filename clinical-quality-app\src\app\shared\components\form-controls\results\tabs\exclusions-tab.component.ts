import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CalendarComponent } from '../../calendar/calendar.component';
import { NotesComponent } from '../../../notes/notes.component';
import { DropdownComponent, DropdownOption } from '../../dropdown/dropdown.component';

@Component({
  selector: 'app-exclusions-tab',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CalendarComponent,
    NotesComponent,
    DropdownComponent
  ],
  templateUrl: './exclusions-tab.component.html',
  styleUrls: ['./exclusions-tab.component.scss']
})
export class ExclusionsTabComponent implements OnInit {
  @Input() formGroup!: FormGroup;
  @Input() disabled: boolean = false;
  @Input() id: string = '';

  @Output() fieldChange = new EventEmitter<void>();

  // Exclusions reasoning options matching Figma specifications
  exclusionsReasoningOptions: DropdownOption[] = [
    { value: 'acute-inpatient-and-ed-visit', label: 'Acute inpatient and ED visit' },
    { value: 'end-stage-renal-disease', label: 'End-stage renal disease' },
    { value: 'frailty-member-81-years', label: 'Frailty: Member 81+ years as of 12/31 of the MY' },
    { value: 'lidocaine-and-epinephrine', label: 'Lidocaine and Epinephrine given to patient' },
    { value: 'medicare-member-institutional-snp', label: 'Medicare member in an Institutional SNP (I-SNP)' },
    { value: 'medicare-member-long-term-care', label: 'Medicare member living in long-term care' },
    { value: 'member-66-80-years', label: 'Member 66-80 years as of 12/31 of the MY' },
    { value: 'member-died-during-my', label: 'Member died during the MY' },
    { value: 'member-in-hospice', label: 'Member in hospice anytime during the MY' },
    { value: 'non-acute-inpatient-admission', label: 'Non-acute inpatient admission' },
    { value: 'palliative-care', label: 'Palliative Care' },
    { value: 'pregnancy', label: 'Pregnancy' },
    { value: 'other', label: 'Other' }
  ];

  constructor() {}

  ngOnInit(): void {
    // Component initialization
  }

  onFormFieldChange(): void {
    this.fieldChange.emit();
  }
}
